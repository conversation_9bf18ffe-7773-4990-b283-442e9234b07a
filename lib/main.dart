import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:camera/camera.dart';
import 'package:provider/provider.dart';
import 'data/services/admob_service.dart';
import 'data/services/persistent_ad_manager.dart';
import 'data/services/app_open_ad_manager.dart';
import 'data/services/rewarded_interstitial_ad_manager.dart';
import 'data/services/pro_status_manager.dart';
import 'data/services/razorpay_service.dart';
import 'data/services/onboarding_service.dart';
import 'firebase_options.dart';
import 'presentation/providers/app_state_provider.dart';
import 'presentation/pages/splash_screen.dart';
import 'presentation/pages/camera_page.dart';
import 'presentation/pages/gallery_page.dart';
import 'presentation/pages/map_view_page.dart';
import 'presentation/pages/file_settings_page.dart';
import 'presentation/pages/settings_page.dart';
import 'presentation/pages/photo_detail_page.dart';
import 'presentation/pages/category_manager_page.dart';
import 'presentation/pages/onboarding_screen.dart';
import 'domain/entities/photo_entity.dart';
import 'generated/l10n/app_localizations.dart';



List<CameraDescription> cameras = [];

Future<void> main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Firebase first
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  
  // Initialize AdMob after Firebase
  await AdMobService.initialize();
  
  // Initialize Persistent Ad Manager
  PersistentAdManager().initialize();

  // Initialize App Open Ad Manager
  AppOpenAdManager().initialize();

  // Initialize Rewarded Interstitial Ad Manager
  RewardedInterstitialAdManager().initialize();

  // Initialize Pro Status Manager
  await ProStatusManager().initialize();
  // Initialize Razorpay Service
  RazorpayService().initialize();

  // Initialize Onboarding Service
  await OnboardingService().initialize();

  // Get available cameras
  try {
    cameras = await availableCameras();
    if (kDebugMode) {
      print('Found ${cameras.length} cameras');
    }
    for (var camera in cameras) {
      if (kDebugMode) {
        print('Camera: ${camera.name}, ${camera.lensDirection}');
      }
    }
  } catch (e) {
    if (kDebugMode) {
      print('Error initializing cameras: $e');
    }
    cameras = [];
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: ChangeNotifierProvider(
        create: (_) => AppProvider(),
        child: Consumer<AppProvider>(
          builder: (context, appProvider, _) {
            final templateSettings = appProvider.currentTemplateSettings;
            final primaryColor = templateSettings['primaryColor'] as Color? ?? Colors.teal;

            return MaterialApp(
              debugShowCheckedModeBanner: false,
              theme: ThemeData(
                primaryColor: primaryColor,
                colorScheme: ColorScheme.fromSeed(
                  seedColor: primaryColor,
                ),
                appBarTheme: AppBarTheme(
                  backgroundColor: primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
              // Localization support
              localizationsDelegates: const [
                AppLocalizations.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              supportedLocales: AppLocalizations.supportedLocales,
              locale: appProvider.currentLocale,
              home: const SplashScreen(),
              routes: {
                '/gallery': (context) => const GalleryPage(),
                '/camera': (context) => const CameraPage(),
                '/map': (context) => const MapViewPage(),
                '/file_settings': (context) => const FileSettingsPage(),
                '/settings': (context) => const SettingsPage(),
                '/measurements': (context) => const CategoryManagerPage(),
                '/onboarding': (context) => const OnboardingScreen(),
              },
              onGenerateRoute: (settings) {
                if (settings.name == '/photo_detail') {
                  final photo = settings.arguments as PhotoEntity;
                  return MaterialPageRoute(
                    builder: (context) => PhotoDetailPage(photo: photo),
                  );
                }
                return null;
              },
            );
          },
        ),
      ),
    );
  }
}




