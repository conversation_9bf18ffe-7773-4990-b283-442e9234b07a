import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:camera/camera.dart';
import 'package:provider/provider.dart';
import 'data/services/admob_service.dart';
import 'data/services/persistent_ad_manager.dart';
import 'data/services/app_open_ad_manager.dart';
import 'data/services/rewarded_interstitial_ad_manager.dart';
import 'data/services/pro_status_manager.dart';
import 'data/services/razorpay_service.dart';
import 'data/services/onboarding_service.dart';
import 'firebase_options.dart';
import 'presentation/providers/app_state_provider.dart';
import 'presentation/pages/splash_screen.dart';
import 'presentation/pages/camera_page.dart';
import 'presentation/pages/gallery_page.dart';
import 'presentation/pages/map_view_page.dart';
import 'presentation/pages/file_settings_page.dart';
import 'presentation/pages/settings_page.dart';
import 'presentation/pages/photo_detail_page.dart';
import 'presentation/pages/category_manager_page.dart';
import 'presentation/pages/onboarding_screen.dart';
import 'domain/entities/photo_entity.dart';
import 'generated/l10n/app_localizations.dart';



List<CameraDescription> cameras = [];

Future<void> main() async {
  try {
    // Ensure Flutter is initialized
    WidgetsFlutterBinding.ensureInitialized();



    // Initialize other services with try-catch blocks
    try {
      await AdMobService.initialize();
    } catch (e) {
      print('Error initializing AdMob: $e');
    }

    try {
      PersistentAdManager().initialize();
    } catch (e) {
      print('Error initializing PersistentAdManager: $e');
    }

    try {
      AppOpenAdManager().initialize();
    } catch (e) {
      print('Error initializing AppOpenAdManager: $e');
    }

    try {
      RewardedInterstitialAdManager().initialize();
    } catch (e) {
      print('Error initializing RewardedInterstitialAdManager: $e');
    }

    try {
      await ProStatusManager().initialize();
    } catch (e) {
      print('Error initializing ProStatusManager: $e');
    }

    try {
      RazorpayService().initialize();
    } catch (e) {
      print('Error initializing RazorpayService: $e');
    }

    try {
      await OnboardingService().initialize();
    } catch (e) {
      print('Error initializing OnboardingService: $e');
    }
    // Initialize Firebase with better error handling
    try {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      print('Firebase initialized successfully');
    } catch (e) {
      print('Firebase initialization failed: $e');
      // Continue without Firebase for now
    }

    runApp(
      MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (context) => AppProvider()),
        ],
        child: const MyApp(),
      ),
    );
  } catch (e, stackTrace) {
    print('Fatal error in main: $e');
    print('Stack trace: $stackTrace');
    // Run a minimal app that displays the error
    runApp(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: Text('App initialization error: $e'),
          ),
        ),
      ),
    );
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    try {
      final appProvider = Provider.of<AppProvider>(context);
      
      return MaterialApp(
        title: 'GPS Map Camera',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          primarySwatch: Colors.teal,
          visualDensity: VisualDensity.adaptivePlatformDensity,
        ),
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: AppLocalizations.supportedLocales,
        locale: appProvider.currentLocale,
        home: const SplashScreen(),
        routes: {
          '/gallery': (context) => const GalleryPage(),
          '/camera': (context) => const CameraPage(),
          '/map': (context) => const MapViewPage(),
          '/file_settings': (context) => const FileSettingsPage(),
          '/settings': (context) => const SettingsPage(),
          '/measurements': (context) => const CategoryManagerPage(),
          '/onboarding': (context) => const OnboardingScreen(),
        },
        onGenerateRoute: (settings) {
          if (settings.name == '/photo_detail') {
            final photo = settings.arguments as PhotoEntity;
            return MaterialPageRoute(
              builder: (context) => PhotoDetailPage(photo: photo),
            );
          }
          return null;
        },
      );
    } catch (e, stackTrace) {
      print('Error in MyApp.build: $e');
      print('Stack trace: $stackTrace');
      return MaterialApp(
        home: Scaffold(
          body: Center(
            child: Text('Error building app: $e'),
          ),
        ),
      );
    }
  }
}




