// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'GPS Map Camera';

  @override
  String get camera => 'Camera';

  @override
  String get gallery => 'Gallery';

  @override
  String get map => 'Map';

  @override
  String get settings => 'Settings';

  @override
  String get fieldMeasurement => 'Field Measurement';

  @override
  String get distanceMeasurement => 'Distance Measurement';

  @override
  String get locationMarker => 'Location Marker';

  @override
  String get savedMeasurements => 'Saved Measurements';

  @override
  String get preferences => 'Preferences';

  @override
  String get unitFormats => 'Unit Formats';

  @override
  String get unitSettings => 'Unit Settings';

  @override
  String get others => 'Others';

  @override
  String get language => 'Language';

  @override
  String get selectLanguage => 'Select your preferred language';

  @override
  String get areaUnit => 'Area Unit';

  @override
  String get areaUnitDescription => 'Unit for area measurements';

  @override
  String get distanceUnit => 'Distance Unit';

  @override
  String get distanceUnitDescription => 'Unit for distance measurements';

  @override
  String get perimeterUnit => 'Perimeter Unit';

  @override
  String get perimeterUnitDescription => 'Unit for perimeter measurements';

  @override
  String get howToUse => 'How to Use';

  @override
  String get howToUseDescription => 'Learn how to use the app features';

  @override
  String get feedback => 'Feedback';

  @override
  String get feedbackDescription => 'Send us your feedback and suggestions';

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get delete => 'Delete';

  @override
  String get edit => 'Edit';

  @override
  String get clear => 'Clear';

  @override
  String get points => 'Points';

  @override
  String get area => 'Area';

  @override
  String get perimeter => 'Perimeter';

  @override
  String get measurements => 'Measurements';

  @override
  String get distance => 'Distance';

  @override
  String get totalDistance => 'Total Distance';

  @override
  String get segments => 'Segments';

  @override
  String get pointToPointDistances => 'Point-to-Point Distances';

  @override
  String get tapMethod => 'Tap';

  @override
  String get walkMethod => 'Walk';

  @override
  String get hybridMethod => 'Hybrid';

  @override
  String get satelliteView => 'Satellite';

  @override
  String get normalView => 'Normal';

  @override
  String get settingsSaved => 'Settings saved successfully';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get loading => 'Loading...';

  @override
  String get noData => 'No data available';

  @override
  String get squareMeters => 'Square Meters';

  @override
  String get squareFeet => 'Square Feet';

  @override
  String get acres => 'Acres';

  @override
  String get hectares => 'Hectares';

  @override
  String get squareKilometers => 'Square Kilometers';

  @override
  String get squareMiles => 'Square Miles';

  @override
  String get meters => 'Meters';

  @override
  String get kilometers => 'Kilometers';

  @override
  String get feet => 'Feet';

  @override
  String get miles => 'Miles';

  @override
  String get yards => 'Yards';

  @override
  String get centimeters => 'Centimeters';

  @override
  String get cameraFeatures => '📷 Camera Features';

  @override
  String get cameraFeaturesContent =>
      '• Tap the camera button to take photos with GPS location\n• Photos automatically include location coordinates\n• Timestamp and address are embedded in the image\n• Use the grid lines for better composition\n• Switch between front and back cameras';

  @override
  String get mapViewFeatures => '🗺️ Map View';

  @override
  String get mapViewFeaturesContent =>
      '• View all your photos on an interactive map\n• Tap on markers to see photo details\n• Switch between normal and satellite view\n• Zoom in/out to explore different areas\n• Long press to add custom markers';

  @override
  String get measurementTools => '📏 Measurement Tools';

  @override
  String get measurementToolsContent =>
      '• Field Measurement: Measure area of land/fields\n• Distance Measurement: Measure distances between points\n• Location Marker: Add and save specific locations\n• Choose between tap, walk, or hybrid input methods\n• Results are saved automatically';

  @override
  String get galleryFeatures => '🖼️ Gallery';

  @override
  String get galleryFeaturesContent =>
      '• Browse all your captured photos\n• View photos in grid or list format\n• Tap to view full-size images\n• Share photos with location data\n• Delete unwanted photos';

  @override
  String get settingsFeatures => '⚙️ Settings';

  @override
  String get settingsFeaturesContent =>
      '• Change app language preferences\n• Set measurement units (meters, feet, etc.)\n• Customize area and distance units\n• Access help and feedback options\n• Save your preferences automatically';

  @override
  String get tipsAndTricks => '💡 Tips & Tricks';

  @override
  String get tipsAndTricksContent =>
      '• Enable location services for accurate GPS data\n• Take photos in good lighting for better quality\n• Use measurement tools for professional surveying\n• Regular backup of important photos\n• Check app permissions in device settings';

  @override
  String get needMoreHelp => 'Need More Help?';

  @override
  String get needMoreHelpContent =>
      'If you need additional assistance, please use the Feedback option in Settings to contact our support team.';

  @override
  String get weValueYourFeedback => 'We Value Your Feedback';

  @override
  String get feedbackIntro =>
      'Help us improve the app by sharing your thoughts, suggestions, or reporting issues.';

  @override
  String get name => 'Name';

  @override
  String get email => 'Email';

  @override
  String get feedbackType => 'Feedback Type';

  @override
  String get subject => 'Subject';

  @override
  String get message => 'Message';

  @override
  String get sendFeedback => 'Send Feedback';

  @override
  String get general => 'General';

  @override
  String get bugReport => 'Bug Report';

  @override
  String get featureRequest => 'Feature Request';

  @override
  String get improvementSuggestion => 'Improvement Suggestion';

  @override
  String get technicalIssue => 'Technical Issue';

  @override
  String get userExperience => 'User Experience';

  @override
  String get pleaseEnterName => 'Please enter your name';

  @override
  String get pleaseEnterEmail => 'Please enter your email';

  @override
  String get pleaseEnterValidEmail => 'Please enter a valid email';

  @override
  String get pleaseEnterSubject => 'Please enter a subject';

  @override
  String get pleaseEnterMessage => 'Please enter your message';

  @override
  String get messageTooShort => 'Message should be at least 10 characters long';

  @override
  String get emailAppOpened => 'Email app opened. Thank you for your feedback!';

  @override
  String get emailError =>
      'Error: Could not open email app. Please email us <NAME_EMAIL>';

  @override
  String get alternativeContact => 'Alternative Contact';

  @override
  String get alternativeContactText => 'You can also email us directly at:';

  @override
  String get distanceSummary => 'Distance Summary';

  @override
  String get fieldSummary => 'Field Summary';

  @override
  String get saveMeasurement => 'Save Measurement';

  @override
  String get measurementName => 'Measurement Name';

  @override
  String get notes => 'Notes';

  @override
  String get notesOptional => 'Notes (Optional)';

  @override
  String get measurementSaved => 'Measurement saved successfully';

  @override
  String get errorSavingMeasurement => 'Error saving measurement';

  @override
  String get deleteConfirmation => 'Delete Confirmation';

  @override
  String get deleteConfirmationMessage =>
      'Are you sure you want to delete this measurement?';

  @override
  String get type => 'Type';

  @override
  String get date => 'Date';

  @override
  String get location => 'Location';

  @override
  String get timestamp => 'Timestamp';

  @override
  String get startRecording => 'Start Recording';

  @override
  String get stopRecording => 'Stop Recording';

  @override
  String get recording => 'Recording...';

  @override
  String get gpsAccuracy => 'GPS Accuracy';
}
