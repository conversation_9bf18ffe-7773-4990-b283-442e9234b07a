// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appTitle => 'GPS地图相机';

  @override
  String get camera => '相机';

  @override
  String get gallery => '图库';

  @override
  String get map => '地图';

  @override
  String get settings => '设置';

  @override
  String get fieldMeasurement => '田地测量';

  @override
  String get distanceMeasurement => '距离测量';

  @override
  String get locationMarker => '位置标记';

  @override
  String get savedMeasurements => '已保存的测量';

  @override
  String get preferences => '偏好设置';

  @override
  String get unitFormats => '单位格式';

  @override
  String get unitSettings => 'Unit Settings';

  @override
  String get others => '其他';

  @override
  String get language => '语言';

  @override
  String get selectLanguage => '选择您的首选语言';

  @override
  String get areaUnit => '面积单位';

  @override
  String get areaUnitDescription => '面积测量单位';

  @override
  String get distanceUnit => '距离单位';

  @override
  String get distanceUnitDescription => '距离测量单位';

  @override
  String get perimeterUnit => '周长单位';

  @override
  String get perimeterUnitDescription => '周长测量单位';

  @override
  String get howToUse => '使用方法';

  @override
  String get howToUseDescription => '学习如何使用应用功能';

  @override
  String get feedback => '反馈';

  @override
  String get feedbackDescription => '向我们发送您的反馈和建议';

  @override
  String get save => '保存';

  @override
  String get cancel => '取消';

  @override
  String get ok => '确定';

  @override
  String get yes => '是';

  @override
  String get no => '否';

  @override
  String get delete => '删除';

  @override
  String get edit => '编辑';

  @override
  String get clear => '清除';

  @override
  String get points => '点';

  @override
  String get area => '面积';

  @override
  String get perimeter => '周长';

  @override
  String get measurements => 'Measurements';

  @override
  String get distance => '距离';

  @override
  String get totalDistance => '总距离';

  @override
  String get segments => '段';

  @override
  String get pointToPointDistances => '点到点距离';

  @override
  String get tapMethod => '点击';

  @override
  String get walkMethod => '步行';

  @override
  String get hybridMethod => '混合';

  @override
  String get satelliteView => '卫星';

  @override
  String get normalView => '普通';

  @override
  String get settingsSaved => '设置保存成功';

  @override
  String get error => '错误';

  @override
  String get success => '成功';

  @override
  String get loading => '加载中...';

  @override
  String get noData => '无可用数据';

  @override
  String get squareMeters => '平方米';

  @override
  String get squareFeet => '平方英尺';

  @override
  String get acres => '英亩';

  @override
  String get hectares => '公顷';

  @override
  String get squareKilometers => '平方公里';

  @override
  String get squareMiles => '平方英里';

  @override
  String get meters => '米';

  @override
  String get kilometers => '公里';

  @override
  String get feet => '英尺';

  @override
  String get miles => '英里';

  @override
  String get yards => '码';

  @override
  String get centimeters => '厘米';

  @override
  String get cameraFeatures => '📷 Camera Features';

  @override
  String get cameraFeaturesContent =>
      '• Tap the camera button to take photos with GPS location\n• Photos automatically include location coordinates\n• Timestamp and address are embedded in the image\n• Use the grid lines for better composition\n• Switch between front and back cameras';

  @override
  String get mapViewFeatures => '🗺️ Map View';

  @override
  String get mapViewFeaturesContent =>
      '• View all your photos on an interactive map\n• Tap on markers to see photo details\n• Switch between normal and satellite view\n• Zoom in/out to explore different areas\n• Long press to add custom markers';

  @override
  String get measurementTools => '📏 Measurement Tools';

  @override
  String get measurementToolsContent =>
      '• Field Measurement: Measure area of land/fields\n• Distance Measurement: Measure distances between points\n• Location Marker: Add and save specific locations\n• Choose between tap, walk, or hybrid input methods\n• Results are saved automatically';

  @override
  String get galleryFeatures => '🖼️ Gallery';

  @override
  String get galleryFeaturesContent =>
      '• Browse all your captured photos\n• View photos in grid or list format\n• Tap to view full-size images\n• Share photos with location data\n• Delete unwanted photos';

  @override
  String get settingsFeatures => '⚙️ Settings';

  @override
  String get settingsFeaturesContent =>
      '• Change app language preferences\n• Set measurement units (meters, feet, etc.)\n• Customize area and distance units\n• Access help and feedback options\n• Save your preferences automatically';

  @override
  String get tipsAndTricks => '💡 Tips & Tricks';

  @override
  String get tipsAndTricksContent =>
      '• Enable location services for accurate GPS data\n• Take photos in good lighting for better quality\n• Use measurement tools for professional surveying\n• Regular backup of important photos\n• Check app permissions in device settings';

  @override
  String get needMoreHelp => 'Need More Help?';

  @override
  String get needMoreHelpContent =>
      'If you need additional assistance, please use the Feedback option in Settings to contact our support team.';

  @override
  String get weValueYourFeedback => 'We Value Your Feedback';

  @override
  String get feedbackIntro =>
      'Help us improve the app by sharing your thoughts, suggestions, or reporting issues.';

  @override
  String get name => '姓名';

  @override
  String get email => '邮箱';

  @override
  String get feedbackType => '反馈类型';

  @override
  String get subject => '主题';

  @override
  String get message => '消息';

  @override
  String get sendFeedback => '发送反馈';

  @override
  String get general => '一般';

  @override
  String get bugReport => '错误报告';

  @override
  String get featureRequest => '功能请求';

  @override
  String get improvementSuggestion => '改进建议';

  @override
  String get technicalIssue => '技术问题';

  @override
  String get userExperience => '用户体验';

  @override
  String get pleaseEnterName => '请输入您的姓名';

  @override
  String get pleaseEnterEmail => '请输入您的邮箱';

  @override
  String get pleaseEnterValidEmail => '请输入有效的邮箱';

  @override
  String get pleaseEnterSubject => '请输入主题';

  @override
  String get pleaseEnterMessage => '请输入您的消息';

  @override
  String get messageTooShort => '消息应至少包含10个字符';

  @override
  String get emailAppOpened => 'Email app opened. Thank you for your feedback!';

  @override
  String get emailError =>
      'Error: Could not open email app. Please email us <NAME_EMAIL>';

  @override
  String get alternativeContact => 'Alternative Contact';

  @override
  String get alternativeContactText => 'You can also email us directly at:';

  @override
  String get distanceSummary => '距离摘要';

  @override
  String get fieldSummary => '田地摘要';

  @override
  String get saveMeasurement => '保存测量';

  @override
  String get measurementName => '测量名称';

  @override
  String get notes => '备注';

  @override
  String get notesOptional => '备注（可选）';

  @override
  String get measurementSaved => '测量保存成功';

  @override
  String get errorSavingMeasurement => '保存测量时出错';

  @override
  String get deleteConfirmation => '删除确认';

  @override
  String get deleteConfirmationMessage => '您确定要删除此测量吗？';

  @override
  String get type => '类型';

  @override
  String get date => '日期';

  @override
  String get location => '位置';

  @override
  String get timestamp => '时间戳';

  @override
  String get startRecording => '开始录制';

  @override
  String get stopRecording => '停止录制';

  @override
  String get recording => '录制中...';

  @override
  String get gpsAccuracy => 'GPS精度';
}
