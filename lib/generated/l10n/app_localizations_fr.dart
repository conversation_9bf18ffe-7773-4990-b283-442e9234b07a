// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get appTitle => 'Caméra GPS Carte';

  @override
  String get camera => 'Caméra';

  @override
  String get gallery => 'Galerie';

  @override
  String get map => 'Carte';

  @override
  String get settings => 'Paramètres';

  @override
  String get fieldMeasurement => 'Mesure de Terrain';

  @override
  String get distanceMeasurement => 'Mesure de Distance';

  @override
  String get locationMarker => 'Marqueur de Localisation';

  @override
  String get savedMeasurements => 'Mesures Sauvegardées';

  @override
  String get preferences => 'Préférences';

  @override
  String get unitFormats => 'Formats d\'Unité';

  @override
  String get unitSettings => 'Unit Settings';

  @override
  String get others => 'Autres';

  @override
  String get language => 'Langue';

  @override
  String get selectLanguage => 'Sélectionnez votre langue préférée';

  @override
  String get areaUnit => 'Unité de Surface';

  @override
  String get areaUnitDescription => 'Unité pour les mesures de surface';

  @override
  String get distanceUnit => 'Unité de Distance';

  @override
  String get distanceUnitDescription => 'Unité pour les mesures de distance';

  @override
  String get perimeterUnit => 'Unité de Périmètre';

  @override
  String get perimeterUnitDescription => 'Unité pour les mesures de périmètre';

  @override
  String get howToUse => 'Comment Utiliser';

  @override
  String get howToUseDescription =>
      'Apprenez à utiliser les fonctionnalités de l\'application';

  @override
  String get feedback => 'Commentaires';

  @override
  String get feedbackDescription =>
      'Envoyez-nous vos commentaires et suggestions';

  @override
  String get save => 'Sauvegarder';

  @override
  String get cancel => 'Annuler';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Oui';

  @override
  String get no => 'Non';

  @override
  String get delete => 'Supprimer';

  @override
  String get edit => 'Modifier';

  @override
  String get clear => 'Effacer';

  @override
  String get points => 'Points';

  @override
  String get area => 'Surface';

  @override
  String get perimeter => 'Périmètre';

  @override
  String get measurements => 'Measurements';

  @override
  String get distance => 'Distance';

  @override
  String get totalDistance => 'Distance Totale';

  @override
  String get segments => 'Segments';

  @override
  String get pointToPointDistances => 'Distances Point à Point';

  @override
  String get tapMethod => 'Toucher';

  @override
  String get walkMethod => 'Marcher';

  @override
  String get hybridMethod => 'Hybride';

  @override
  String get satelliteView => 'Satellite';

  @override
  String get normalView => 'Normal';

  @override
  String get settingsSaved => 'Paramètres sauvegardés avec succès';

  @override
  String get error => 'Erreur';

  @override
  String get success => 'Succès';

  @override
  String get loading => 'Chargement...';

  @override
  String get noData => 'Aucune donnée disponible';

  @override
  String get squareMeters => 'Mètres Carrés';

  @override
  String get squareFeet => 'Pieds Carrés';

  @override
  String get acres => 'Acres';

  @override
  String get hectares => 'Hectares';

  @override
  String get squareKilometers => 'Kilomètres Carrés';

  @override
  String get squareMiles => 'Miles Carrés';

  @override
  String get meters => 'Mètres';

  @override
  String get kilometers => 'Kilomètres';

  @override
  String get feet => 'Pieds';

  @override
  String get miles => 'Miles';

  @override
  String get yards => 'Yards';

  @override
  String get centimeters => 'Centimètres';

  @override
  String get cameraFeatures => '📷 Fonctionnalités Caméra';

  @override
  String get cameraFeaturesContent =>
      '• Appuyez sur le bouton caméra pour prendre des photos avec localisation GPS\n• Les photos incluent automatiquement les coordonnées de localisation\n• L\'horodatage et l\'adresse sont intégrés dans l\'image\n• Utilisez les lignes de grille pour une meilleure composition\n• Basculez entre les caméras avant et arrière';

  @override
  String get mapViewFeatures => '🗺️ Vue Carte';

  @override
  String get mapViewFeaturesContent =>
      '• Visualisez toutes vos photos sur une carte interactive\n• Appuyez sur les marqueurs pour voir les détails des photos\n• Basculez entre vue normale et satellite\n• Zoomez pour explorer différentes zones\n• Appui long pour ajouter des marqueurs personnalisés';

  @override
  String get measurementTools => '📏 Outils de Mesure';

  @override
  String get measurementToolsContent =>
      '• Mesure de Terrain: Mesurez la surface des terrains/champs\n• Mesure de Distance: Mesurez les distances entre points\n• Marqueur de Localisation: Ajoutez et sauvegardez des emplacements spécifiques\n• Choisissez entre les méthodes d\'entrée tactile, marche ou hybride\n• Les résultats sont sauvegardés automatiquement';

  @override
  String get galleryFeatures => '🖼️ Galerie';

  @override
  String get galleryFeaturesContent =>
      '• Parcourez toutes vos photos capturées\n• Visualisez les photos en format grille ou liste\n• Appuyez pour voir les images en taille réelle\n• Partagez des photos avec données de localisation\n• Supprimez les photos indésirables';

  @override
  String get settingsFeatures => '⚙️ Paramètres';

  @override
  String get settingsFeaturesContent =>
      '• Changez les préférences de langue de l\'application\n• Définissez les unités de mesure (mètres, pieds, etc.)\n• Personnalisez les unités de surface et distance\n• Accédez aux options d\'aide et commentaires\n• Sauvegardez vos préférences automatiquement';

  @override
  String get tipsAndTricks => '💡 Conseils et Astuces';

  @override
  String get tipsAndTricksContent =>
      '• Activez les services de localisation pour des données GPS précises\n• Prenez des photos avec un bon éclairage pour une meilleure qualité\n• Utilisez les outils de mesure pour l\'arpentage professionnel\n• Sauvegarde régulière des photos importantes\n• Vérifiez les autorisations d\'application dans les paramètres de l\'appareil';

  @override
  String get needMoreHelp => 'Besoin de Plus d\'Aide?';

  @override
  String get needMoreHelpContent =>
      'Si vous avez besoin d\'assistance supplémentaire, veuillez utiliser l\'option Commentaires dans Paramètres pour contacter notre équipe de support.';

  @override
  String get weValueYourFeedback => 'Nous Valorisons Vos Commentaires';

  @override
  String get feedbackIntro =>
      'Aidez-nous à améliorer l\'application en partageant vos pensées, suggestions ou en signalant des problèmes.';

  @override
  String get name => 'Nom';

  @override
  String get email => 'Email';

  @override
  String get feedbackType => 'Type de Commentaire';

  @override
  String get subject => 'Sujet';

  @override
  String get message => 'Message';

  @override
  String get sendFeedback => 'Envoyer Commentaires';

  @override
  String get general => 'Général';

  @override
  String get bugReport => 'Rapport de Bug';

  @override
  String get featureRequest => 'Demande de Fonctionnalité';

  @override
  String get improvementSuggestion => 'Suggestion d\'Amélioration';

  @override
  String get technicalIssue => 'Problème Technique';

  @override
  String get userExperience => 'Expérience Utilisateur';

  @override
  String get pleaseEnterName => 'Veuillez entrer votre nom';

  @override
  String get pleaseEnterEmail => 'Veuillez entrer votre email';

  @override
  String get pleaseEnterValidEmail => 'Veuillez entrer un email valide';

  @override
  String get pleaseEnterSubject => 'Veuillez entrer un sujet';

  @override
  String get pleaseEnterMessage => 'Veuillez entrer votre message';

  @override
  String get messageTooShort =>
      'Le message doit contenir au moins 10 caractères';

  @override
  String get emailAppOpened =>
      'Application email ouverte. Merci pour vos commentaires!';

  @override
  String get emailError =>
      'Erreur: Impossible d\'ouvrir l\'application email. Veuillez nous envoyer un email directement à <EMAIL>';

  @override
  String get alternativeContact => 'Contact Alternatif';

  @override
  String get alternativeContactText =>
      'Vous pouvez aussi nous envoyer un email directement à:';

  @override
  String get distanceSummary => 'Résumé Distance';

  @override
  String get fieldSummary => 'Résumé Terrain';

  @override
  String get saveMeasurement => 'Sauvegarder Mesure';

  @override
  String get measurementName => 'Nom de Mesure';

  @override
  String get notes => 'Notes';

  @override
  String get notesOptional => 'Notes (Optionnel)';

  @override
  String get measurementSaved => 'Mesure sauvegardée avec succès';

  @override
  String get errorSavingMeasurement =>
      'Erreur lors de la sauvegarde de la mesure';

  @override
  String get deleteConfirmation => 'Confirmation de Suppression';

  @override
  String get deleteConfirmationMessage =>
      'Êtes-vous sûr de vouloir supprimer cette mesure?';

  @override
  String get type => 'Type';

  @override
  String get date => 'Date';

  @override
  String get location => 'Localisation';

  @override
  String get timestamp => 'Horodatage';

  @override
  String get startRecording => 'Commencer l\'Enregistrement';

  @override
  String get stopRecording => 'Arrêter l\'Enregistrement';

  @override
  String get recording => 'Enregistrement...';

  @override
  String get gpsAccuracy => 'Précision GPS';
}
