// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Spanish Castilian (`es`).
class AppLocalizationsEs extends AppLocalizations {
  AppLocalizationsEs([String locale = 'es']) : super(locale);

  @override
  String get appTitle => 'Cámara GPS Mapa';

  @override
  String get camera => 'Cámara';

  @override
  String get gallery => 'Galería';

  @override
  String get map => 'Mapa';

  @override
  String get settings => 'Configuración';

  @override
  String get fieldMeasurement => 'Medición de Campo';

  @override
  String get distanceMeasurement => 'Medición de Distancia';

  @override
  String get locationMarker => 'Marcador de Ubicación';

  @override
  String get savedMeasurements => 'Mediciones Guardadas';

  @override
  String get preferences => 'Preferencias';

  @override
  String get unitFormats => 'Formatos de Unidad';

  @override
  String get unitSettings => 'Unit Settings';

  @override
  String get others => 'Otros';

  @override
  String get language => 'Idioma';

  @override
  String get selectLanguage => 'Selecciona tu idioma preferido';

  @override
  String get areaUnit => 'Unidad de Área';

  @override
  String get areaUnitDescription => 'Unidad para mediciones de área';

  @override
  String get distanceUnit => 'Unidad de Distancia';

  @override
  String get distanceUnitDescription => 'Unidad para mediciones de distancia';

  @override
  String get perimeterUnit => 'Unidad de Perímetro';

  @override
  String get perimeterUnitDescription => 'Unidad para mediciones de perímetro';

  @override
  String get howToUse => 'Cómo Usar';

  @override
  String get howToUseDescription =>
      'Aprende a usar las funciones de la aplicación';

  @override
  String get feedback => 'Comentarios';

  @override
  String get feedbackDescription => 'Envíanos tus comentarios y sugerencias';

  @override
  String get save => 'Guardar';

  @override
  String get cancel => 'Cancelar';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Sí';

  @override
  String get no => 'No';

  @override
  String get delete => 'Eliminar';

  @override
  String get edit => 'Editar';

  @override
  String get clear => 'Limpiar';

  @override
  String get points => 'Puntos';

  @override
  String get area => 'Área';

  @override
  String get perimeter => 'Perímetro';

  @override
  String get measurements => 'Measurements';

  @override
  String get distance => 'Distancia';

  @override
  String get totalDistance => 'Distancia Total';

  @override
  String get segments => 'Segmentos';

  @override
  String get pointToPointDistances => 'Distancias Punto a Punto';

  @override
  String get tapMethod => 'Tocar';

  @override
  String get walkMethod => 'Caminar';

  @override
  String get hybridMethod => 'Híbrido';

  @override
  String get satelliteView => 'Satélite';

  @override
  String get normalView => 'Normal';

  @override
  String get settingsSaved => 'Configuración guardada exitosamente';

  @override
  String get error => 'Error';

  @override
  String get success => 'Éxito';

  @override
  String get loading => 'Cargando...';

  @override
  String get noData => 'No hay datos disponibles';

  @override
  String get squareMeters => 'Metros Cuadrados';

  @override
  String get squareFeet => 'Pies Cuadrados';

  @override
  String get acres => 'Acres';

  @override
  String get hectares => 'Hectáreas';

  @override
  String get squareKilometers => 'Kilómetros Cuadrados';

  @override
  String get squareMiles => 'Millas Cuadradas';

  @override
  String get meters => 'Metros';

  @override
  String get kilometers => 'Kilómetros';

  @override
  String get feet => 'Pies';

  @override
  String get miles => 'Millas';

  @override
  String get yards => 'Yardas';

  @override
  String get centimeters => 'Centímetros';

  @override
  String get cameraFeatures => '📷 Funciones de Cámara';

  @override
  String get cameraFeaturesContent =>
      '• Toca el botón de cámara para tomar fotos con ubicación GPS\n• Las fotos incluyen automáticamente coordenadas de ubicación\n• La marca de tiempo y dirección se incrustan en la imagen\n• Usa las líneas de cuadrícula para mejor composición\n• Cambia entre cámaras frontal y trasera';

  @override
  String get mapViewFeatures => '🗺️ Vista de Mapa';

  @override
  String get mapViewFeaturesContent =>
      '• Ve todas tus fotos en un mapa interactivo\n• Toca los marcadores para ver detalles de fotos\n• Cambia entre vista normal y satélite\n• Acerca/aleja para explorar diferentes áreas\n• Mantén presionado para agregar marcadores personalizados';

  @override
  String get measurementTools => '📏 Herramientas de Medición';

  @override
  String get measurementToolsContent =>
      '• Medición de Campo: Mide el área de terrenos/campos\n• Medición de Distancia: Mide distancias entre puntos\n• Marcador de Ubicación: Agrega y guarda ubicaciones específicas\n• Elige entre métodos de entrada táctil, caminar o híbrido\n• Los resultados se guardan automáticamente';

  @override
  String get galleryFeatures => '🖼️ Galería';

  @override
  String get galleryFeaturesContent =>
      '• Navega por todas tus fotos capturadas\n• Ve fotos en formato de cuadrícula o lista\n• Toca para ver imágenes a tamaño completo\n• Comparte fotos con datos de ubicación\n• Elimina fotos no deseadas';

  @override
  String get settingsFeatures => '⚙️ Configuración';

  @override
  String get settingsFeaturesContent =>
      '• Cambia las preferencias de idioma de la aplicación\n• Establece unidades de medición (metros, pies, etc.)\n• Personaliza unidades de área y distancia\n• Accede a opciones de ayuda y comentarios\n• Guarda tus preferencias automáticamente';

  @override
  String get tipsAndTricks => '💡 Consejos y Trucos';

  @override
  String get tipsAndTricksContent =>
      '• Habilita servicios de ubicación para datos GPS precisos\n• Toma fotos con buena iluminación para mejor calidad\n• Usa herramientas de medición para topografía profesional\n• Respaldo regular de fotos importantes\n• Verifica permisos de aplicación en configuración del dispositivo';

  @override
  String get needMoreHelp => '¿Necesitas Más Ayuda?';

  @override
  String get needMoreHelpContent =>
      'Si necesitas asistencia adicional, por favor usa la opción de Comentarios en Configuración para contactar a nuestro equipo de soporte.';

  @override
  String get weValueYourFeedback => 'Valoramos Tus Comentarios';

  @override
  String get feedbackIntro =>
      'Ayúdanos a mejorar la aplicación compartiendo tus pensamientos, sugerencias o reportando problemas.';

  @override
  String get name => 'Nombre';

  @override
  String get email => 'Correo Electrónico';

  @override
  String get feedbackType => 'Tipo de Comentario';

  @override
  String get subject => 'Asunto';

  @override
  String get message => 'Mensaje';

  @override
  String get sendFeedback => 'Enviar Comentarios';

  @override
  String get general => 'General';

  @override
  String get bugReport => 'Reporte de Error';

  @override
  String get featureRequest => 'Solicitud de Función';

  @override
  String get improvementSuggestion => 'Sugerencia de Mejora';

  @override
  String get technicalIssue => 'Problema Técnico';

  @override
  String get userExperience => 'Experiencia de Usuario';

  @override
  String get pleaseEnterName => 'Por favor ingresa tu nombre';

  @override
  String get pleaseEnterEmail => 'Por favor ingresa tu correo electrónico';

  @override
  String get pleaseEnterValidEmail =>
      'Por favor ingresa un correo electrónico válido';

  @override
  String get pleaseEnterSubject => 'Por favor ingresa un asunto';

  @override
  String get pleaseEnterMessage => 'Por favor ingresa tu mensaje';

  @override
  String get messageTooShort => 'El mensaje debe tener al menos 10 caracteres';

  @override
  String get emailAppOpened =>
      'Aplicación de correo abierta. ¡Gracias por tus comentarios!';

  @override
  String get emailError =>
      'Error: No se pudo abrir la aplicación de correo. Por favor envíanos un correo <NAME_EMAIL>';

  @override
  String get alternativeContact => 'Contacto Alternativo';

  @override
  String get alternativeContactText =>
      'También puedes enviarnos un correo directamente a:';

  @override
  String get distanceSummary => 'Resumen de Distancia';

  @override
  String get fieldSummary => 'Resumen de Campo';

  @override
  String get saveMeasurement => 'Guardar Medición';

  @override
  String get measurementName => 'Nombre de Medición';

  @override
  String get notes => 'Notas';

  @override
  String get notesOptional => 'Notas (Opcional)';

  @override
  String get measurementSaved => 'Medición guardada exitosamente';

  @override
  String get errorSavingMeasurement => 'Error al guardar medición';

  @override
  String get deleteConfirmation => 'Confirmación de Eliminación';

  @override
  String get deleteConfirmationMessage =>
      '¿Estás seguro de que quieres eliminar esta medición?';

  @override
  String get type => 'Tipo';

  @override
  String get date => 'Fecha';

  @override
  String get location => 'Ubicación';

  @override
  String get timestamp => 'Marca de Tiempo';

  @override
  String get startRecording => 'Iniciar Grabación';

  @override
  String get stopRecording => 'Detener Grabación';

  @override
  String get recording => 'Grabando...';

  @override
  String get gpsAccuracy => 'Precisión GPS';
}
