// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for German (`de`).
class AppLocalizationsDe extends AppLocalizations {
  AppLocalizationsDe([String locale = 'de']) : super(locale);

  @override
  String get appTitle => 'GPS Karten Kamera';

  @override
  String get camera => 'Kamera';

  @override
  String get gallery => 'Galerie';

  @override
  String get map => 'Karte';

  @override
  String get settings => 'Einstellungen';

  @override
  String get fieldMeasurement => 'Feldmessung';

  @override
  String get distanceMeasurement => 'Entfernungsmessung';

  @override
  String get locationMarker => 'Standortmarkierung';

  @override
  String get savedMeasurements => 'Gespeicherte Messungen';

  @override
  String get preferences => 'Einstellungen';

  @override
  String get unitFormats => 'Einheitenformate';

  @override
  String get unitSettings => 'Unit Settings';

  @override
  String get others => 'Andere';

  @override
  String get language => 'Sprache';

  @override
  String get selectLanguage => 'Wählen Sie Ihre bevorzugte Sprache';

  @override
  String get areaUnit => 'Flächeneinheit';

  @override
  String get areaUnitDescription => 'Einheit für Flächenmessungen';

  @override
  String get distanceUnit => 'Entfernungseinheit';

  @override
  String get distanceUnitDescription => 'Einheit für Entfernungsmessungen';

  @override
  String get perimeterUnit => 'Umfangseinheit';

  @override
  String get perimeterUnitDescription => 'Einheit für Umfangsmessungen';

  @override
  String get howToUse => 'Verwendung';

  @override
  String get howToUseDescription =>
      'Lernen Sie die App-Funktionen zu verwenden';

  @override
  String get feedback => 'Feedback';

  @override
  String get feedbackDescription =>
      'Senden Sie uns Ihr Feedback und Ihre Vorschläge';

  @override
  String get save => 'Speichern';

  @override
  String get cancel => 'Abbrechen';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Ja';

  @override
  String get no => 'Nein';

  @override
  String get delete => 'Löschen';

  @override
  String get edit => 'Bearbeiten';

  @override
  String get clear => 'Löschen';

  @override
  String get points => 'Punkte';

  @override
  String get area => 'Fläche';

  @override
  String get perimeter => 'Umfang';

  @override
  String get measurements => 'Measurements';

  @override
  String get distance => 'Entfernung';

  @override
  String get totalDistance => 'Gesamtentfernung';

  @override
  String get segments => 'Segmente';

  @override
  String get pointToPointDistances => 'Punkt-zu-Punkt Entfernungen';

  @override
  String get tapMethod => 'Tippen';

  @override
  String get walkMethod => 'Gehen';

  @override
  String get hybridMethod => 'Hybrid';

  @override
  String get satelliteView => 'Satellit';

  @override
  String get normalView => 'Normal';

  @override
  String get settingsSaved => 'Einstellungen erfolgreich gespeichert';

  @override
  String get error => 'Fehler';

  @override
  String get success => 'Erfolg';

  @override
  String get loading => 'Lädt...';

  @override
  String get noData => 'Keine Daten verfügbar';

  @override
  String get squareMeters => 'Quadratmeter';

  @override
  String get squareFeet => 'Quadratfuß';

  @override
  String get acres => 'Acres';

  @override
  String get hectares => 'Hektar';

  @override
  String get squareKilometers => 'Quadratkilometer';

  @override
  String get squareMiles => 'Quadratmeilen';

  @override
  String get meters => 'Meter';

  @override
  String get kilometers => 'Kilometer';

  @override
  String get feet => 'Fuß';

  @override
  String get miles => 'Meilen';

  @override
  String get yards => 'Yards';

  @override
  String get centimeters => 'Zentimeter';

  @override
  String get cameraFeatures => '📷 Camera Features';

  @override
  String get cameraFeaturesContent =>
      '• Tap the camera button to take photos with GPS location\n• Photos automatically include location coordinates\n• Timestamp and address are embedded in the image\n• Use the grid lines for better composition\n• Switch between front and back cameras';

  @override
  String get mapViewFeatures => '🗺️ Map View';

  @override
  String get mapViewFeaturesContent =>
      '• View all your photos on an interactive map\n• Tap on markers to see photo details\n• Switch between normal and satellite view\n• Zoom in/out to explore different areas\n• Long press to add custom markers';

  @override
  String get measurementTools => '📏 Measurement Tools';

  @override
  String get measurementToolsContent =>
      '• Field Measurement: Measure area of land/fields\n• Distance Measurement: Measure distances between points\n• Location Marker: Add and save specific locations\n• Choose between tap, walk, or hybrid input methods\n• Results are saved automatically';

  @override
  String get galleryFeatures => '🖼️ Gallery';

  @override
  String get galleryFeaturesContent =>
      '• Browse all your captured photos\n• View photos in grid or list format\n• Tap to view full-size images\n• Share photos with location data\n• Delete unwanted photos';

  @override
  String get settingsFeatures => '⚙️ Settings';

  @override
  String get settingsFeaturesContent =>
      '• Change app language preferences\n• Set measurement units (meters, feet, etc.)\n• Customize area and distance units\n• Access help and feedback options\n• Save your preferences automatically';

  @override
  String get tipsAndTricks => '💡 Tips & Tricks';

  @override
  String get tipsAndTricksContent =>
      '• Enable location services for accurate GPS data\n• Take photos in good lighting for better quality\n• Use measurement tools for professional surveying\n• Regular backup of important photos\n• Check app permissions in device settings';

  @override
  String get needMoreHelp => 'Need More Help?';

  @override
  String get needMoreHelpContent =>
      'If you need additional assistance, please use the Feedback option in Settings to contact our support team.';

  @override
  String get weValueYourFeedback => 'We Value Your Feedback';

  @override
  String get feedbackIntro =>
      'Help us improve the app by sharing your thoughts, suggestions, or reporting issues.';

  @override
  String get name => 'Name';

  @override
  String get email => 'E-Mail';

  @override
  String get feedbackType => 'Feedback-Typ';

  @override
  String get subject => 'Betreff';

  @override
  String get message => 'Nachricht';

  @override
  String get sendFeedback => 'Feedback senden';

  @override
  String get general => 'Allgemein';

  @override
  String get bugReport => 'Fehlerbericht';

  @override
  String get featureRequest => 'Funktionsanfrage';

  @override
  String get improvementSuggestion => 'Verbesserungsvorschlag';

  @override
  String get technicalIssue => 'Technisches Problem';

  @override
  String get userExperience => 'Benutzererfahrung';

  @override
  String get pleaseEnterName => 'Bitte geben Sie Ihren Namen ein';

  @override
  String get pleaseEnterEmail => 'Bitte geben Sie Ihre E-Mail ein';

  @override
  String get pleaseEnterValidEmail => 'Bitte geben Sie eine gültige E-Mail ein';

  @override
  String get pleaseEnterSubject => 'Bitte geben Sie einen Betreff ein';

  @override
  String get pleaseEnterMessage => 'Bitte geben Sie Ihre Nachricht ein';

  @override
  String get messageTooShort =>
      'Die Nachricht sollte mindestens 10 Zeichen lang sein';

  @override
  String get emailAppOpened => 'Email app opened. Thank you for your feedback!';

  @override
  String get emailError =>
      'Error: Could not open email app. Please email us <NAME_EMAIL>';

  @override
  String get alternativeContact => 'Alternative Contact';

  @override
  String get alternativeContactText => 'You can also email us directly at:';

  @override
  String get distanceSummary => 'Entfernungszusammenfassung';

  @override
  String get fieldSummary => 'Feldzusammenfassung';

  @override
  String get saveMeasurement => 'Messung speichern';

  @override
  String get measurementName => 'Messungsname';

  @override
  String get notes => 'Notizen';

  @override
  String get notesOptional => 'Notizen (Optional)';

  @override
  String get measurementSaved => 'Messung erfolgreich gespeichert';

  @override
  String get errorSavingMeasurement => 'Fehler beim Speichern der Messung';

  @override
  String get deleteConfirmation => 'Löschbestätigung';

  @override
  String get deleteConfirmationMessage =>
      'Sind Sie sicher, dass Sie diese Messung löschen möchten?';

  @override
  String get type => 'Typ';

  @override
  String get date => 'Datum';

  @override
  String get location => 'Standort';

  @override
  String get timestamp => 'Zeitstempel';

  @override
  String get startRecording => 'Aufnahme starten';

  @override
  String get stopRecording => 'Aufnahme stoppen';

  @override
  String get recording => 'Aufnahme...';

  @override
  String get gpsAccuracy => 'GPS-Genauigkeit';
}
