// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Tamil (`ta`).
class AppLocalizationsTa extends AppLocalizations {
  AppLocalizationsTa([String locale = 'ta']) : super(locale);

  @override
  String get appTitle => 'ஜிபிஎஸ் வரைபட கேமரா';

  @override
  String get camera => 'கேமரா';

  @override
  String get gallery => 'கேலரி';

  @override
  String get map => 'வரைபடம்';

  @override
  String get settings => 'அமைப்புகள்';

  @override
  String get fieldMeasurement => 'வயல் அளவீடு';

  @override
  String get distanceMeasurement => 'தூர அளவீடு';

  @override
  String get locationMarker => 'இடம் குறிப்பான்';

  @override
  String get savedMeasurements => 'சேமிக்கப்பட்ட அளவீடுகள்';

  @override
  String get preferences => 'விருப்பத்தேர்வுகள்';

  @override
  String get unitFormats => 'அலகு வடிவங்கள்';

  @override
  String get unitSettings => 'Unit Settings';

  @override
  String get others => 'மற்றவை';

  @override
  String get language => 'மொழி';

  @override
  String get selectLanguage => 'உங்கள் விருப்பமான மொழியைத் தேர்ந்தெடுக்கவும்';

  @override
  String get areaUnit => 'பரப்பளவு அலகு';

  @override
  String get areaUnitDescription => 'பரப்பளவு அளவீடுகளுக்கான அலகு';

  @override
  String get distanceUnit => 'தூர அலகு';

  @override
  String get distanceUnitDescription => 'தூர அளவீடுகளுக்கான அலகு';

  @override
  String get perimeterUnit => 'சுற்றளவு அலகு';

  @override
  String get perimeterUnitDescription => 'சுற்றளவு அளவீடுகளுக்கான அலகு';

  @override
  String get howToUse => 'எப்படி பயன்படுத்துவது';

  @override
  String get howToUseDescription =>
      'ஆப்ஸ் அம்சங்களை எப்படி பயன்படுத்துவது என்பதை அறியுங்கள்';

  @override
  String get feedback => 'கருத்து';

  @override
  String get feedbackDescription =>
      'உங்கள் கருத்துகளையும் பரிந்துரைகளையும் எங்களுக்கு அனுப்புங்கள்';

  @override
  String get save => 'சேமி';

  @override
  String get cancel => 'ரத்து';

  @override
  String get ok => 'சரி';

  @override
  String get yes => 'ஆம்';

  @override
  String get no => 'இல்லை';

  @override
  String get delete => 'நீக்கு';

  @override
  String get edit => 'திருத்து';

  @override
  String get clear => 'அழி';

  @override
  String get points => 'புள்ளிகள்';

  @override
  String get area => 'பரப்பளவு';

  @override
  String get perimeter => 'சுற்றளவு';

  @override
  String get measurements => 'Measurements';

  @override
  String get distance => 'தூரம்';

  @override
  String get totalDistance => 'மொத்த தூரம்';

  @override
  String get segments => 'பிரிவுகள்';

  @override
  String get pointToPointDistances => 'புள்ளி முதல் புள்ளி தூரங்கள்';

  @override
  String get tapMethod => 'தட்டு';

  @override
  String get walkMethod => 'நடை';

  @override
  String get hybridMethod => 'கலப்பு';

  @override
  String get satelliteView => 'செயற்கைக்கோள்';

  @override
  String get normalView => 'சாதாரண';

  @override
  String get settingsSaved => 'அமைப்புகள் வெற்றிகரமாக சேமிக்கப்பட்டன';

  @override
  String get error => 'பிழை';

  @override
  String get success => 'வெற்றி';

  @override
  String get loading => 'ஏற்றுகிறது...';

  @override
  String get noData => 'தரவு இல்லை';

  @override
  String get squareMeters => 'சதுர மீட்டர்';

  @override
  String get squareFeet => 'சதுர அடி';

  @override
  String get acres => 'ஏக்கர்';

  @override
  String get hectares => 'ஹெக்டேர்';

  @override
  String get squareKilometers => 'சதுர கிலோமீட்டர்';

  @override
  String get squareMiles => 'சதுர மைல்';

  @override
  String get meters => 'மீட்டர்';

  @override
  String get kilometers => 'கிலோமீட்டர்';

  @override
  String get feet => 'அடி';

  @override
  String get miles => 'மைல்';

  @override
  String get yards => 'யார்டு';

  @override
  String get centimeters => 'சென்டிமீட்டர்';

  @override
  String get cameraFeatures => '📷 கேமரா அம்சங்கள்';

  @override
  String get cameraFeaturesContent =>
      '• ஜிபிஎஸ் இடத்துடன் புகைப்படங்களை எடுக்க கேமரா பொத்தானை அழுத்தவும்\n• புகைப்படங்கள் தானாகவே இட ஒருங்கிணைப்புகளை உள்ளடக்கும்\n• நேர முத்திரை மற்றும் முகவரி படத்தில் உட்பொதிக்கப்பட்டுள்ளது\n• சிறந்த கலவைக்கு கட்டம் கோடுகளைப் பயன்படுத்தவும்\n• முன் மற்றும் பின் கேமராக்களுக்கு இடையில் மாறவும்';

  @override
  String get mapViewFeatures => '🗺️ வரைபட காட்சி';

  @override
  String get mapViewFeaturesContent =>
      '• ஊடாடும் வரைபடத்தில் உங்கள் அனைத்து புகைப்படங்களையும் பார்க்கவும்\n• புகைப்பட விவரங்களைப் பார்க்க குறிப்பான்களை அழுத்தவும்\n• சாதாரண மற்றும் செயற்கைக்கோள் காட்சிக்கு இடையில் மாறவும்\n• வெவ்வேறு பகுதிகளை ஆராய பெரிதாக்கவும்/சிறிதாக்கவும்\n• தனிப்பயன் குறிப்பான்களைச் சேர்க்க நீண்ட நேரம் அழுத்தவும்';

  @override
  String get measurementTools => '📏 அளவீட்டு கருவிகள்';

  @override
  String get measurementToolsContent =>
      '• வயல் அளவீடு: நிலம்/வயல்களின் பரப்பளவை அளக்கவும்\n• தூர அளவீடு: புள்ளிகளுக்கு இடையிலான தூரங்களை அளக்கவும்\n• இட குறிப்பான்: குறிப்பிட்ட இடங்களைச் சேர்த்து சேமிக்கவும்\n• தட்டு, நடை அல்லது கலப்பு உள்ளீட்டு முறைகளுக்கு இடையில் தேர்வு செய்யவும்\n• முடிவுகள் தானாகவே சேமிக்கப்படும்';

  @override
  String get galleryFeatures => '🖼️ கேலரி';

  @override
  String get galleryFeaturesContent =>
      '• உங்கள் எடுக்கப்பட்ட அனைத்து புகைப்படங்களையும் உலாவவும்\n• கட்டம் அல்லது பட்டியல் வடிவத்தில் புகைப்படங்களைப் பார்க்கவும்\n• முழு அளவிலான படங்களைப் பார்க்க அழுத்தவும்\n• இட தரவுடன் புகைப்படங்களைப் பகிரவும்\n• தேவையற்ற புகைப்படங்களை நீக்கவும்';

  @override
  String get settingsFeatures => '⚙️ அமைப்புகள்';

  @override
  String get settingsFeaturesContent =>
      '• ஆப்ஸ் மொழி விருப்பத்தேர்வுகளை மாற்றவும்\n• அளவீட்டு அலகுகளை அமைக்கவும் (மீட்டர், அடி, போன்றவை)\n• பரப்பளவு மற்றும் தூர அலகுகளை தனிப்பயனாக்கவும்\n• உதவி மற்றும் கருத்து விருப்பங்களை அணுகவும்\n• உங்கள் விருப்பத்தேர்வுகளை தானாகவே சேமிக்கவும்';

  @override
  String get tipsAndTricks => '💡 குறிப்புகள் மற்றும் தந்திரங்கள்';

  @override
  String get tipsAndTricksContent =>
      '• துல்லியமான ஜிபிஎஸ் தரவுக்கு இட சேவைகளை இயக்கவும்\n• சிறந்த தரத்திற்கு நல்ல வெளிச்சத்தில் புகைப்படங்களை எடுக்கவும்\n• தொழில்முறை கணக்கெடுப்புக்கு அளவீட்டு கருவிகளைப் பயன்படுத்தவும்\n• முக்கியமான புகைப்படங்களின் வழக்கமான காப்புப்பிரதி\n• சாதன அமைப்புகளில் ஆப்ஸ் அனுமதிகளைச் சரிபார்க்கவும்';

  @override
  String get needMoreHelp => 'மேலும் உதவி தேவையா?';

  @override
  String get needMoreHelpContent =>
      'உங்களுக்கு கூடுதல் உதவி தேவைப்பட்டால், எங்கள் ஆதரவு குழுவைத் தொடர்பு கொள்ள அமைப்புகளில் கருத்து விருப்பத்தைப் பயன்படுத்தவும்.';

  @override
  String get weValueYourFeedback => 'உங்கள் கருத்தை நாங்கள் மதிக்கிறோம்';

  @override
  String get feedbackIntro =>
      'உங்கள் எண்ணங்கள், பரிந்துரைகள் அல்லது சிக்கல்களைப் புகாரளிப்பதன் மூலம் ஆப்ஸை மேம்படுத்த எங்களுக்கு உதவுங்கள்.';

  @override
  String get name => 'பெயர்';

  @override
  String get email => 'மின்னஞ்சல்';

  @override
  String get feedbackType => 'கருத்து வகை';

  @override
  String get subject => 'பொருள்';

  @override
  String get message => 'செய்தி';

  @override
  String get sendFeedback => 'கருத்தை அனுப்பு';

  @override
  String get general => 'பொது';

  @override
  String get bugReport => 'பிழை அறிக்கை';

  @override
  String get featureRequest => 'அம்ச கோரிக்கை';

  @override
  String get improvementSuggestion => 'மேம்பாட்டு பரிந்துரை';

  @override
  String get technicalIssue => 'தொழில்நுட்ப சிக்கல்';

  @override
  String get userExperience => 'பயனர் அனுபவம்';

  @override
  String get pleaseEnterName => 'தயவுசெய்து உங்கள் பெயரை உள்ளிடவும்';

  @override
  String get pleaseEnterEmail => 'தயவுசெய்து உங்கள் மின்னஞ்சலை உள்ளிடவும்';

  @override
  String get pleaseEnterValidEmail => 'தயவுசெய்து சரியான மின்னஞ்சலை உள்ளிடவும்';

  @override
  String get pleaseEnterSubject => 'தயவுசெய்து ஒரு பொருளை உள்ளிடவும்';

  @override
  String get pleaseEnterMessage => 'தயவுசெய்து உங்கள் செய்தியை உள்ளிடவும்';

  @override
  String get messageTooShort =>
      'செய்தி குறைந்தது 10 எழுத்துகள் நீளமாக இருக்க வேண்டும்';

  @override
  String get emailAppOpened =>
      'மின்னஞ்சல் ஆப்ஸ் திறக்கப்பட்டது. உங்கள் கருத்துக்கு நன்றி!';

  @override
  String get emailError =>
      'பிழை: மின்னஞ்சல் ஆப்ஸைத் திறக்க முடியவில்லை. தயவுசெய்து எங்களுக்கு நேரடியாக மின்னஞ்சல் அனுப்பவும் <EMAIL>';

  @override
  String get alternativeContact => 'மாற்று தொடர்பு';

  @override
  String get alternativeContactText =>
      'நீங்கள் எங்களுக்கு நேரடியாகவும் மின்னஞ்சல் அனுப்பலாம்:';

  @override
  String get distanceSummary => 'தூர சுருக்கம்';

  @override
  String get fieldSummary => 'வயல் சுருக்கம்';

  @override
  String get saveMeasurement => 'அளவீட்டைச் சேமி';

  @override
  String get measurementName => 'அளவீட்டு பெயர்';

  @override
  String get notes => 'குறிப்புகள்';

  @override
  String get notesOptional => 'குறிப்புகள் (விருப்பமானது)';

  @override
  String get measurementSaved => 'அளவீடு வெற்றிகரமாக சேமிக்கப்பட்டது';

  @override
  String get errorSavingMeasurement => 'அளவீட்டைச் சேமிப்பதில் பிழை';

  @override
  String get deleteConfirmation => 'நீக்குதல் உறுதிப்படுத்தல்';

  @override
  String get deleteConfirmationMessage =>
      'இந்த அளவீட்டை நீக்க விரும்புகிறீர்களா?';

  @override
  String get type => 'வகை';

  @override
  String get date => 'தேதி';

  @override
  String get location => 'இடம்';

  @override
  String get timestamp => 'நேர முத்திரை';

  @override
  String get startRecording => 'பதிவைத் தொடங்கு';

  @override
  String get stopRecording => 'பதிவை நிறுத்து';

  @override
  String get recording => 'பதிவு செய்கிறது...';

  @override
  String get gpsAccuracy => 'ஜிபிஎஸ் துல்லியம்';
}
