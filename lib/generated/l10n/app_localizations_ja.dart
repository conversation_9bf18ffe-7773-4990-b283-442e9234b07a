// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Japanese (`ja`).
class AppLocalizationsJa extends AppLocalizations {
  AppLocalizationsJa([String locale = 'ja']) : super(locale);

  @override
  String get appTitle => 'GPSマップカメラ';

  @override
  String get camera => 'カメラ';

  @override
  String get gallery => 'ギャラリー';

  @override
  String get map => 'マップ';

  @override
  String get settings => '設定';

  @override
  String get fieldMeasurement => 'フィールド測定';

  @override
  String get distanceMeasurement => '距離測定';

  @override
  String get locationMarker => '位置マーカー';

  @override
  String get savedMeasurements => '保存された測定';

  @override
  String get preferences => '設定';

  @override
  String get unitFormats => '単位形式';

  @override
  String get unitSettings => 'Unit Settings';

  @override
  String get others => 'その他';

  @override
  String get language => '言語';

  @override
  String get selectLanguage => 'お好みの言語を選択してください';

  @override
  String get areaUnit => '面積単位';

  @override
  String get areaUnitDescription => '面積測定の単位';

  @override
  String get distanceUnit => '距離単位';

  @override
  String get distanceUnitDescription => '距離測定の単位';

  @override
  String get perimeterUnit => '周囲単位';

  @override
  String get perimeterUnitDescription => '周囲測定の単位';

  @override
  String get howToUse => '使用方法';

  @override
  String get howToUseDescription => 'アプリの機能の使用方法を学ぶ';

  @override
  String get feedback => 'フィードバック';

  @override
  String get feedbackDescription => 'フィードバックと提案をお送りください';

  @override
  String get save => '保存';

  @override
  String get cancel => 'キャンセル';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'はい';

  @override
  String get no => 'いいえ';

  @override
  String get delete => '削除';

  @override
  String get edit => '編集';

  @override
  String get clear => 'クリア';

  @override
  String get points => 'ポイント';

  @override
  String get area => '面積';

  @override
  String get perimeter => '周囲';

  @override
  String get measurements => 'Measurements';

  @override
  String get distance => '距離';

  @override
  String get totalDistance => '総距離';

  @override
  String get segments => 'セグメント';

  @override
  String get pointToPointDistances => 'ポイント間距離';

  @override
  String get tapMethod => 'タップ';

  @override
  String get walkMethod => '歩行';

  @override
  String get hybridMethod => 'ハイブリッド';

  @override
  String get satelliteView => '衛星';

  @override
  String get normalView => '通常';

  @override
  String get settingsSaved => '設定が正常に保存されました';

  @override
  String get error => 'エラー';

  @override
  String get success => '成功';

  @override
  String get loading => '読み込み中...';

  @override
  String get noData => '利用可能なデータがありません';

  @override
  String get squareMeters => '平方メートル';

  @override
  String get squareFeet => '平方フィート';

  @override
  String get acres => 'エーカー';

  @override
  String get hectares => 'ヘクタール';

  @override
  String get squareKilometers => '平方キロメートル';

  @override
  String get squareMiles => '平方マイル';

  @override
  String get meters => 'メートル';

  @override
  String get kilometers => 'キロメートル';

  @override
  String get feet => 'フィート';

  @override
  String get miles => 'マイル';

  @override
  String get yards => 'ヤード';

  @override
  String get centimeters => 'センチメートル';

  @override
  String get cameraFeatures => '📷 Camera Features';

  @override
  String get cameraFeaturesContent =>
      '• Tap the camera button to take photos with GPS location\n• Photos automatically include location coordinates\n• Timestamp and address are embedded in the image\n• Use the grid lines for better composition\n• Switch between front and back cameras';

  @override
  String get mapViewFeatures => '🗺️ Map View';

  @override
  String get mapViewFeaturesContent =>
      '• View all your photos on an interactive map\n• Tap on markers to see photo details\n• Switch between normal and satellite view\n• Zoom in/out to explore different areas\n• Long press to add custom markers';

  @override
  String get measurementTools => '📏 Measurement Tools';

  @override
  String get measurementToolsContent =>
      '• Field Measurement: Measure area of land/fields\n• Distance Measurement: Measure distances between points\n• Location Marker: Add and save specific locations\n• Choose between tap, walk, or hybrid input methods\n• Results are saved automatically';

  @override
  String get galleryFeatures => '🖼️ Gallery';

  @override
  String get galleryFeaturesContent =>
      '• Browse all your captured photos\n• View photos in grid or list format\n• Tap to view full-size images\n• Share photos with location data\n• Delete unwanted photos';

  @override
  String get settingsFeatures => '⚙️ Settings';

  @override
  String get settingsFeaturesContent =>
      '• Change app language preferences\n• Set measurement units (meters, feet, etc.)\n• Customize area and distance units\n• Access help and feedback options\n• Save your preferences automatically';

  @override
  String get tipsAndTricks => '💡 Tips & Tricks';

  @override
  String get tipsAndTricksContent =>
      '• Enable location services for accurate GPS data\n• Take photos in good lighting for better quality\n• Use measurement tools for professional surveying\n• Regular backup of important photos\n• Check app permissions in device settings';

  @override
  String get needMoreHelp => 'Need More Help?';

  @override
  String get needMoreHelpContent =>
      'If you need additional assistance, please use the Feedback option in Settings to contact our support team.';

  @override
  String get weValueYourFeedback => 'We Value Your Feedback';

  @override
  String get feedbackIntro =>
      'Help us improve the app by sharing your thoughts, suggestions, or reporting issues.';

  @override
  String get name => '名前';

  @override
  String get email => 'メール';

  @override
  String get feedbackType => 'フィードバックタイプ';

  @override
  String get subject => '件名';

  @override
  String get message => 'メッセージ';

  @override
  String get sendFeedback => 'フィードバックを送信';

  @override
  String get general => '一般';

  @override
  String get bugReport => 'バグレポート';

  @override
  String get featureRequest => '機能リクエスト';

  @override
  String get improvementSuggestion => '改善提案';

  @override
  String get technicalIssue => '技術的問題';

  @override
  String get userExperience => 'ユーザーエクスペリエンス';

  @override
  String get pleaseEnterName => 'お名前を入力してください';

  @override
  String get pleaseEnterEmail => 'メールアドレスを入力してください';

  @override
  String get pleaseEnterValidEmail => '有効なメールアドレスを入力してください';

  @override
  String get pleaseEnterSubject => '件名を入力してください';

  @override
  String get pleaseEnterMessage => 'メッセージを入力してください';

  @override
  String get messageTooShort => 'メッセージは少なくとも10文字以上である必要があります';

  @override
  String get emailAppOpened => 'Email app opened. Thank you for your feedback!';

  @override
  String get emailError =>
      'Error: Could not open email app. Please email us <NAME_EMAIL>';

  @override
  String get alternativeContact => 'Alternative Contact';

  @override
  String get alternativeContactText => 'You can also email us directly at:';

  @override
  String get distanceSummary => '距離の概要';

  @override
  String get fieldSummary => 'フィールドの概要';

  @override
  String get saveMeasurement => '測定を保存';

  @override
  String get measurementName => '測定名';

  @override
  String get notes => 'メモ';

  @override
  String get notesOptional => 'メモ（オプション）';

  @override
  String get measurementSaved => '測定が正常に保存されました';

  @override
  String get errorSavingMeasurement => '測定の保存中にエラーが発生しました';

  @override
  String get deleteConfirmation => '削除の確認';

  @override
  String get deleteConfirmationMessage => 'この測定を削除してもよろしいですか？';

  @override
  String get type => 'タイプ';

  @override
  String get date => '日付';

  @override
  String get location => '場所';

  @override
  String get timestamp => 'タイムスタンプ';

  @override
  String get startRecording => '記録開始';

  @override
  String get stopRecording => '記録停止';

  @override
  String get recording => '記録中...';

  @override
  String get gpsAccuracy => 'GPS精度';
}
