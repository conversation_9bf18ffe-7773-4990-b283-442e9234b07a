// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Hindi (`hi`).
class AppLocalizationsHi extends AppLocalizations {
  AppLocalizationsHi([String locale = 'hi']) : super(locale);

  @override
  String get appTitle => 'जीपीएस मैप कैमरा';

  @override
  String get camera => 'कैमरा';

  @override
  String get gallery => 'गैलरी';

  @override
  String get map => 'मानचित्र';

  @override
  String get settings => 'सेटिंग्स';

  @override
  String get fieldMeasurement => 'क्षेत्र माप';

  @override
  String get distanceMeasurement => 'दूरी माप';

  @override
  String get locationMarker => 'स्थान मार्कर';

  @override
  String get savedMeasurements => 'सहेजे गए माप';

  @override
  String get preferences => 'प्राथमिकताएं';

  @override
  String get unitFormats => 'इकाई प्रारूप';

  @override
  String get unitSettings => 'Unit Settings';

  @override
  String get others => 'अन्य';

  @override
  String get language => 'भाषा';

  @override
  String get selectLanguage => 'अपनी पसंदीदा भाषा चुनें';

  @override
  String get areaUnit => 'क्षेत्रफल इकाई';

  @override
  String get areaUnitDescription => 'क्षेत्रफल माप के लिए इकाई';

  @override
  String get distanceUnit => 'दूरी इकाई';

  @override
  String get distanceUnitDescription => 'दूरी माप के लिए इकाई';

  @override
  String get perimeterUnit => 'परिधि इकाई';

  @override
  String get perimeterUnitDescription => 'परिधि माप के लिए इकाई';

  @override
  String get howToUse => 'उपयोग कैसे करें';

  @override
  String get howToUseDescription => 'ऐप सुविधाओं का उपयोग करना सीखें';

  @override
  String get feedback => 'फीडबैक';

  @override
  String get feedbackDescription => 'हमें अपनी प्रतिक्रिया और सुझाव भेजें';

  @override
  String get save => 'सहेजें';

  @override
  String get cancel => 'रद्द करें';

  @override
  String get ok => 'ठीक है';

  @override
  String get yes => 'हां';

  @override
  String get no => 'नहीं';

  @override
  String get delete => 'हटाएं';

  @override
  String get edit => 'संपादित करें';

  @override
  String get clear => 'साफ़ करें';

  @override
  String get points => 'बिंदु';

  @override
  String get area => 'क्षेत्रफल';

  @override
  String get perimeter => 'परिधि';

  @override
  String get measurements => 'Measurements';

  @override
  String get distance => 'दूरी';

  @override
  String get totalDistance => 'कुल दूरी';

  @override
  String get segments => 'खंड';

  @override
  String get pointToPointDistances => 'बिंदु से बिंदु दूरी';

  @override
  String get tapMethod => 'टैप';

  @override
  String get walkMethod => 'चलना';

  @override
  String get hybridMethod => 'हाइब्रिड';

  @override
  String get satelliteView => 'उपग्रह';

  @override
  String get normalView => 'सामान्य';

  @override
  String get settingsSaved => 'सेटिंग्स सफलतापूर्वक सहेजी गईं';

  @override
  String get error => 'त्रुटि';

  @override
  String get success => 'सफलता';

  @override
  String get loading => 'लोड हो रहा है...';

  @override
  String get noData => 'कोई डेटा उपलब्ध नहीं';

  @override
  String get squareMeters => 'वर्ग मीटर';

  @override
  String get squareFeet => 'वर्ग फुट';

  @override
  String get acres => 'एकड़';

  @override
  String get hectares => 'हेक्टेयर';

  @override
  String get squareKilometers => 'वर्ग किलोमीटर';

  @override
  String get squareMiles => 'वर्ग मील';

  @override
  String get meters => 'मीटर';

  @override
  String get kilometers => 'किलोमीटर';

  @override
  String get feet => 'फुट';

  @override
  String get miles => 'मील';

  @override
  String get yards => 'गज';

  @override
  String get centimeters => 'सेंटीमीटर';

  @override
  String get cameraFeatures => '📷 कैमरा सुविधाएं';

  @override
  String get cameraFeaturesContent =>
      '• जीपीएस स्थान के साथ फोटो लेने के लिए कैमरा बटन दबाएं\n• फोटो में स्वचालित रूप से स्थान निर्देशांक शामिल होते हैं\n• टाइमस्टैम्प और पता छवि में एम्बेड किया जाता है\n• बेहतर संरचना के लिए ग्रिड लाइनों का उपयोग करें\n• फ्रंट और बैक कैमरा के बीच स्विच करें';

  @override
  String get mapViewFeatures => '🗺️ मानचित्र दृश्य';

  @override
  String get mapViewFeaturesContent =>
      '• इंटरैक्टिव मानचित्र पर अपनी सभी तस्वीरें देखें\n• फोटो विवरण देखने के लिए मार्करों पर टैप करें\n• सामान्य और उपग्रह दृश्य के बीच स्विच करें\n• विभिन्न क्षेत्रों का पता लगाने के लिए ज़ूम इन/आउट करें\n• कस्टम मार्कर जोड़ने के लिए लंबे समय तक दबाएं';

  @override
  String get measurementTools => '📏 माप उपकरण';

  @override
  String get measurementToolsContent =>
      '• क्षेत्र माप: भूमि/खेतों का क्षेत्रफल मापें\n• दूरी माप: बिंदुओं के बीच की दूरी मापें\n• स्थान मार्कर: विशिष्ट स्थानों को जोड़ें और सहेजें\n• टैप, वॉक या हाइब्रिड इनपुट विधियों के बीच चुनें\n• परिणाम स्वचालित रूप से सहेजे जाते हैं';

  @override
  String get galleryFeatures => '🖼️ गैलरी';

  @override
  String get galleryFeaturesContent =>
      '• अपनी सभी कैप्चर की गई तस्वीरों को ब्राउज़ करें\n• ग्रिड या सूची प्रारूप में फोटो देखें\n• पूर्ण आकार की छवियां देखने के लिए टैप करें\n• स्थान डेटा के साथ फोटो साझा करें\n• अनचाहे फोटो हटाएं';

  @override
  String get settingsFeatures => '⚙️ सेटिंग्स';

  @override
  String get settingsFeaturesContent =>
      '• ऐप भाषा प्राथमिकताएं बदलें\n• माप इकाइयां सेट करें (मीटर, फुट, आदि)\n• क्षेत्रफल और दूरी इकाइयों को कस्टमाइज़ करें\n• सहायता और फीडबैक विकल्पों तक पहुंचें\n• अपनी प्राथमिकताएं स्वचालित रूप से सहेजें';

  @override
  String get tipsAndTricks => '💡 सुझाव और तरकीबें';

  @override
  String get tipsAndTricksContent =>
      '• सटीक जीपीएस डेटा के लिए स्थान सेवाएं सक्षम करें\n• बेहतर गुणवत्ता के लिए अच्छी रोशनी में फोटो लें\n• पेशेवर सर्वेक्षण के लिए माप उपकरणों का उपयोग करें\n• महत्वपूर्ण फोटो का नियमित बैकअप\n• डिवाइस सेटिंग्स में ऐप अनुमतियां जांचें';

  @override
  String get needMoreHelp => 'और मदद चाहिए?';

  @override
  String get needMoreHelpContent =>
      'यदि आपको अतिरिक्त सहायता की आवश्यकता है, तो कृपया हमारी सहायता टीम से संपर्क करने के लिए सेटिंग्स में फीडबैक विकल्प का उपयोग करें।';

  @override
  String get weValueYourFeedback => 'हम आपकी प्रतिक्रिया को महत्व देते हैं';

  @override
  String get feedbackIntro =>
      'अपने विचार, सुझाव या समस्याओं की रिपोर्ट साझा करके ऐप को बेहतर बनाने में हमारी मदद करें।';

  @override
  String get name => 'नाम';

  @override
  String get email => 'ईमेल';

  @override
  String get feedbackType => 'फीडबैक प्रकार';

  @override
  String get subject => 'विषय';

  @override
  String get message => 'संदेश';

  @override
  String get sendFeedback => 'फीडबैक भेजें';

  @override
  String get general => 'सामान्य';

  @override
  String get bugReport => 'बग रिपोर्ट';

  @override
  String get featureRequest => 'सुविधा अनुरोध';

  @override
  String get improvementSuggestion => 'सुधार सुझाव';

  @override
  String get technicalIssue => 'तकनीकी समस्या';

  @override
  String get userExperience => 'उपयोगकर्ता अनुभव';

  @override
  String get pleaseEnterName => 'कृपया अपना नाम दर्ज करें';

  @override
  String get pleaseEnterEmail => 'कृपया अपना ईमेल दर्ज करें';

  @override
  String get pleaseEnterValidEmail => 'कृपया एक वैध ईमेल दर्ज करें';

  @override
  String get pleaseEnterSubject => 'कृपया एक विषय दर्ज करें';

  @override
  String get pleaseEnterMessage => 'कृपया अपना संदेश दर्ज करें';

  @override
  String get messageTooShort => 'संदेश कम से कम 10 अक्षर लंबा होना चाहिए';

  @override
  String get emailAppOpened =>
      'ईमेल ऐप खोला गया। आपकी प्रतिक्रिया के लिए धन्यवाद!';

  @override
  String get emailError =>
      'त्रुटि: ईमेल ऐप नहीं खोल सका। कृपया हमें सीधे ईमेल करें <EMAIL>';

  @override
  String get alternativeContact => 'वैकल्पिक संपर्क';

  @override
  String get alternativeContactText => 'आप हमें सीधे ईमेल भी कर सकते हैं:';

  @override
  String get distanceSummary => 'दूरी सारांश';

  @override
  String get fieldSummary => 'क्षेत्र सारांश';

  @override
  String get saveMeasurement => 'माप सहेजें';

  @override
  String get measurementName => 'माप नाम';

  @override
  String get notes => 'नोट्स';

  @override
  String get notesOptional => 'नोट्स (वैकल्पिक)';

  @override
  String get measurementSaved => 'माप सफलतापूर्वक सहेजा गया';

  @override
  String get errorSavingMeasurement => 'माप सहेजने में त्रुटि';

  @override
  String get deleteConfirmation => 'हटाने की पुष्टि';

  @override
  String get deleteConfirmationMessage =>
      'क्या आप वाकई इस माप को हटाना चाहते हैं?';

  @override
  String get type => 'प्रकार';

  @override
  String get date => 'दिनांक';

  @override
  String get location => 'स्थान';

  @override
  String get timestamp => 'टाइमस्टैम्प';

  @override
  String get startRecording => 'रिकॉर्डिंग शुरू करें';

  @override
  String get stopRecording => 'रिकॉर्डिंग बंद करें';

  @override
  String get recording => 'रिकॉर्ड हो रहा है...';

  @override
  String get gpsAccuracy => 'जीपीएस सटीकता';
}
