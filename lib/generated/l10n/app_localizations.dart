import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_de.dart';
import 'app_localizations_en.dart';
import 'app_localizations_es.dart';
import 'app_localizations_fr.dart';
import 'app_localizations_hi.dart';
import 'app_localizations_ja.dart';
import 'app_localizations_ta.dart';
import 'app_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('de'),
    Locale('en'),
    Locale('es'),
    Locale('fr'),
    Locale('hi'),
    Locale('ja'),
    Locale('ta'),
    Locale('zh'),
  ];

  /// The title of the application
  ///
  /// In en, this message translates to:
  /// **'GPS Map Camera'**
  String get appTitle;

  /// No description provided for @camera.
  ///
  /// In en, this message translates to:
  /// **'Camera'**
  String get camera;

  /// No description provided for @gallery.
  ///
  /// In en, this message translates to:
  /// **'Gallery'**
  String get gallery;

  /// No description provided for @map.
  ///
  /// In en, this message translates to:
  /// **'Map'**
  String get map;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// No description provided for @fieldMeasurement.
  ///
  /// In en, this message translates to:
  /// **'Field Measurement'**
  String get fieldMeasurement;

  /// No description provided for @distanceMeasurement.
  ///
  /// In en, this message translates to:
  /// **'Distance Measurement'**
  String get distanceMeasurement;

  /// No description provided for @locationMarker.
  ///
  /// In en, this message translates to:
  /// **'Location Marker'**
  String get locationMarker;

  /// No description provided for @savedMeasurements.
  ///
  /// In en, this message translates to:
  /// **'Saved Measurements'**
  String get savedMeasurements;

  /// No description provided for @preferences.
  ///
  /// In en, this message translates to:
  /// **'Preferences'**
  String get preferences;

  /// No description provided for @unitFormats.
  ///
  /// In en, this message translates to:
  /// **'Unit Formats'**
  String get unitFormats;

  /// No description provided for @unitSettings.
  ///
  /// In en, this message translates to:
  /// **'Unit Settings'**
  String get unitSettings;

  /// No description provided for @others.
  ///
  /// In en, this message translates to:
  /// **'Others'**
  String get others;

  /// No description provided for @language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// No description provided for @selectLanguage.
  ///
  /// In en, this message translates to:
  /// **'Select your preferred language'**
  String get selectLanguage;

  /// No description provided for @areaUnit.
  ///
  /// In en, this message translates to:
  /// **'Area Unit'**
  String get areaUnit;

  /// No description provided for @areaUnitDescription.
  ///
  /// In en, this message translates to:
  /// **'Unit for area measurements'**
  String get areaUnitDescription;

  /// No description provided for @distanceUnit.
  ///
  /// In en, this message translates to:
  /// **'Distance Unit'**
  String get distanceUnit;

  /// No description provided for @distanceUnitDescription.
  ///
  /// In en, this message translates to:
  /// **'Unit for distance measurements'**
  String get distanceUnitDescription;

  /// No description provided for @perimeterUnit.
  ///
  /// In en, this message translates to:
  /// **'Perimeter Unit'**
  String get perimeterUnit;

  /// No description provided for @perimeterUnitDescription.
  ///
  /// In en, this message translates to:
  /// **'Unit for perimeter measurements'**
  String get perimeterUnitDescription;

  /// No description provided for @howToUse.
  ///
  /// In en, this message translates to:
  /// **'How to Use'**
  String get howToUse;

  /// No description provided for @howToUseDescription.
  ///
  /// In en, this message translates to:
  /// **'Learn how to use the app features'**
  String get howToUseDescription;

  /// No description provided for @feedback.
  ///
  /// In en, this message translates to:
  /// **'Feedback'**
  String get feedback;

  /// No description provided for @feedbackDescription.
  ///
  /// In en, this message translates to:
  /// **'Send us your feedback and suggestions'**
  String get feedbackDescription;

  /// No description provided for @save.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @ok.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// No description provided for @yes.
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// No description provided for @no.
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get no;

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @edit.
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// No description provided for @clear.
  ///
  /// In en, this message translates to:
  /// **'Clear'**
  String get clear;

  /// No description provided for @points.
  ///
  /// In en, this message translates to:
  /// **'Points'**
  String get points;

  /// No description provided for @area.
  ///
  /// In en, this message translates to:
  /// **'Area'**
  String get area;

  /// No description provided for @perimeter.
  ///
  /// In en, this message translates to:
  /// **'Perimeter'**
  String get perimeter;

  /// No description provided for @measurements.
  ///
  /// In en, this message translates to:
  /// **'Measurements'**
  String get measurements;

  /// No description provided for @distance.
  ///
  /// In en, this message translates to:
  /// **'Distance'**
  String get distance;

  /// No description provided for @totalDistance.
  ///
  /// In en, this message translates to:
  /// **'Total Distance'**
  String get totalDistance;

  /// No description provided for @segments.
  ///
  /// In en, this message translates to:
  /// **'Segments'**
  String get segments;

  /// No description provided for @pointToPointDistances.
  ///
  /// In en, this message translates to:
  /// **'Point-to-Point Distances'**
  String get pointToPointDistances;

  /// No description provided for @tapMethod.
  ///
  /// In en, this message translates to:
  /// **'Tap'**
  String get tapMethod;

  /// No description provided for @walkMethod.
  ///
  /// In en, this message translates to:
  /// **'Walk'**
  String get walkMethod;

  /// No description provided for @hybridMethod.
  ///
  /// In en, this message translates to:
  /// **'Hybrid'**
  String get hybridMethod;

  /// No description provided for @satelliteView.
  ///
  /// In en, this message translates to:
  /// **'Satellite'**
  String get satelliteView;

  /// No description provided for @normalView.
  ///
  /// In en, this message translates to:
  /// **'Normal'**
  String get normalView;

  /// No description provided for @settingsSaved.
  ///
  /// In en, this message translates to:
  /// **'Settings saved successfully'**
  String get settingsSaved;

  /// No description provided for @error.
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// No description provided for @success.
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get success;

  /// No description provided for @loading.
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// No description provided for @noData.
  ///
  /// In en, this message translates to:
  /// **'No data available'**
  String get noData;

  /// No description provided for @squareMeters.
  ///
  /// In en, this message translates to:
  /// **'Square Meters'**
  String get squareMeters;

  /// No description provided for @squareFeet.
  ///
  /// In en, this message translates to:
  /// **'Square Feet'**
  String get squareFeet;

  /// No description provided for @acres.
  ///
  /// In en, this message translates to:
  /// **'Acres'**
  String get acres;

  /// No description provided for @hectares.
  ///
  /// In en, this message translates to:
  /// **'Hectares'**
  String get hectares;

  /// No description provided for @squareKilometers.
  ///
  /// In en, this message translates to:
  /// **'Square Kilometers'**
  String get squareKilometers;

  /// No description provided for @squareMiles.
  ///
  /// In en, this message translates to:
  /// **'Square Miles'**
  String get squareMiles;

  /// No description provided for @meters.
  ///
  /// In en, this message translates to:
  /// **'Meters'**
  String get meters;

  /// No description provided for @kilometers.
  ///
  /// In en, this message translates to:
  /// **'Kilometers'**
  String get kilometers;

  /// No description provided for @feet.
  ///
  /// In en, this message translates to:
  /// **'Feet'**
  String get feet;

  /// No description provided for @miles.
  ///
  /// In en, this message translates to:
  /// **'Miles'**
  String get miles;

  /// No description provided for @yards.
  ///
  /// In en, this message translates to:
  /// **'Yards'**
  String get yards;

  /// No description provided for @centimeters.
  ///
  /// In en, this message translates to:
  /// **'Centimeters'**
  String get centimeters;

  /// No description provided for @cameraFeatures.
  ///
  /// In en, this message translates to:
  /// **'📷 Camera Features'**
  String get cameraFeatures;

  /// No description provided for @cameraFeaturesContent.
  ///
  /// In en, this message translates to:
  /// **'• Tap the camera button to take photos with GPS location\n• Photos automatically include location coordinates\n• Timestamp and address are embedded in the image\n• Use the grid lines for better composition\n• Switch between front and back cameras'**
  String get cameraFeaturesContent;

  /// No description provided for @mapViewFeatures.
  ///
  /// In en, this message translates to:
  /// **'🗺️ Map View'**
  String get mapViewFeatures;

  /// No description provided for @mapViewFeaturesContent.
  ///
  /// In en, this message translates to:
  /// **'• View all your photos on an interactive map\n• Tap on markers to see photo details\n• Switch between normal and satellite view\n• Zoom in/out to explore different areas\n• Long press to add custom markers'**
  String get mapViewFeaturesContent;

  /// No description provided for @measurementTools.
  ///
  /// In en, this message translates to:
  /// **'📏 Measurement Tools'**
  String get measurementTools;

  /// No description provided for @measurementToolsContent.
  ///
  /// In en, this message translates to:
  /// **'• Field Measurement: Measure area of land/fields\n• Distance Measurement: Measure distances between points\n• Location Marker: Add and save specific locations\n• Choose between tap, walk, or hybrid input methods\n• Results are saved automatically'**
  String get measurementToolsContent;

  /// No description provided for @galleryFeatures.
  ///
  /// In en, this message translates to:
  /// **'🖼️ Gallery'**
  String get galleryFeatures;

  /// No description provided for @galleryFeaturesContent.
  ///
  /// In en, this message translates to:
  /// **'• Browse all your captured photos\n• View photos in grid or list format\n• Tap to view full-size images\n• Share photos with location data\n• Delete unwanted photos'**
  String get galleryFeaturesContent;

  /// No description provided for @settingsFeatures.
  ///
  /// In en, this message translates to:
  /// **'⚙️ Settings'**
  String get settingsFeatures;

  /// No description provided for @settingsFeaturesContent.
  ///
  /// In en, this message translates to:
  /// **'• Change app language preferences\n• Set measurement units (meters, feet, etc.)\n• Customize area and distance units\n• Access help and feedback options\n• Save your preferences automatically'**
  String get settingsFeaturesContent;

  /// No description provided for @tipsAndTricks.
  ///
  /// In en, this message translates to:
  /// **'💡 Tips & Tricks'**
  String get tipsAndTricks;

  /// No description provided for @tipsAndTricksContent.
  ///
  /// In en, this message translates to:
  /// **'• Enable location services for accurate GPS data\n• Take photos in good lighting for better quality\n• Use measurement tools for professional surveying\n• Regular backup of important photos\n• Check app permissions in device settings'**
  String get tipsAndTricksContent;

  /// No description provided for @needMoreHelp.
  ///
  /// In en, this message translates to:
  /// **'Need More Help?'**
  String get needMoreHelp;

  /// No description provided for @needMoreHelpContent.
  ///
  /// In en, this message translates to:
  /// **'If you need additional assistance, please use the Feedback option in Settings to contact our support team.'**
  String get needMoreHelpContent;

  /// No description provided for @weValueYourFeedback.
  ///
  /// In en, this message translates to:
  /// **'We Value Your Feedback'**
  String get weValueYourFeedback;

  /// No description provided for @feedbackIntro.
  ///
  /// In en, this message translates to:
  /// **'Help us improve the app by sharing your thoughts, suggestions, or reporting issues.'**
  String get feedbackIntro;

  /// No description provided for @name.
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get name;

  /// No description provided for @email.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// No description provided for @feedbackType.
  ///
  /// In en, this message translates to:
  /// **'Feedback Type'**
  String get feedbackType;

  /// No description provided for @subject.
  ///
  /// In en, this message translates to:
  /// **'Subject'**
  String get subject;

  /// No description provided for @message.
  ///
  /// In en, this message translates to:
  /// **'Message'**
  String get message;

  /// No description provided for @sendFeedback.
  ///
  /// In en, this message translates to:
  /// **'Send Feedback'**
  String get sendFeedback;

  /// No description provided for @general.
  ///
  /// In en, this message translates to:
  /// **'General'**
  String get general;

  /// No description provided for @bugReport.
  ///
  /// In en, this message translates to:
  /// **'Bug Report'**
  String get bugReport;

  /// No description provided for @featureRequest.
  ///
  /// In en, this message translates to:
  /// **'Feature Request'**
  String get featureRequest;

  /// No description provided for @improvementSuggestion.
  ///
  /// In en, this message translates to:
  /// **'Improvement Suggestion'**
  String get improvementSuggestion;

  /// No description provided for @technicalIssue.
  ///
  /// In en, this message translates to:
  /// **'Technical Issue'**
  String get technicalIssue;

  /// No description provided for @userExperience.
  ///
  /// In en, this message translates to:
  /// **'User Experience'**
  String get userExperience;

  /// No description provided for @pleaseEnterName.
  ///
  /// In en, this message translates to:
  /// **'Please enter your name'**
  String get pleaseEnterName;

  /// No description provided for @pleaseEnterEmail.
  ///
  /// In en, this message translates to:
  /// **'Please enter your email'**
  String get pleaseEnterEmail;

  /// No description provided for @pleaseEnterValidEmail.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email'**
  String get pleaseEnterValidEmail;

  /// No description provided for @pleaseEnterSubject.
  ///
  /// In en, this message translates to:
  /// **'Please enter a subject'**
  String get pleaseEnterSubject;

  /// No description provided for @pleaseEnterMessage.
  ///
  /// In en, this message translates to:
  /// **'Please enter your message'**
  String get pleaseEnterMessage;

  /// No description provided for @messageTooShort.
  ///
  /// In en, this message translates to:
  /// **'Message should be at least 10 characters long'**
  String get messageTooShort;

  /// No description provided for @emailAppOpened.
  ///
  /// In en, this message translates to:
  /// **'Email app opened. Thank you for your feedback!'**
  String get emailAppOpened;

  /// No description provided for @emailError.
  ///
  /// In en, this message translates to:
  /// **'Error: Could not open email app. Please email us <NAME_EMAIL>'**
  String get emailError;

  /// No description provided for @alternativeContact.
  ///
  /// In en, this message translates to:
  /// **'Alternative Contact'**
  String get alternativeContact;

  /// No description provided for @alternativeContactText.
  ///
  /// In en, this message translates to:
  /// **'You can also email us directly at:'**
  String get alternativeContactText;

  /// No description provided for @distanceSummary.
  ///
  /// In en, this message translates to:
  /// **'Distance Summary'**
  String get distanceSummary;

  /// No description provided for @fieldSummary.
  ///
  /// In en, this message translates to:
  /// **'Field Summary'**
  String get fieldSummary;

  /// No description provided for @saveMeasurement.
  ///
  /// In en, this message translates to:
  /// **'Save Measurement'**
  String get saveMeasurement;

  /// No description provided for @measurementName.
  ///
  /// In en, this message translates to:
  /// **'Measurement Name'**
  String get measurementName;

  /// No description provided for @notes.
  ///
  /// In en, this message translates to:
  /// **'Notes'**
  String get notes;

  /// No description provided for @notesOptional.
  ///
  /// In en, this message translates to:
  /// **'Notes (Optional)'**
  String get notesOptional;

  /// No description provided for @measurementSaved.
  ///
  /// In en, this message translates to:
  /// **'Measurement saved successfully'**
  String get measurementSaved;

  /// No description provided for @errorSavingMeasurement.
  ///
  /// In en, this message translates to:
  /// **'Error saving measurement'**
  String get errorSavingMeasurement;

  /// No description provided for @deleteConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Delete Confirmation'**
  String get deleteConfirmation;

  /// No description provided for @deleteConfirmationMessage.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this measurement?'**
  String get deleteConfirmationMessage;

  /// No description provided for @type.
  ///
  /// In en, this message translates to:
  /// **'Type'**
  String get type;

  /// No description provided for @date.
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get date;

  /// No description provided for @location.
  ///
  /// In en, this message translates to:
  /// **'Location'**
  String get location;

  /// No description provided for @timestamp.
  ///
  /// In en, this message translates to:
  /// **'Timestamp'**
  String get timestamp;

  /// No description provided for @startRecording.
  ///
  /// In en, this message translates to:
  /// **'Start Recording'**
  String get startRecording;

  /// No description provided for @stopRecording.
  ///
  /// In en, this message translates to:
  /// **'Stop Recording'**
  String get stopRecording;

  /// No description provided for @recording.
  ///
  /// In en, this message translates to:
  /// **'Recording...'**
  String get recording;

  /// No description provided for @gpsAccuracy.
  ///
  /// In en, this message translates to:
  /// **'GPS Accuracy'**
  String get gpsAccuracy;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>[
    'de',
    'en',
    'es',
    'fr',
    'hi',
    'ja',
    'ta',
    'zh',
  ].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'de':
      return AppLocalizationsDe();
    case 'en':
      return AppLocalizationsEn();
    case 'es':
      return AppLocalizationsEs();
    case 'fr':
      return AppLocalizationsFr();
    case 'hi':
      return AppLocalizationsHi();
    case 'ja':
      return AppLocalizationsJa();
    case 'ta':
      return AppLocalizationsTa();
    case 'zh':
      return AppLocalizationsZh();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
