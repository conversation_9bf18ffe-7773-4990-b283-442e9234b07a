{"@@locale": "en", "appTitle": "GPS Map Camera", "@appTitle": {"description": "The title of the application"}, "camera": "Camera", "gallery": "Gallery", "map": "Map", "settings": "Settings", "fieldMeasurement": "Field Measurement", "distanceMeasurement": "Distance Measurement", "locationMarker": "Location Marker", "savedMeasurements": "Saved Measurements", "preferences": "Preferences", "unitFormats": "Unit Formats", "unitSettings": "Unit Settings", "others": "Others", "language": "Language", "selectLanguage": "Select your preferred language", "areaUnit": "Area Unit", "areaUnitDescription": "Unit for area measurements", "distanceUnit": "Distance Unit", "distanceUnitDescription": "Unit for distance measurements", "perimeterUnit": "Perimeter Unit", "perimeterUnitDescription": "Unit for perimeter measurements", "howToUse": "How to Use", "howToUseDescription": "Learn how to use the app features", "feedback": "<PERSON><PERSON><PERSON>", "feedbackDescription": "Send us your feedback and suggestions", "save": "Save", "cancel": "Cancel", "ok": "OK", "yes": "Yes", "no": "No", "delete": "Delete", "edit": "Edit", "clear": "Clear", "points": "Points", "area": "Area", "perimeter": "Perimeter", "measurements": "Measurements", "distance": "Distance", "totalDistance": "Total Distance", "segments": "Segments", "pointToPointDistances": "Point-to-Point Distances", "tapMethod": "Tap", "walkMethod": "Walk", "hybridMethod": "Hybrid", "satelliteView": "Satellite", "normalView": "Normal", "settingsSaved": "Setting<PERSON> saved successfully", "error": "Error", "success": "Success", "loading": "Loading...", "noData": "No data available", "squareMeters": "Square Meters", "squareFeet": "Square Feet", "acres": "<PERSON><PERSON>s", "hectares": "Hectares", "squareKilometers": "Square Kilometers", "squareMiles": "Square Miles", "meters": "Meters", "kilometers": "Kilometers", "feet": "Feet", "miles": "<PERSON>", "yards": "Yards", "centimeters": "Centimeters", "cameraFeatures": "📷 Camera Features", "cameraFeaturesContent": "• Tap the camera button to take photos with GPS location\n• Photos automatically include location coordinates\n• Timestamp and address are embedded in the image\n• Use the grid lines for better composition\n• Switch between front and back cameras", "mapViewFeatures": "🗺️ Map View", "mapViewFeaturesContent": "• View all your photos on an interactive map\n• Tap on markers to see photo details\n• Switch between normal and satellite view\n• Zoom in/out to explore different areas\n• Long press to add custom markers", "measurementTools": "📏 Measurement Tools", "measurementToolsContent": "• Field Measurement: Measure area of land/fields\n• Distance Measurement: Measure distances between points\n• Location Marker: Add and save specific locations\n• Choose between tap, walk, or hybrid input methods\n• Results are saved automatically", "galleryFeatures": "🖼️ Gallery", "galleryFeaturesContent": "• Browse all your captured photos\n• View photos in grid or list format\n• Tap to view full-size images\n• Share photos with location data\n• Delete unwanted photos", "settingsFeatures": "⚙️ Settings", "settingsFeaturesContent": "• Change app language preferences\n• Set measurement units (meters, feet, etc.)\n• Customize area and distance units\n• Access help and feedback options\n• Save your preferences automatically", "tipsAndTricks": "💡 Tips & Tricks", "tipsAndTricksContent": "• Enable location services for accurate GPS data\n• Take photos in good lighting for better quality\n• Use measurement tools for professional surveying\n• Regular backup of important photos\n• Check app permissions in device settings", "needMoreHelp": "Need More Help?", "needMoreHelpContent": "If you need additional assistance, please use the Feedback option in Settings to contact our support team.", "weValueYourFeedback": "We Value Your Feedback", "feedbackIntro": "Help us improve the app by sharing your thoughts, suggestions, or reporting issues.", "name": "Name", "email": "Email", "feedbackType": "Feedback Type", "subject": "Subject", "message": "Message", "sendFeedback": "Send Feedback", "general": "General", "bugReport": "Bug Report", "featureRequest": "Feature Request", "improvementSuggestion": "Improvement Suggestion", "technicalIssue": "Technical Issue", "userExperience": "User Experience", "pleaseEnterName": "Please enter your name", "pleaseEnterEmail": "Please enter your email", "pleaseEnterValidEmail": "Please enter a valid email", "pleaseEnterSubject": "Please enter a subject", "pleaseEnterMessage": "Please enter your message", "messageTooShort": "Message should be at least 10 characters long", "emailAppOpened": "Email app opened. Thank you for your feedback!", "emailError": "Error: Could not open email app. Please email us <NAME_EMAIL>", "alternativeContact": "Alternative Contact", "alternativeContactText": "You can also email us directly at:", "distanceSummary": "Distance Summary", "fieldSummary": "Field Summary", "saveMeasurement": "Save Measurement", "measurementName": "Measurement Name", "notes": "Notes", "notesOptional": "Notes (Optional)", "measurementSaved": "Measurement saved successfully", "errorSavingMeasurement": "Error saving measurement", "deleteConfirmation": "Delete Confirmation", "deleteConfirmationMessage": "Are you sure you want to delete this measurement?", "type": "Type", "date": "Date", "location": "Location", "timestamp": "Timestamp", "startRecording": "Start Recording", "stopRecording": "Stop Recording", "recording": "Recording...", "gpsAccuracy": "GPS Accuracy"}