
class OpenLocationCode {
  final _openLocationCode = OpenLocationCode();
  
  String encode(double latitude, double longitude) {
    return _openLocationCode.encode(latitude, longitude);
  }
  
  Map<String, double> decode(String code) {
    final result = _openLocationCode.decode(code);
    return {
      'latitude': result.center.latitude,
      'longitude': result.center.longitude,
    };
  }
}

extension on Map<String, double> {
  get center => null;
}
