import 'package:flutter/material.dart';
import '../presentation/widgets/interactive_tutorial_overlay.dart';

class TutorialData {
  static List<TutorialStep> getCameraTutorialSteps() {
    return [
      TutorialStep(
        title: "Welcome to Camera!",
        description: "This is your professional GPS camera. Let me show you step-by-step how to capture amazing photos with location data. Follow each step carefully!",
        actionText: "Let's start the camera tutorial!",
        icon: Icons.camera_alt,
        highlightColor: Colors.blue,
        arrowPosition: const Offset(200, 300),
        arrowDirection: ArrowDirection.down,
      ),
      TutorialStep(
        title: "Step 1: Find the Capture Button",
        description: "Look for the large circular button at the bottom center of the screen. This is your main capture button. Tap it once to take a photo with GPS data automatically embedded.",
        actionText: "Tap to capture",
        icon: Icons.camera,
        highlightColor: Colors.blue,
        targetRect: const Rect.fromLTWH(150, 600, 100, 100), // Capture button area
        arrowPosition: const Offset(200, 550),
        arrowDirection: ArrowDirection.down,
      ),
      TutorialStep(
        title: "Step 2: Adjust Flash Settings",
        description: "Look for the flash icon (⚡) usually at the top of the screen. Tap it to cycle through: Auto (recommended), On (for dark areas), or Off (for natural lighting). Choose based on your environment.",
        actionText: "Tap for flash",
        icon: Icons.flash_on,
        highlightColor: Colors.orange,
        targetRect: const Rect.fromLTWH(50, 100, 50, 50), // Flash button area
        arrowPosition: const Offset(75, 50),
        arrowDirection: ArrowDirection.down,
      ),
      TutorialStep(
        title: "Step 3: Switch Camera if Needed",
        description: "Find the camera flip icon (🔄) to switch between front and back camera. Tap once to switch to front camera for selfies, tap again to return to back camera.",
        actionText: "Tap to flip",
        icon: Icons.flip_camera_ios,
        highlightColor: Colors.green,
        targetRect: const Rect.fromLTWH(300, 100, 50, 50), // Camera flip button area
        arrowPosition: const Offset(325, 50),
        arrowDirection: ArrowDirection.down,
      ),
      TutorialStep(
        title: "Step 4: Check GPS Status",
        description: "Look for the GPS indicator (📍) on screen. Green circle = GPS ready, Red circle = searching for signal. Wait for green before taking important location photos.",
        actionText: "GPS status",
        icon: Icons.gps_fixed,
        highlightColor: Colors.red,
        targetRect: const Rect.fromLTWH(20, 200, 40, 40), // GPS status area
        arrowPosition: const Offset(40, 150),
        arrowDirection: ArrowDirection.down,
      ),
      TutorialStep(
        title: "Step 5: View Photos on Map",
        description: "After taking photos, tap the map icon (🗺️) to see all your captured images plotted on an interactive map. Discover where you've been!",
        actionText: "View map",
        icon: Icons.map,
        highlightColor: Colors.green,
        targetRect: const Rect.fromLTWH(250, 600, 60, 60), // Map button area
        arrowPosition: const Offset(280, 550),
        arrowDirection: ArrowDirection.down,
      ),
    ];
  }

  static List<TutorialStep> getGalleryTutorialSteps() {
    return [
      TutorialStep(
        title: "Welcome to Gallery!",
        description: "Here you can see all your photos with GPS location data. Let me show you how to navigate and use all the gallery features step by step.",
        actionText: "Let's explore your photo gallery!",
        icon: Icons.photo_library,
        highlightColor: Colors.purple,
        arrowPosition: const Offset(200, 300),
        arrowDirection: ArrowDirection.down,
      ),
      TutorialStep(
        title: "Step 1: Browse Photo Grid",
        description: "Your photos are displayed in a grid layout. Scroll up and down to see all your memories. Each photo thumbnail shows a small location icon if GPS data is available.",
        actionText: "Scroll photos",
        icon: Icons.grid_view,
        highlightColor: Colors.blue,
        targetRect: const Rect.fromLTWH(20, 150, 350, 400), // Photo grid area
        arrowPosition: const Offset(200, 100),
        arrowDirection: ArrowDirection.down,
      ),
      TutorialStep(
        title: "Step 2: View Photo Details",
        description: "Tap any photo once to open it in full screen. You'll see the image plus GPS coordinates, address, date, and time. Swipe left/right to browse other photos.",
        actionText: "Tap photo",
        icon: Icons.info,
        highlightColor: Colors.green,
        targetRect: const Rect.fromLTWH(20, 150, 100, 100), // First photo in grid
        arrowPosition: const Offset(70, 100),
        arrowDirection: ArrowDirection.down,
      ),
      TutorialStep(
        title: "Step 3: Check Location Info",
        description: "In photo details, look for the location section. You'll see GPS coordinates, full address, and sometimes a small map. This shows exactly where the photo was taken.",
        actionText: "Location info",
        icon: Icons.location_on,
        highlightColor: Colors.red,
        targetRect: const Rect.fromLTWH(20, 400, 350, 80), // Location info area
        arrowPosition: const Offset(200, 350),
        arrowDirection: ArrowDirection.down,
      ),
      TutorialStep(
        title: "Step 4: Share Your Photos",
        description: "To share a photo: Long press on any photo thumbnail, then select 'Share' from the menu. You can send it via WhatsApp, email, or save to your device gallery.",
        actionText: "Long press",
        icon: Icons.share,
        highlightColor: Colors.orange,
        targetRect: const Rect.fromLTWH(130, 150, 100, 100), // Second photo in grid
        arrowPosition: const Offset(180, 100),
        arrowDirection: ArrowDirection.down,
      ),
      TutorialStep(
        title: "Step 5: Search & Filter",
        description: "Tap the search icon (🔍) at the top to find specific photos. You can search by location name, date, or address. Very helpful when you have many photos!",
        actionText: "Search",
        icon: Icons.search,
        highlightColor: Colors.teal,
        targetRect: const Rect.fromLTWH(320, 50, 50, 50), // Search button area
        arrowPosition: const Offset(345, 20),
        arrowDirection: ArrowDirection.down,
      ),
    ];
  }

  static List<TutorialStep> getMapTutorialSteps() {
    return [
      TutorialStep(
        title: "Welcome to Map View!",
        description: "This interactive map shows all your photos as markers. Let me guide you through all the map features and how to use them effectively.",
        actionText: "Let's explore the interactive map!",
        icon: Icons.map,
        highlightColor: Colors.green,
        arrowPosition: const Offset(200, 300),
        arrowDirection: ArrowDirection.down,
      ),
      TutorialStep(
        title: "Step 1: Understanding Photo Markers",
        description: "Each red pin (📍) on the map represents where you took a photo. Tap any marker to see a preview of the photo taken at that location. Multiple photos at the same location will show as a cluster.",
        actionText: "Tap marker",
        icon: Icons.place,
        highlightColor: Colors.red,
        targetRect: const Rect.fromLTWH(100, 250, 40, 40), // Example photo marker
        arrowPosition: const Offset(120, 200),
        arrowDirection: ArrowDirection.down,
      ),
      TutorialStep(
        title: "Step 2: Navigate the Map",
        description: "Use pinch gestures to zoom in/out, or tap the +/- buttons. Drag to move around the map. Double-tap to quickly zoom in on an area. Explore your photo locations in detail!",
        actionText: "Pinch & drag",
        icon: Icons.zoom_in,
        highlightColor: Colors.blue,
        targetRect: const Rect.fromLTWH(20, 150, 350, 300), // Map area
        arrowPosition: const Offset(200, 100),
        arrowDirection: ArrowDirection.down,
      ),
      TutorialStep(
        title: "Step 3: Change Map Style",
        description: "Look for the layers button (⚏) to switch map types. Tap it to choose: Normal (street view), Satellite (aerial view), or Terrain (topographic). Pick the style that works best for you.",
        actionText: "Map style",
        icon: Icons.layers,
        highlightColor: Colors.orange,
        targetRect: const Rect.fromLTWH(320, 100, 50, 50), // Map layers button
        arrowPosition: const Offset(345, 50),
        arrowDirection: ArrowDirection.down,
      ),
      TutorialStep(
        title: "Step 4: Find Your Location",
        description: "Tap the location button (🎯) to center the map on your current position. This is helpful for navigation or to see photos taken near where you are now.",
        actionText: "My location",
        icon: Icons.my_location,
        highlightColor: Colors.purple,
        targetRect: const Rect.fromLTWH(320, 160, 50, 50), // My location button
        arrowPosition: const Offset(345, 110),
        arrowDirection: ArrowDirection.down,
      ),
      TutorialStep(
        title: "Step 5: Work with Photo Clusters",
        description: "When multiple photos are close together, they appear as numbered clusters (like '5'). Tap any cluster to zoom in and see individual photo markers. This keeps the map clean and organized.",
        actionText: "Tap cluster",
        icon: Icons.group_work,
        highlightColor: Colors.teal,
        targetRect: const Rect.fromLTWH(150, 300, 60, 60), // Example cluster area
        arrowPosition: const Offset(180, 250),
        arrowDirection: ArrowDirection.down,
      ),
    ];
  }
}
