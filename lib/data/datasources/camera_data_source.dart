import 'package:camera/camera.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';

class CameraDatasource {
  Future<List<CameraDescription>> getAvailableCameras() async {
    return await availableCameras();
  }

  CameraController? _controller;

  Future<void> initialize() async {
    final cameras = await availableCameras();
    _controller = CameraController(cameras.first, ResolutionPreset.high);
    await _controller!.initialize();
  }

  CameraController get controller => _controller!;

  Future<String> takePicture() async {
    final directory = await getApplicationDocumentsDirectory();
    final filePath = '${directory.path}/${DateTime.now().millisecondsSinceEpoch}.jpg';
    final file = await _controller!.takePicture();
    final newFile = await File(file.path).copy(filePath);
    return newFile.path;
  }
}
