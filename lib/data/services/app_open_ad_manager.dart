
import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'admob_service.dart';
import 'pro_status_manager.dart';

class AppOpenAdManager {
  static final AppOpenAdManager _instance = AppOpenAdManager._internal();
  factory AppOpenAdManager() => _instance;
  AppOpenAdManager._internal();

  AppOpenAd? _appOpenAd;
  bool _isShowingAd = false;
  bool _isAdLoaded = false;
  DateTime? _appOpenLoadTime;
  final ProStatusManager _proManager = ProStatusManager();
  
  // Maximum duration to keep an app open ad before considering it expired
  static const Duration maxCacheDuration = Duration(hours: 4);

  void initialize() {
    _loadAppOpenAd();
  }

  void _loadAppOpenAd() {
    // Don't load ads for Pro users
    if (_proManager.isProUser) {
      return;
    }

    if (_isAdLoaded || _isShowingAd) {
      return;
    }

    print('Loading app open ad...');
    AdMobService.loadAppOpenAd(
      onAdLoaded: (AppOpenAd ad) {
        print('App open ad loaded successfully');
        _appOpenAd = ad;
        _isAdLoaded = true;
        _appOpenLoadTime = DateTime.now();
        
        _appOpenAd!.fullScreenContentCallback = FullScreenContentCallback(
          onAdShowedFullScreenContent: (AppOpenAd ad) {
            print('App open ad showed full screen content');
            _isShowingAd = true;
          },
          onAdFailedToShowFullScreenContent: (AppOpenAd ad, AdError error) {
            print('App open ad failed to show full screen content: $error');
            _isShowingAd = false;
            ad.dispose();
            _appOpenAd = null;
            _isAdLoaded = false;
            _loadAppOpenAd(); // Try to load a new ad
          },
          onAdDismissedFullScreenContent: (AppOpenAd ad) {
            print('App open ad dismissed full screen content');
            _isShowingAd = false;
            ad.dispose();
            _appOpenAd = null;
            _isAdLoaded = false;
            _loadAppOpenAd(); // Load a new ad for next time
          },
          onAdImpression: (AppOpenAd ad) {
            print('App open ad impression recorded');
          },
          onAdClicked: (AppOpenAd ad) {
            print('App open ad clicked');
          },
        );
      },
      onAdFailedToLoad: (LoadAdError error) {
        print('App open ad failed to load: $error');
        _isAdLoaded = false;
        _appOpenAd = null;
        
        // Retry loading after a delay
        Future.delayed(const Duration(seconds: 10), () {
          _loadAppOpenAd();
        });
      },
    );
  }

  bool get isAdAvailable {
    return _appOpenAd != null && 
           _isAdLoaded && 
           !_isShowingAd &&
           !_isAdExpired();
  }

  bool _isAdExpired() {
    if (_appOpenLoadTime == null) return true;
    return DateTime.now().difference(_appOpenLoadTime!) > maxCacheDuration;
  }

  void showAdIfAvailable() {
    try {
      // Don't show ads for Pro users
      if (_proManager.isProUser) {
        print('Pro user - no app open ad');
        return;
      }

      if (!isAdAvailable) {
        print('App open ad not available');
        if (!_isAdLoaded) {
          _loadAppOpenAd(); // Try to load if not loaded
        }
        return;
      }

      if (_isAdExpired()) {
        print('App open ad expired, loading new ad');
        _appOpenAd?.dispose();
        _appOpenAd = null;
        _isAdLoaded = false;
        _loadAppOpenAd();
        return;
      }

      print('Showing app open ad');
      _appOpenAd!.show();
    } catch (e) {
      print('Error showing app open ad: $e');
      // Continue app flow even if ad fails
    }
  }

  void dispose() {
    _appOpenAd?.dispose();
    _appOpenAd = null;
    _isAdLoaded = false;
    _isShowingAd = false;
  }
}

// App Lifecycle Observer to show ads when app comes to foreground
class AppLifecycleObserver extends WidgetsBindingObserver {
  final AppOpenAdManager _appOpenAdManager = AppOpenAdManager();
  bool _isAppInBackground = false;

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    switch (state) {
      case AppLifecycleState.resumed:
        print('App resumed from background');
        if (_isAppInBackground) {
          _isAppInBackground = false;
          // Show app open ad when returning from background
          Future.delayed(const Duration(milliseconds: 500), () {
            _appOpenAdManager.showAdIfAvailable();
          });
        }
        break;
      case AppLifecycleState.paused:
        print('App paused (going to background)');
        _isAppInBackground = true;
        break;
      case AppLifecycleState.detached:
        print('App detached');
        break;
      case AppLifecycleState.inactive:
        print('App inactive');
        break;
      case AppLifecycleState.hidden:
        print('App hidden');
        break;
    }
  }
}
