import 'package:geocoding/geocoding.dart';

class GeocodingService {
  Future<String> getAddressFromLatLng(double latitude, double longitude) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(latitude, longitude);
      
      if (placemarks.isEmpty) {
        return "Address not found";
      }
      
      Placemark place = placemarks[0];
      
      // Build address string
      List<String> addressParts = [
        if (place.street != null && place.street!.isNotEmpty) place.street!,
        if (place.subLocality != null && place.subLocality!.isNotEmpty) place.subLocality!,
        if (place.locality != null && place.locality!.isNotEmpty) place.locality!,
        if (place.administrativeArea != null && place.administrativeArea!.isNotEmpty) place.administrativeArea!,
        if (place.country != null && place.country!.isNotEmpty) place.country!,
      ];
      
      return addressParts.join(", ");
    } catch (e) {
      print('Error getting address: $e');
      return "Error getting address";
    }
  }
}