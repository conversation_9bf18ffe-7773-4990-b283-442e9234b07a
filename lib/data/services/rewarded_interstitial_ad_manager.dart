
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'admob_service.dart';

class RewardedInterstitialAdManager {
  static final RewardedInterstitialAdManager _instance = RewardedInterstitialAdManager._internal();
  factory RewardedInterstitialAdManager() => _instance;
  RewardedInterstitialAdManager._internal();

  RewardedInterstitialAd? _rewardedInterstitialAd;
  bool _isAdLoaded = false;
  bool _isShowingAd = false;
  DateTime? _adLoadTime;
  
  // Maximum duration to keep a rewarded interstitial ad before considering it expired
  static const Duration maxCacheDuration = Duration(hours: 1);

  void initialize() {
    _loadRewardedInterstitialAd();
  }

  void _loadRewardedInterstitialAd() {
    if (_isAdLoaded || _isShowingAd) {
      return;
    }

    print('Loading rewarded interstitial ad...');
    AdMobService.loadRewardedInterstitialAd(
      onAdLoaded: (RewardedInterstitialAd ad) {
        print('Rewarded interstitial ad loaded successfully');
        _rewardedInterstitialAd = ad;
        _isAdLoaded = true;
        _adLoadTime = DateTime.now();
        
        _rewardedInterstitialAd!.fullScreenContentCallback = FullScreenContentCallback(
          onAdShowedFullScreenContent: (RewardedInterstitialAd ad) {
            print('Rewarded interstitial ad showed full screen content');
            _isShowingAd = true;
          },
          onAdFailedToShowFullScreenContent: (RewardedInterstitialAd ad, AdError error) {
            print('Rewarded interstitial ad failed to show full screen content: $error');
            _isShowingAd = false;
            ad.dispose();
            _rewardedInterstitialAd = null;
            _isAdLoaded = false;
            _loadRewardedInterstitialAd(); // Try to load a new ad
          },
          onAdDismissedFullScreenContent: (RewardedInterstitialAd ad) {
            print('Rewarded interstitial ad dismissed full screen content');
            _isShowingAd = false;
            ad.dispose();
            _rewardedInterstitialAd = null;
            _isAdLoaded = false;
            _loadRewardedInterstitialAd(); // Load a new ad for next time
          },
          onAdImpression: (RewardedInterstitialAd ad) {
            print('Rewarded interstitial ad impression recorded');
          },
          onAdClicked: (RewardedInterstitialAd ad) {
            print('Rewarded interstitial ad clicked');
          },
        );
      },
      onAdFailedToLoad: (LoadAdError error) {
        print('Rewarded interstitial ad failed to load: $error');
        _isAdLoaded = false;
        _rewardedInterstitialAd = null;
        
        // Retry loading after a delay
        Future.delayed(const Duration(seconds: 30), () {
          _loadRewardedInterstitialAd();
        });
      },
    );
  }

  bool get isAdAvailable {
    return _rewardedInterstitialAd != null && 
           _isAdLoaded && 
           !_isShowingAd &&
           !_isAdExpired();
  }

  bool _isAdExpired() {
    if (_adLoadTime == null) return true;
    return DateTime.now().difference(_adLoadTime!) > maxCacheDuration;
  }

  void showAdIfAvailable({
    required void Function(AdWithoutView ad, RewardItem reward) onUserEarnedReward,
    void Function()? onAdClosed,
  }) {
    if (!isAdAvailable) {
      print('Rewarded interstitial ad not available');
      if (!_isAdLoaded) {
        _loadRewardedInterstitialAd(); // Try to load if not loaded
      }
      onAdClosed?.call(); // Call onAdClosed if ad is not available
      return;
    }

    if (_isAdExpired()) {
      print('Rewarded interstitial ad expired, loading new ad');
      _rewardedInterstitialAd?.dispose();
      _rewardedInterstitialAd = null;
      _isAdLoaded = false;
      _loadRewardedInterstitialAd();
      onAdClosed?.call(); // Call onAdClosed if ad is expired
      return;
    }

    print('Showing rewarded interstitial ad');
    _rewardedInterstitialAd!.show(
      onUserEarnedReward: (AdWithoutView ad, RewardItem reward) {
        print('User earned reward: ${reward.amount} ${reward.type}');
        onUserEarnedReward(ad, reward);
      },
    );
    
    // Set up a callback for when ad is closed
    if (onAdClosed != null) {
      _rewardedInterstitialAd!.fullScreenContentCallback = FullScreenContentCallback(
        onAdDismissedFullScreenContent: (RewardedInterstitialAd ad) {
          print('Rewarded interstitial ad dismissed');
          _isShowingAd = false;
          ad.dispose();
          _rewardedInterstitialAd = null;
          _isAdLoaded = false;
          _loadRewardedInterstitialAd();
          onAdClosed();
        },
        onAdFailedToShowFullScreenContent: (RewardedInterstitialAd ad, AdError error) {
          print('Rewarded interstitial ad failed to show: $error');
          _isShowingAd = false;
          ad.dispose();
          _rewardedInterstitialAd = null;
          _isAdLoaded = false;
          _loadRewardedInterstitialAd();
          onAdClosed();
        },
      );
    }
  }

  void dispose() {
    _rewardedInterstitialAd?.dispose();
    _rewardedInterstitialAd = null;
    _isAdLoaded = false;
    _isShowingAd = false;
  }
}
