import 'dart:async';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'admob_service.dart';

class PersistentAdManager {
  static final PersistentAdManager _instance = PersistentAdManager._internal();
  factory PersistentAdManager() => _instance;
  PersistentAdManager._internal();

  final Map<String, BannerAd> _loadedAds = {};
  final Map<String, bool> _adLoadingStates = {};
  Timer? _refreshTimer;

  static const Duration _refreshInterval = Duration(minutes: 5);

  void initialize() {
    // Start periodic refresh of ads
    _refreshTimer = Timer.periodic(_refreshInterval, (timer) {
      _refreshAllAds();
    });
  }

  void dispose() {
    _refreshTimer?.cancel();
    for (var ad in _loadedAds.values) {
      ad.dispose();
    }
    _loadedAds.clear();
    _adLoadingStates.clear();
  }

  BannerAd? getAd(String key) {
    return _loadedAds[key];
  }

  bool isAdLoaded(String key) {
    return _loadedAds.containsKey(key) && _loadedAds[key] != null;
  }

  bool isAdLoading(String key) {
    return _adLoadingStates[key] ?? false;
  }

  void preloadAd(String key, AdSize adSize) {
    if (_adLoadingStates[key] == true || _loadedAds.containsKey(key)) {
      return; // Already loading or loaded
    }

    _adLoadingStates[key] = true;

    final ad = AdMobService.createBannerAd(
      adSize: adSize,
      onAdLoaded: (Ad ad) {
        print('Persistent ad loaded for key: $key');
        _loadedAds[key] = ad as BannerAd;
        _adLoadingStates[key] = false;
      },
      onAdFailedToLoad: (Ad ad, LoadAdError error) {
        print('Persistent ad failed to load for key $key: $error');
        ad.dispose();
        _adLoadingStates[key] = false;
        
        // Retry after delay
        Future.delayed(const Duration(seconds: 10), () {
          if (!_loadedAds.containsKey(key)) {
            preloadAd(key, adSize);
          }
        });
      },
    );

    ad.load();
  }

  void _refreshAllAds() {
    print('Refreshing all persistent ads...');
    final keys = List<String>.from(_loadedAds.keys);
    
    for (String key in keys) {
      final oldAd = _loadedAds[key];
      if (oldAd != null) {
        // Create new ad
        final newAd = AdMobService.createBannerAd(
          adSize: oldAd.size,
          onAdLoaded: (Ad ad) {
            print('Refreshed ad loaded for key: $key');
            // Dispose old ad and replace with new one
            oldAd.dispose();
            _loadedAds[key] = ad as BannerAd;
          },
          onAdFailedToLoad: (Ad ad, LoadAdError error) {
            print('Failed to refresh ad for key $key: $error');
            ad.dispose();
            // Keep the old ad if refresh fails
          },
        );
        
        newAd.load();
      }
    }
  }

  void removeAd(String key) {
    final ad = _loadedAds.remove(key);
    ad?.dispose();
    _adLoadingStates.remove(key);
  }
}
