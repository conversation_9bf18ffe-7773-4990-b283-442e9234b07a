import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocalizationService {
  static const String _localeKey = 'selected_locale';
  
  static LocalizationService? _instance;
  static LocalizationService get instance {
    _instance ??= LocalizationService._internal();
    return _instance!;
  }
  
  LocalizationService._internal();

  // Supported locales
  static const List<Locale> supportedLocales = [
    Locale('en', ''), // English
    Locale('ta', ''), // Tamil
    Locale('hi', ''), // Hindi
    Locale('es', ''), // Spanish
    Locale('fr', ''), // French
    Locale('de', ''), // German
    Locale('zh', ''), // Chinese
    Locale('ja', ''), // Japanese
  ];

  // Language display names
  static const Map<String, String> languageNames = {
    'en': 'English',
    'ta': 'Tamil',
    'hi': 'Hindi',
    'es': 'Spanish',
    'fr': 'French',
    'de': 'German',
    'zh': 'Chinese',
    'ja': 'Japanese',
  };

  // Language native names
  static const Map<String, String> languageNativeNames = {
    'en': 'English',
    'ta': 'தமிழ்',
    'hi': 'हिन्दी',
    'es': 'Español',
    'fr': 'Français',
    'de': 'Deutsch',
    'zh': '中文',
    'ja': '日本語',
  };

  /// Get the current locale from SharedPreferences
  Future<Locale> getCurrentLocale() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final localeCode = prefs.getString(_localeKey);
      
      if (localeCode != null) {
        return Locale(localeCode, '');
      }
    } catch (e) {
      print('Error loading locale: $e');
    }
    
    // Return default locale (English) if not found
    return const Locale('en', '');
  }

  /// Save the selected locale to SharedPreferences
  Future<bool> setLocale(Locale locale) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setString(_localeKey, locale.languageCode);
    } catch (e) {
      print('Error saving locale: $e');
      return false;
    }
  }

  /// Get language display name
  String getLanguageDisplayName(String languageCode) {
    return languageNames[languageCode] ?? 'Unknown';
  }

  /// Get language native name
  String getLanguageNativeName(String languageCode) {
    return languageNativeNames[languageCode] ?? 'Unknown';
  }

  /// Check if a locale is supported
  bool isLocaleSupported(Locale locale) {
    return supportedLocales.any((supportedLocale) => 
        supportedLocale.languageCode == locale.languageCode);
  }

  /// Get locale from language name
  Locale? getLocaleFromLanguageName(String languageName) {
    final entry = languageNames.entries.firstWhere(
      (entry) => entry.value == languageName,
      orElse: () => const MapEntry('', ''),
    );
    
    if (entry.key.isNotEmpty) {
      return Locale(entry.key, '');
    }
    
    return null;
  }

  /// Get all supported language names
  List<String> getSupportedLanguageNames() {
    return supportedLocales.map((locale) => 
        getLanguageDisplayName(locale.languageCode)).toList();
  }

  /// Get all supported language native names
  List<String> getSupportedLanguageNativeNames() {
    return supportedLocales.map((locale) => 
        getLanguageNativeName(locale.languageCode)).toList();
  }

  /// Clear saved locale
  Future<bool> clearLocale() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.remove(_localeKey);
    } catch (e) {
      print('Error clearing locale: $e');
      return false;
    }
  }

  /// Get system locale if supported, otherwise return default
  Locale getSystemLocaleOrDefault() {
    final systemLocale = WidgetsBinding.instance.platformDispatcher.locale;
    
    if (isLocaleSupported(systemLocale)) {
      return systemLocale;
    }
    
    return const Locale('en', '');
  }

  /// Resolve locale based on supported locales
  Locale resolveLocale(Locale? locale, Iterable<Locale> supportedLocales) {
    if (locale != null && isLocaleSupported(locale)) {
      return locale;
    }
    
    return getSystemLocaleOrDefault();
  }
}
