import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';

import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

class ImageProcessingService {
  Future<String> addTextOverlayToImage({
    required String imagePath,
    required String text,
    required String locationText,
    String? addressText,
    required String timestamp,
    String? plusCode,
  }) async {
    try {
      // Read the image file
      final File imageFile = File(imagePath);
      final List<int> imageBytes = await imageFile.readAsBytes();
      final img.Image? image = img.decodeImage(Uint8List.fromList(imageBytes));
      
      if (image == null) {
        throw Exception('Failed to decode image');
      }
      
      // Create a copy of the image to draw on - preserve original dimensions and quality
      final img.Image editableImage = img.copyResize(
        image, 
        width: image.width, 
        height: image.height,
        interpolation: img.Interpolation.cubic
      );
      
      // Add semi-transparent background for text
      _drawTextBackground(editableImage);
      
      // Add text overlay
      // App name at the top - LARGER FONT
      _drawText(
        editableImage,
        text,
        20,
        20,
        img.ColorRgba8(255, 255, 255, 255),
        fontSize: 32  // Increased from 24
      );
      
      // Location at the bottom - LARGER FONT
      int yPosition = editableImage.height - 160;
      
      _drawText(
        editableImage,
        locationText,
        20,
        yPosition,
        img.ColorRgba8(255, 255, 255, 255),
        fontSize: 24  // Increased from 18
      );
      
      yPosition += 40;  // Increased spacing
      
      // Plus code if available - LARGER FONT
      if (plusCode != null) {
        _drawText(
          editableImage,
          "Plus Code: $plusCode",
          20,
          yPosition,
          img.ColorRgba8(255, 255, 255, 255),
          fontSize: 24  // Increased from 18
        );
        yPosition += 40;  // Increased spacing
      }
      
      // Address if available - LARGER FONT
      if (addressText != null) {
        _drawText(
          editableImage,
          addressText,
          20,
          yPosition,
          img.ColorRgba8(255, 255, 255, 255),
          fontSize: 24  // Increased from 18
        );
        yPosition += 40;  // Increased spacing
      }
      
      // Timestamp at the bottom - LARGER FONT
      _drawText(
        editableImage,
        timestamp,
        20,
        yPosition,
        img.ColorRgba8(255, 255, 255, 255),
        fontSize: 24  // Increased from 18
      );
      
      // Save the modified image with high quality
      final directory = await getApplicationDocumentsDirectory();
      final String folderPath = '${directory.path}/Pictures/Edited';
      
      await Directory(folderPath).create(recursive: true);
      
      final String fileName = 'edited_${path.basename(imagePath)}';
      final String newImagePath = path.join(folderPath, fileName);
      
      final File newImageFile = File(newImagePath);
      await newImageFile.writeAsBytes(img.encodeJpg(editableImage, quality: 100));
      
      return newImagePath;
    } catch (e) {
      print('Error processing image: $e');
      return imagePath; // Return original path if processing fails
    }
  }
  
  void _drawTextBackground(img.Image image) {
    // Draw semi-transparent black rectangle at the top - TALLER
    _drawRectangle(
      image, 
      0, 
      0, 
      image.width, 
      90,  // Increased from 70
      img.ColorRgba8(0, 0, 0, 170)  // Slightly more opaque
    );
    
    // Draw semi-transparent black rectangle at the bottom - TALLER
    _drawRectangle(
      image, 
      0, 
      image.height - 200,  // Increased from 160
      image.width, 
      image.height, 
      img.ColorRgba8(0, 0, 0, 170)  // Slightly more opaque
    );
  }
  
  void _drawRectangle(img.Image image, int x1, int y1, int x2, int y2, img.ColorRgba8 color) {
    for (int y = y1; y < y2; y++) {
      for (int x = x1; x < x2; x++) {
        if (x >= 0 && x < image.width && y >= 0 && y < image.height) {
          image.setPixel(x, y, color);
        }
      }
    }
  }
  
  void _drawText(
    img.Image image, 
    String text, 
    int x, 
    int y, 
    img.ColorRgba8 color, 
    {int fontSize = 24}  // Default increased from 18
  ) {
    // Choose font based on size
    img.BitmapFont font;
    switch (fontSize) {
      case 32:
        font = img.arial24;  // Use largest available font
        break;
      case 24:
        font = img.arial24;
        break;
      case 14:
        font = img.arial14;
        break;
      default:
        font = img.arial24;  // Default to larger font
    }
    
    // Draw text with a stronger shadow for better visibility
    img.drawString(
      image,
      text,
      font: font,
      x: x + 2,  // Increased shadow offset
      y: y + 2,  // Increased shadow offset
      color: img.ColorRgba8(0, 0, 0, 220),  // Darker shadow
    );
    
    img.drawString(
      image,
      text,
      font: font,
      x: x,
      y: y,
      color: color,
    );
  }

  /// Check if an image file is valid and can be decoded
  static Future<bool> isValidImageFile(String imagePath) async {
    try {
      final File imageFile = File(imagePath);
      if (!await imageFile.exists()) {
        print('Image file does not exist: $imagePath');
        return false;
      }
      
      // Try to read a small portion of the file to check if it's a valid image
      final RandomAccessFile file = await imageFile.open(mode: FileMode.read);
      List<int> header = [];
      try {
        // Read first 12 bytes to check file signature
        header = await file.read(12);
      } finally {
        await file.close();
      }
      
      // Check for common image format signatures
      // JPEG starts with FF D8 FF
      if (header.length >= 3 && 
          header[0] == 0xFF && 
          header[1] == 0xD8 && 
          header[2] == 0xFF) {
        return true;
      }
      
      // PNG starts with 89 50 4E 47 0D 0A 1A 0A
      if (header.length >= 8 &&
          header[0] == 0x89 && 
          header[1] == 0x50 && 
          header[2] == 0x4E && 
          header[3] == 0x47 &&
          header[4] == 0x0D && 
          header[5] == 0x0A && 
          header[6] == 0x1A && 
          header[7] == 0x0A) {
        return true;
      }
      
      print('Invalid image format for file: $imagePath');
      return false;
    } catch (e) {
      print('Error checking image file: $e');
      return false;
    }
  }
}
