import 'package:shared_preferences/shared_preferences.dart';

class ProStatusManager {
  static final ProStatusManager _instance = ProStatusManager._internal();
  factory ProStatusManager() => _instance;
  ProStatusManager._internal();

  static const String _proStatusKey = 'is_pro_user';
  static const String _proExpiryKey = 'pro_expiry_date';
  
  bool _isProUser = false;
  DateTime? _proExpiryDate;

  bool get isProUser => _isProUser && !_isProExpired();
  
  bool _isProExpired() {
    if (_proExpiryDate == null) return true;
    return DateTime.now().isAfter(_proExpiryDate!);
  }

  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    _isProUser = prefs.getBool(_proStatusKey) ?? false;
    
    final expiryMillis = prefs.getInt(_proExpiryKey);
    if (expiryMillis != null) {
      _proExpiryDate = DateTime.fromMillisecondsSinceEpoch(expiryMillis);
    }
    
    print('Pro Status initialized: $_isProUser, Expiry: $_proExpiryDate');
  }

  Future<void> activateProUser({Duration? duration}) async {
    final prefs = await SharedPreferences.getInstance();
    
    _isProUser = true;
    await prefs.setBool(_proStatusKey, true);
    
    // Set expiry date (default 1 year if not specified)
    final expiry = DateTime.now().add(duration ?? const Duration(days: 365));
    _proExpiryDate = expiry;
    await prefs.setInt(_proExpiryKey, expiry.millisecondsSinceEpoch);
    
    print('Pro user activated until: $expiry');
  }

  Future<void> deactivateProUser() async {
    final prefs = await SharedPreferences.getInstance();
    
    _isProUser = false;
    _proExpiryDate = null;
    
    await prefs.setBool(_proStatusKey, false);
    await prefs.remove(_proExpiryKey);
    
    print('Pro user deactivated');
  }

  Future<void> activateTemporaryPro({Duration duration = const Duration(hours: 24)}) async {
    final prefs = await SharedPreferences.getInstance();
    
    _isProUser = true;
    await prefs.setBool(_proStatusKey, true);
    
    final expiry = DateTime.now().add(duration);
    _proExpiryDate = expiry;
    await prefs.setInt(_proExpiryKey, expiry.millisecondsSinceEpoch);
    
    print('Temporary pro activated until: $expiry');
  }

  String get proStatusText {
    if (!_isProUser) return 'Free User';
    if (_isProExpired()) return 'Pro Expired';
    
    final remaining = _proExpiryDate!.difference(DateTime.now());
    if (remaining.inDays > 0) {
      return 'Pro (${remaining.inDays} days left)';
    } else if (remaining.inHours > 0) {
      return 'Pro (${remaining.inHours} hours left)';
    } else {
      return 'Pro (expires soon)';
    }
  }
}
