import 'package:flutter/foundation.dart';

import 'package:razorpay_flutter/razorpay_flutter.dart';
import 'pro_status_manager.dart';

class RazorpayService {
  static final RazorpayService _instance = RazorpayService._internal();
  factory RazorpayService() => _instance;
  RazorpayService._internal();

  late Razorpay _razorpay;
  VoidCallback? _onSuccess;
  VoidCallback? _onError;

  // Test Razorpay credentials - Use working test keys
  static const String _keyId = 'rzp_live_ILgsfZCZoFIKMb'; // Test key
// Test secret (not used in frontend)

  bool _isInitialized = false;

  void initialize() {
    try {
      if (_isInitialized) {
        print('Razorpay already initialized');
        return;
      }

      _razorpay = Razorpay();
      _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
      _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
      _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
      _isInitialized = true;
      print('Razorpay initialized successfully');
    } catch (e) {
      print('Error initializing Razorpay: $e');
      _isInitialized = false;
    }
  }

  void dispose() {
    _razorpay.clear();
  }

  Future<void> startPayment({
    required String orderId,
    required double amount,
    required String description,
    required String userEmail,
    required String userPhone,
    required VoidCallback onSuccess,
    required VoidCallback onError,
  }) async {
    if (!_isInitialized) {
      if (kDebugMode) {
        print('Razorpay not initialized, initializing now...');
      }
      initialize();
      if (!_isInitialized) {
        if (kDebugMode) {
          print('Failed to initialize Razorpay');
        }
        onError();
        return Future.value();
      }
    }

    _onSuccess = onSuccess;
    _onError = onError;

    var options = {
      'key': _keyId,
      'amount': (amount * 100).toInt(), // Amount in paise
      'name': 'GPS Map Camera Pro',
      'description': description,
      'prefill': {
        'contact': userPhone,
        'email': userEmail,
      },
      'theme': {
        'color': '#2E7D32',
      },
      'method': {
        'netbanking': true,
        'card': true,
        'upi': true,
        'wallet': true,
      },
      'retry': {
        'enabled': true,
        'max_count': 3,
      },
    };

    try {
      print('Starting Razorpay payment with options: $options');

      // Add a small delay to ensure proper initialization
      Future.delayed(const Duration(milliseconds: 100), () {
        try {
          _razorpay.open(options);
          print('Razorpay payment dialog opened');
        } catch (e) {
          print('Error opening Razorpay dialog: $e');
          _onError?.call();
        }
      });

      // Add timeout check
      Future.delayed(const Duration(seconds: 10), () {
        if (_onSuccess != null && _onError != null) {
          print('Payment dialog timeout - no response received');
        }
      });

    } catch (e) {
      print('Error starting payment: $e');
      _onError?.call();
    }
  }

  void _handlePaymentSuccess(PaymentSuccessResponse response) {
    print('Payment Success: ${response.paymentId}');
    print('Order ID: ${response.orderId}');
    print('Signature: ${response.signature}');
    
    // Activate Pro user
    ProStatusManager().activateProUser();
    
    _onSuccess?.call();
  }

  void _handlePaymentError(PaymentFailureResponse response) {
    print('Payment Error: ${response.code} - ${response.message}');

    // Handle back button press (user cancelled)
    if (response.code == Razorpay.PAYMENT_CANCELLED) {
      print('Payment cancelled by user (back button pressed)');
    } else {
      print('Payment failed with error: ${response.message}');
    }

    _onError?.call();
  }

  void _handleExternalWallet(ExternalWalletResponse response) {
    print('External Wallet: ${response.walletName}');
  }

  // Predefined payment options
  static const Map<String, Map<String, dynamic>> paymentPlans = {
    'monthly': {
      'amount': 99.0,
      'description': 'GPS Map Camera Pro - Monthly',
      'duration': Duration(days: 30),
    },
    'yearly': {
      'amount': 999.0,
      'description': 'GPS Map Camera Pro - Yearly (Save 17%)',
      'duration': Duration(days: 365),
    },
    'lifetime': {
      'amount': 2999.0,
      'description': 'GPS Map Camera Pro - Lifetime',
      'duration': Duration(days: 36500), // 100 years
    },
  };

  Future<void> buyProSubscription({
    required String planType,
    required String userEmail,
    required String userPhone,
    required VoidCallback onSuccess,
    required VoidCallback onError,
  }) async {
    final plan = paymentPlans[planType];
    if (plan == null) {
      print('Invalid plan type: $planType');
      onError();
      return;
    }

    // Generate order ID (in real app, this should come from your backend)
    final orderId = 'order_${DateTime.now().millisecondsSinceEpoch}';

    await startPayment(
      orderId: orderId,
      amount: plan['amount'],
      description: plan['description'],
      userEmail: userEmail,
      userPhone: userPhone,
      onSuccess: () {
        // Activate pro with specific duration
        ProStatusManager().activateProUser(duration: plan['duration']);
        onSuccess();
      },
      onError: onError,
    );
  }
}
