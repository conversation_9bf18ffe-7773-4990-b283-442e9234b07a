import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ThemeManager extends ChangeNotifier {
  static final ThemeManager _instance = ThemeManager._internal();
  factory ThemeManager() => _instance;
  ThemeManager._internal();

  static const String _themeKey = 'selected_theme';
  String _currentTheme = 'default';

  String get currentTheme => _currentTheme;

  final Map<String, ThemeData> _themes = {
    'default': ThemeData(
      primarySwatch: Colors.teal,
      brightness: Brightness.light,
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
      ),
    ),
    'dark': ThemeData(
      primarySwatch: Colors.teal,
      brightness: Brightness.dark,
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
    ),
    'blue': ThemeData(
      primarySwatch: Colors.blue,
      brightness: Brightness.light,
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
    ),
    'purple': ThemeData(
      primarySwatch: Colors.purple,
      brightness: Brightness.light,
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
      ),
    ),
    'green': ThemeData(
      primarySwatch: Colors.green,
      brightness: Brightness.light,
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
      ),
    ),
    'orange': ThemeData(
      primarySwatch: Colors.orange,
      brightness: Brightness.light,
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
    ),
    'red': ThemeData(
      primarySwatch: Colors.red,
      brightness: Brightness.light,
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
      ),
    ),
    'pink': ThemeData(
      primarySwatch: Colors.pink,
      brightness: Brightness.light,
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.pink,
        foregroundColor: Colors.white,
      ),
    ),
  };

  ThemeData get currentThemeData => _themes[_currentTheme] ?? _themes['default']!;

  List<String> get availableThemes => _themes.keys.toList();

  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    _currentTheme = prefs.getString(_themeKey) ?? 'default';
    notifyListeners();
  }

  Future<void> setTheme(String themeName) async {
    if (_themes.containsKey(themeName)) {
      _currentTheme = themeName;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_themeKey, themeName);
      notifyListeners();
      print('Theme changed to: $themeName');
    }
  }

  Color getThemeColor(String themeName) {
    switch (themeName) {
      case 'default':
        return Colors.teal;
      case 'dark':
        return Colors.black;
      case 'blue':
        return Colors.blue;
      case 'purple':
        return Colors.purple;
      case 'green':
        return Colors.green;
      case 'orange':
        return Colors.orange;
      case 'red':
        return Colors.red;
      case 'pink':
        return Colors.pink;
      default:
        return Colors.teal;
    }
  }

  String getThemeDisplayName(String themeName) {
    switch (themeName) {
      case 'default':
        return 'Default Teal';
      case 'dark':
        return 'Dark Mode';
      case 'blue':
        return 'Ocean Blue';
      case 'purple':
        return 'Royal Purple';
      case 'green':
        return 'Nature Green';
      case 'orange':
        return 'Sunset Orange';
      case 'red':
        return 'Vibrant Red';
      case 'pink':
        return 'Cherry Pink';
      default:
        return 'Unknown';
    }
  }
}
