import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../tutorial_data.dart';
import '../../presentation/widgets/interactive_tutorial_overlay.dart';

class TutorialService {
  static final TutorialService _instance = TutorialService._internal();
  factory TutorialService() => _instance;
  TutorialService._internal();

  static const String _cameraTutorialKey = 'camera_tutorial_shown';
  static const String _galleryTutorialKey = 'gallery_tutorial_shown';
  static const String _mapTutorialKey = 'map_tutorial_shown';
  static const String _templatesTutorialKey = 'templates_tutorial_shown';

  Future<bool> shouldShowCameraTutorial() async {
    final prefs = await SharedPreferences.getInstance();
    return !(prefs.getBool(_cameraTutorialKey) ?? false);
  }

  Future<bool> shouldShowGalleryTutorial() async {
    final prefs = await SharedPreferences.getInstance();
    return !(prefs.getBool(_galleryTutorialKey) ?? false);
  }

  Future<bool> shouldShowMapTutorial() async {
    final prefs = await SharedPreferences.getInstance();
    return !(prefs.getBool(_mapTutorialKey) ?? false);
  }

  Future<bool> shouldShowTemplatesTutorial() async {
    final prefs = await SharedPreferences.getInstance();
    return !(prefs.getBool(_templatesTutorialKey) ?? false);
  }

  Future<void> markCameraTutorialShown() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_cameraTutorialKey, true);
  }

  Future<void> markGalleryTutorialShown() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_galleryTutorialKey, true);
  }

  Future<void> markMapTutorialShown() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_mapTutorialKey, true);
  }

  Future<void> markTemplatesTutorialShown() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_templatesTutorialKey, true);
  }

  Future<void> resetAllTutorials() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_cameraTutorialKey, false);
    await prefs.setBool(_galleryTutorialKey, false);
    await prefs.setBool(_mapTutorialKey, false);
    await prefs.setBool(_templatesTutorialKey, false);
  }

  void showCameraTutorial(BuildContext context) {
    _showTutorial(
      context,
      TutorialData.getCameraTutorialSteps(),
      () => markCameraTutorialShown(),
    );
  }

  void showGalleryTutorial(BuildContext context) {
    _showTutorial(
      context,
      TutorialData.getGalleryTutorialSteps(),
      () => markGalleryTutorialShown(),
    );
  }

  void showMapTutorial(BuildContext context) {
    _showTutorial(
      context,
      TutorialData.getMapTutorialSteps(),
      () => markMapTutorialShown(),
    );
  }



  void _showTutorial(
    BuildContext context,
    List<TutorialStep> steps,
    VoidCallback onComplete,
  ) {
    // Tutorial disabled - just call onComplete immediately
    onComplete();
  }

  // Helper method to show tutorial with delay (for better UX)
  void showTutorialWithDelay(
    BuildContext context,
    List<TutorialStep> steps,
    VoidCallback onComplete, {
    Duration delay = const Duration(milliseconds: 500),
  }) {
    Future.delayed(delay, () {
      if (context.mounted) {
        _showTutorial(context, steps, onComplete);
      }
    });
  }
}
