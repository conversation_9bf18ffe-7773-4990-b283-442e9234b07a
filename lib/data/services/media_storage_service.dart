import 'dart:io';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path/path.dart' as path;

class MediaStorageService {
  static const MethodChannel _channel = MethodChannel('com.example.gpsmapcamera/media_storage');

  /// Save an image or video to the device gallery
  static Future<bool> saveImageToGallery(String filePath) async {
    try {
      // Check for storage permissions (Android 13+ uses different permissions)
      bool hasPermission = await _checkStoragePermission();
      if (!hasPermission) {
        print('Storage permission denied');
        return false;
      }

      // Check if it's a video file
      final isVideo = filePath.toLowerCase().endsWith('.mp4') ||
                      filePath.toLowerCase().endsWith('.mov') ||
                      filePath.toLowerCase().endsWith('.avi');

      // Try multiple approaches to save to device gallery
      try {
        final File file = File(filePath);
        if (!await file.exists()) {
          print('File does not exist: $filePath');
          return false;
        }

        bool success = false;

        // Method 1: Try using method channel first (most reliable for Android)
        try {
          print('Attempting to save ${isVideo ? 'video' : 'image'} using method channel: $filePath');
          final result = await _channel.invokeMethod('saveImageToGallery', {
            'imagePath': filePath,
            'quality': 100,
            'isVideo': isVideo,
          });

          if (result == true) {
            print('Successfully saved using method channel');
            return true;
          } else {
            print('Method channel returned false');
          }
        } catch (e) {
          print('Method channel failed: $e');
        }

        // Method 2: Try to save to public directories as fallback
        try {
          success = await _saveToPublicDirectory(file, isVideo);
          if (success) {
            print('Successfully saved using public directory method');
            return true;
          }
        } catch (e) {
          print('Public directory method failed: $e');
        }

        // Method 3: Copy to Downloads folder as fallback
        try {
          success = await _saveToDownloads(file, isVideo);
          if (success) {
            print('Successfully saved to Downloads folder');
            return true;
          }
        } catch (e) {
          print('Downloads folder method failed: $e');
        }

        return false;
      } catch (e) {
        print('All save methods failed: $e');
        
        // Fallback method - copy file to public directory
        final file = File(filePath);
        if (await file.exists()) {
          // Create a copy in the Pictures or Movies directory
          final directory = await getExternalStorageDirectory();
          if (directory == null) return false;
          
          // Navigate up to find the Pictures/Movies directory
          String newPath = "";
          List<String> paths = directory.path.split("/");
          for (int x = 1; x < paths.length; x++) {
            String folder = paths[x];
            if (folder != "Android") {
              newPath += "/" + folder;
            } else {
              break;
            }
          }
          
          final String folderName = isVideo ? "Movies" : "Pictures";
          newPath = newPath + "/$folderName/GPSMapCamera";
          
          final saveDir = Directory(newPath);
          if (!await saveDir.exists()) {
            await saveDir.create(recursive: true);
          }
          
          // Copy the file
          final String fileName = path.basename(filePath);
          final String savePath = path.join(newPath, fileName);
          
          await file.copy(savePath);
          return true;
        }
        return false;
      }
    } catch (e) {
      print('Error saving media to gallery: $e');
      return false;
    }
  }

  /// Save a video to the device gallery
  static Future<bool> saveVideoToGallery(String videoPath) async {
    // Use the same method as saveImageToGallery since it handles both images and videos
    return await saveImageToGallery(videoPath);
  }

  /// Test method channel connectivity
  static Future<bool> testMethodChannel() async {
    try {
      final result = await _channel.invokeMethod('testConnection');
      print('Method channel test result: $result');
      return result == true;
    } catch (e) {
      print('Method channel test failed: $e');
      return false;
    }
  }

  /// Save to public directory (DCIM/Camera or Movies)
  static Future<bool> _saveToPublicDirectory(File file, bool isVideo) async {
    try {
      // Get external storage directory
      final Directory? externalDir = await getExternalStorageDirectory();
      if (externalDir == null) return false;

      // Navigate to public directory
      List<String> pathSegments = externalDir.path.split('/');
      int androidIndex = pathSegments.indexOf('Android');
      if (androidIndex <= 0) return false;

      String publicPath = pathSegments.sublist(0, androidIndex).join('/');

      // Use standard Android directories
      String targetPath;
      if (isVideo) {
        targetPath = '$publicPath/Movies/GPSMapCamera';
      } else {
        targetPath = '$publicPath/DCIM/GPSMapCamera';
      }

      final Directory targetDir = Directory(targetPath);
      if (!await targetDir.exists()) {
        await targetDir.create(recursive: true);
      }

      // Copy file with timestamp to avoid conflicts
      final String fileName = path.basename(file.path);
      final String targetFilePath = path.join(targetPath, fileName);
      await file.copy(targetFilePath);

      print('Saved to public directory: $targetFilePath');
      return true;
    } catch (e) {
      print('Public directory save failed: $e');
      return false;
    }
  }

  /// Save to Downloads folder as fallback
  static Future<bool> _saveToDownloads(File file, bool isVideo) async {
    try {
      final Directory? externalDir = await getExternalStorageDirectory();
      if (externalDir == null) return false;

      List<String> pathSegments = externalDir.path.split('/');
      int androidIndex = pathSegments.indexOf('Android');
      if (androidIndex <= 0) return false;

      String publicPath = pathSegments.sublist(0, androidIndex).join('/');
      String targetPath = '$publicPath/Download/GPSMapCamera';

      final Directory targetDir = Directory(targetPath);
      if (!await targetDir.exists()) {
        await targetDir.create(recursive: true);
      }

      final String fileName = path.basename(file.path);
      final String targetFilePath = path.join(targetPath, fileName);
      await file.copy(targetFilePath);

      print('Saved to Downloads: $targetFilePath');
      return true;
    } catch (e) {
      print('Downloads save failed: $e');
      return false;
    }
  }

  /// Check storage permissions for different Android versions
  static Future<bool> _checkStoragePermission() async {
    try {
      // For Android 13+ (API 33+), we need different permissions
      if (Platform.isAndroid) {
        // Try photos permission first (Android 13+)
        var photosPermission = await Permission.photos.status;
        if (photosPermission.isGranted) {
          return true;
        }

        // Request photos permission
        photosPermission = await Permission.photos.request();
        if (photosPermission.isGranted) {
          return true;
        }

        // Fallback to storage permission for older Android versions
        var storagePermission = await Permission.storage.status;
        if (storagePermission.isGranted) {
          return true;
        }

        storagePermission = await Permission.storage.request();
        return storagePermission.isGranted;
      }

      // For iOS, photos permission is sufficient
      if (Platform.isIOS) {
        var photosPermission = await Permission.photos.status;
        if (photosPermission.isGranted) {
          return true;
        }

        photosPermission = await Permission.photos.request();
        return photosPermission.isGranted;
      }

      return false;
    } catch (e) {
      print('Error checking storage permission: $e');
      return false;
    }
  }
}