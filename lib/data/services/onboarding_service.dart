import 'package:shared_preferences/shared_preferences.dart';

class OnboardingService {
  static final OnboardingService _instance = OnboardingService._internal();
  factory OnboardingService() => _instance;
  OnboardingService._internal();

  static const String _firstLaunchKey = 'is_first_launch';
  static const String _onboardingCompletedKey = 'onboarding_completed';
  static const String _onboardingSkippedKey = 'onboarding_skipped';

  bool _isFirstLaunch = true;
  bool _onboardingCompleted = false;
  bool _onboardingSkipped = false;

  bool get isFirstLaunch => _isFirstLaunch;
  bool get onboardingCompleted => _onboardingCompleted;
  bool get onboardingSkipped => _onboardingSkipped;
  bool get shouldShowOnboarding => _isFirstLaunch && !_onboardingCompleted && !_onboardingSkipped;

  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    _isFirstLaunch = prefs.getBool(_firstLaunchKey) ?? true;
    _onboardingCompleted = prefs.getBool(_onboardingCompletedKey) ?? false;
    _onboardingSkipped = prefs.getBool(_onboardingSkippedKey) ?? false;

    print('Onboarding Service initialized: First Launch: $_isFirstLaunch, Completed: $_onboardingCompleted, Skipped: $_onboardingSkipped');
  }

  Future<void> markFirstLaunchComplete() async {
    final prefs = await SharedPreferences.getInstance();
    _isFirstLaunch = false;
    await prefs.setBool(_firstLaunchKey, false);
    print('First launch marked as complete');
  }

  Future<void> markOnboardingComplete() async {
    final prefs = await SharedPreferences.getInstance();
    _onboardingCompleted = true;
    await prefs.setBool(_onboardingCompletedKey, true);
    await markFirstLaunchComplete();
    print('Onboarding marked as complete');
  }

  Future<void> markOnboardingSkipped() async {
    final prefs = await SharedPreferences.getInstance();
    _onboardingSkipped = true;
    await prefs.setBool(_onboardingSkippedKey, true);
    await markFirstLaunchComplete();
    print('Onboarding skipped - will not show again');
  }

  Future<void> resetOnboarding() async {
    final prefs = await SharedPreferences.getInstance();
    _isFirstLaunch = true;
    _onboardingCompleted = false;
    _onboardingSkipped = false;
    await prefs.setBool(_firstLaunchKey, true);
    await prefs.setBool(_onboardingCompletedKey, false);
    await prefs.setBool(_onboardingSkippedKey, false);
    print('Onboarding reset - will show on next launch');
  }
}
