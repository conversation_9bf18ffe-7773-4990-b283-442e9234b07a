import 'dart:io';

import 'package:google_mobile_ads/google_mobile_ads.dart';

class AdMobService {
  // Your ad unit IDs
  static const String _appOpenAdUnitId = 'ca-app-pub-1734408835750695/1249830420'; // App Open Ad
  static const String _bannerAdUnitId = 'ca-app-pub-1734408835750695~8222432888'; // Banner Ad
  static const String _rewardedInterstitialAdUnitId = 'ca-app-pub-1734408835750695/1249830420'; // For theme changes only

  // Test ad unit IDs for development
  static final String _testBannerAdUnitId = Platform.isAndroid
      ? 'ca-app-pub-3940256099942544/6300978111'
      : 'ca-app-pub-3940256099942544/2934735716';

  static final String _testAppOpenAdUnitId = Platform.isAndroid
      ? 'ca-app-pub-3940256099942544/9257395921'
      : 'ca-app-pub-3940256099942544/5575463023';

  static final String _testRewardedInterstitialAdUnitId = Platform.isAndroid
      ? 'ca-app-pub-3940256099942544/5354046379'
      : 'ca-app-pub-3940256099942544/6978759866';

  // Get banner ad unit ID
  static String get bannerAdUnitId {
    return const bool.fromEnvironment('dart.vm.product')
        ? _bannerAdUnitId
        : _testBannerAdUnitId;
  }

  // Get app open ad unit ID
  static String get appOpenAdUnitId {
    return const bool.fromEnvironment('dart.vm.product')
        ? _appOpenAdUnitId
        : _testAppOpenAdUnitId;
  }

  // Get rewarded interstitial ad unit ID
  static String get rewardedInterstitialAdUnitId {
    return const bool.fromEnvironment('dart.vm.product')
        ? _rewardedInterstitialAdUnitId
        : _testRewardedInterstitialAdUnitId;
  }
  
  static Future<void> initialize() async {
    await MobileAds.instance.initialize();
  }
  
  static BannerAd createBannerAd({
    required AdSize adSize,
    required void Function(Ad, LoadAdError) onAdFailedToLoad,
    required void Function(Ad) onAdLoaded,
  }) {
    return BannerAd(
      adUnitId: bannerAdUnitId,
      size: adSize,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: onAdLoaded,
        onAdFailedToLoad: onAdFailedToLoad,
        onAdOpened: (Ad ad) => print('Banner ad opened.'),
        onAdClosed: (Ad ad) => print('Banner ad closed.'),
        onAdImpression: (Ad ad) => print('Banner ad impression.'),
      ),
    );
  }

  static void loadAppOpenAd({
    required void Function(AppOpenAd) onAdLoaded,
    required void Function(LoadAdError) onAdFailedToLoad,
  }) {
    AppOpenAd.load(
      adUnitId: appOpenAdUnitId,
      request: const AdRequest(),
      adLoadCallback: AppOpenAdLoadCallback(
        onAdLoaded: onAdLoaded,
        onAdFailedToLoad: (LoadAdError error) {
          print('App open ad failed to load: $error');
          onAdFailedToLoad(error);
        },
      ),
    );
  }

  static void loadRewardedInterstitialAd({
    required void Function(RewardedInterstitialAd) onAdLoaded,
    required void Function(LoadAdError) onAdFailedToLoad,
  }) {
    RewardedInterstitialAd.load(
      adUnitId: rewardedInterstitialAdUnitId,
      request: const AdRequest(),
      rewardedInterstitialAdLoadCallback: RewardedInterstitialAdLoadCallback(
        onAdLoaded: onAdLoaded,
        onAdFailedToLoad: (LoadAdError error) {
          print('Rewarded interstitial ad failed to load: $error');
          onAdFailedToLoad(error);
        },
      ),
    );
  }
}
