import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../domain/models/user_settings_model.dart';

class UserSettingsService {
  static const String _settingsKey = 'user_settings';
  
  static UserSettingsService? _instance;
  static UserSettingsService get instance {
    _instance ??= UserSettingsService._internal();
    return _instance!;
  }
  
  UserSettingsService._internal();

  /// Load user settings from SharedPreferences
  Future<UserSettingsModel> loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_settingsKey);
      
      if (settingsJson != null) {
        final Map<String, dynamic> settingsMap = json.decode(settingsJson);
        return UserSettingsModel.fromJson(settingsMap);
      }
    } catch (e) {
      print('Error loading user settings: $e');
    }
    
    // Return default settings if loading fails
    return const UserSettingsModel();
  }

  /// Save user settings to SharedPreferences
  Future<bool> saveSettings(UserSettingsModel settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = json.encode(settings.toJson());
      return await prefs.setString(_settingsKey, settingsJson);
    } catch (e) {
      print('Error saving user settings: $e');
      return false;
    }
  }

  /// Clear all user settings
  Future<bool> clearSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.remove(_settingsKey);
    } catch (e) {
      print('Error clearing user settings: $e');
      return false;
    }
  }

  /// Get specific setting value
  Future<String> getLanguage() async {
    final settings = await loadSettings();
    return settings.language;
  }

  Future<String> getAreaUnit() async {
    final settings = await loadSettings();
    return settings.areaUnit;
  }

  Future<String> getDistanceUnit() async {
    final settings = await loadSettings();
    return settings.distanceUnit;
  }

  Future<String> getPerimeterUnit() async {
    final settings = await loadSettings();
    return settings.perimeterUnit;
  }

  /// Update specific setting
  Future<bool> updateLanguage(String language) async {
    final currentSettings = await loadSettings();
    final updatedSettings = currentSettings.copyWith(language: language);
    return await saveSettings(updatedSettings);
  }

  Future<bool> updateAreaUnit(String areaUnit) async {
    final currentSettings = await loadSettings();
    final updatedSettings = currentSettings.copyWith(areaUnit: areaUnit);
    return await saveSettings(updatedSettings);
  }

  Future<bool> updateDistanceUnit(String distanceUnit) async {
    final currentSettings = await loadSettings();
    final updatedSettings = currentSettings.copyWith(distanceUnit: distanceUnit);
    return await saveSettings(updatedSettings);
  }

  Future<bool> updatePerimeterUnit(String perimeterUnit) async {
    final currentSettings = await loadSettings();
    final updatedSettings = currentSettings.copyWith(perimeterUnit: perimeterUnit);
    return await saveSettings(updatedSettings);
  }

  /// Utility methods for unit conversion
  static double convertArea(double value, String fromUnit, String toUnit) {
    // Convert to square meters first
    double valueInSquareMeters;
    switch (fromUnit) {
      case 'Square Meters':
        valueInSquareMeters = value;
        break;
      case 'Square Feet':
        valueInSquareMeters = value * 0.092903;
        break;
      case 'Acres':
        valueInSquareMeters = value * 4046.86;
        break;
      case 'Hectares':
        valueInSquareMeters = value * 10000;
        break;
      case 'Square Kilometers':
        valueInSquareMeters = value * 1000000;
        break;
      case 'Square Miles':
        valueInSquareMeters = value * 2589988.11;
        break;
      default:
        valueInSquareMeters = value;
    }

    // Convert from square meters to target unit
    switch (toUnit) {
      case 'Square Meters':
        return valueInSquareMeters;
      case 'Square Feet':
        return valueInSquareMeters / 0.092903;
      case 'Acres':
        return valueInSquareMeters / 4046.86;
      case 'Hectares':
        return valueInSquareMeters / 10000;
      case 'Square Kilometers':
        return valueInSquareMeters / 1000000;
      case 'Square Miles':
        return valueInSquareMeters / 2589988.11;
      default:
        return valueInSquareMeters;
    }
  }

  static double convertDistance(double value, String fromUnit, String toUnit) {
    // Convert to meters first
    double valueInMeters;
    switch (fromUnit) {
      case 'Meters':
        valueInMeters = value;
        break;
      case 'Kilometers':
        valueInMeters = value * 1000;
        break;
      case 'Feet':
        valueInMeters = value * 0.3048;
        break;
      case 'Miles':
        valueInMeters = value * 1609.34;
        break;
      case 'Yards':
        valueInMeters = value * 0.9144;
        break;
      case 'Centimeters':
        valueInMeters = value * 0.01;
        break;
      default:
        valueInMeters = value;
    }

    // Convert from meters to target unit
    switch (toUnit) {
      case 'Meters':
        return valueInMeters;
      case 'Kilometers':
        return valueInMeters / 1000;
      case 'Feet':
        return valueInMeters / 0.3048;
      case 'Miles':
        return valueInMeters / 1609.34;
      case 'Yards':
        return valueInMeters / 0.9144;
      case 'Centimeters':
        return valueInMeters / 0.01;
      default:
        return valueInMeters;
    }
  }
}
