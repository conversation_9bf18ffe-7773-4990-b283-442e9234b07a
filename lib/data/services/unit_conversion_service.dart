
class UnitConversionService {
  static UnitConversionService? _instance;
  static UnitConversionService get instance {
    _instance ??= UnitConversionService._internal();
    return _instance!;
  }
  
  UnitConversionService._internal();

  // Area conversion factors to square meters
  static const Map<String, double> _areaToSquareMeters = {
    'Square Meters': 1.0,
    'Square Feet': 0.092903,
    'Acres': 4046.86,
    'Hectares': 10000.0,
    'Square Kilometers': 1000000.0,
    'Square Miles': 2589988.11,
  };

  // Distance conversion factors to meters
  static const Map<String, double> _distanceToMeters = {
    'Meters': 1.0,
    'Kilometers': 1000.0,
    'Feet': 0.3048,
    'Miles': 1609.34,
    'Yards': 0.9144,
    'Centimeters': 0.01,
  };

  /// Convert area from one unit to another
  double convertArea(double value, String fromUnit, String toUnit) {
    if (fromUnit == toUnit) return value;
    
    final fromFactor = _areaToSquareMeters[fromUnit];
    final toFactor = _areaToSquareMeters[toUnit];
    
    if (fromFactor == null || toFactor == null) {
      print('Warning: Unknown area unit. From: $fromUnit, To: $toUnit');
      return value;
    }
    
    // Convert to square meters first, then to target unit
    final valueInSquareMeters = value * fromFactor;
    return valueInSquareMeters / toFactor;
  }

  /// Convert distance from one unit to another
  double convertDistance(double value, String fromUnit, String toUnit) {
    if (fromUnit == toUnit) return value;
    
    final fromFactor = _distanceToMeters[fromUnit];
    final toFactor = _distanceToMeters[toUnit];
    
    if (fromFactor == null || toFactor == null) {
      print('Warning: Unknown distance unit. From: $fromUnit, To: $toUnit');
      return value;
    }
    
    // Convert to meters first, then to target unit
    final valueInMeters = value * fromFactor;
    return valueInMeters / toFactor;
  }

  /// Format area value with appropriate unit
  String formatArea(double sqMeters, String targetUnit) {
    final convertedValue = convertArea(sqMeters, 'Square Meters', targetUnit);
    
    switch (targetUnit) {
      case 'Square Meters':
        if (convertedValue < 1) {
          return '${convertedValue.toStringAsFixed(3)} m²';
        } else if (convertedValue < 10000) {
          return '${convertedValue.toStringAsFixed(1)} m²';
        } else {
          return '${convertedValue.toStringAsFixed(0)} m²';
        }
      case 'Square Feet':
        return '${convertedValue.toStringAsFixed(1)} ft²';
      case 'Acres':
        return '${convertedValue.toStringAsFixed(2)} acres';
      case 'Hectares':
        return '${convertedValue.toStringAsFixed(2)} ha';
      case 'Square Kilometers':
        return '${convertedValue.toStringAsFixed(3)} km²';
      case 'Square Miles':
        return '${convertedValue.toStringAsFixed(3)} mi²';
      default:
        return '${convertedValue.toStringAsFixed(2)} $targetUnit';
    }
  }

  /// Format distance value with appropriate unit
  String formatDistance(double meters, String targetUnit) {
    final convertedValue = convertDistance(meters, 'Meters', targetUnit);
    
    switch (targetUnit) {
      case 'Meters':
        if (convertedValue < 1) {
          return '${convertedValue.toStringAsFixed(2)}m'; // Show 0.5m precision for sub-meter
        } else if (convertedValue < 1000) {
          return '${convertedValue.toStringAsFixed(1)}m'; // Show 0.1m precision for meter range
        } else {
          return '${(convertedValue / 1000).toStringAsFixed(2)}km';
        }
      case 'Kilometers':
        return '${convertedValue.toStringAsFixed(2)}km';
      case 'Feet':
        if (convertedValue < 5280) {
          return '${convertedValue.toStringAsFixed(1)}ft';
        } else {
          return '${(convertedValue / 5280).toStringAsFixed(2)}mi';
        }
      case 'Miles':
        return '${convertedValue.toStringAsFixed(2)}mi';
      case 'Yards':
        return '${convertedValue.toStringAsFixed(1)}yd';
      case 'Centimeters':
        if (convertedValue < 10) {
          return '${convertedValue.toStringAsFixed(1)}cm'; // Show 1mm precision for small measurements
        } else {
          return '${convertedValue.toStringAsFixed(0)}cm';
        }
      default:
        return '${convertedValue.toStringAsFixed(2)} $targetUnit';
    }
  }

  /// Get unit abbreviation
  String getUnitAbbreviation(String unit) {
    switch (unit) {
      case 'Square Meters':
        return 'm²';
      case 'Square Feet':
        return 'ft²';
      case 'Acres':
        return 'acres';
      case 'Hectares':
        return 'ha';
      case 'Square Kilometers':
        return 'km²';
      case 'Square Miles':
        return 'mi²';
      case 'Meters':
        return 'm';
      case 'Kilometers':
        return 'km';
      case 'Feet':
        return 'ft';
      case 'Miles':
        return 'mi';
      case 'Yards':
        return 'yd';
      case 'Centimeters':
        return 'cm';
      default:
        return unit;
    }
  }

  /// Get all supported area units
  List<String> getSupportedAreaUnits() {
    return _areaToSquareMeters.keys.toList();
  }

  /// Get all supported distance units
  List<String> getSupportedDistanceUnits() {
    return _distanceToMeters.keys.toList();
  }

  /// Check if unit is supported for area
  bool isAreaUnitSupported(String unit) {
    return _areaToSquareMeters.containsKey(unit);
  }

  /// Check if unit is supported for distance
  bool isDistanceUnitSupported(String unit) {
    return _distanceToMeters.containsKey(unit);
  }

  /// Convert area with smart formatting (shows multiple units for large areas)
  String formatAreaSmart(double sqMeters, String primaryUnit) {
    final convertedValue = convertArea(sqMeters, 'Square Meters', primaryUnit);
    
    if (primaryUnit == 'Square Meters' && sqMeters >= 10000) {
      // Show both hectares and acres for large areas
      final hectares = convertArea(sqMeters, 'Square Meters', 'Hectares');
      final acres = convertArea(sqMeters, 'Square Meters', 'Acres');
      return '${hectares.toStringAsFixed(2)} ha (${acres.toStringAsFixed(2)} acres)';
    }
    
    return formatArea(sqMeters, primaryUnit);
  }

  /// Convert distance with smart formatting (auto-switches to larger units)
  String formatDistanceSmart(double meters, String primaryUnit) {
    if (primaryUnit == 'Meters' && meters >= 1000) {
      return '${(meters / 1000).toStringAsFixed(2)}km';
    } else if (primaryUnit == 'Feet' && meters >= 1609.34) {
      return '${(meters / 1609.34).toStringAsFixed(2)}mi';
    }
    
    return formatDistance(meters, primaryUnit);
  }
}
