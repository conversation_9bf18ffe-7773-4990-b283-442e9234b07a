import 'package:camera/camera.dart';

import '../../domain/entities/location_entity.dart';
import '../../domain/entities/photo_entity.dart';
import '../../domain/repositories/camera_repository.dart';
import '../datasources/camera_data_source.dart';
import '../datasources/location_data_source.dart';

class CameraRepositoryImpl implements CameraRepository {
    final CameraDatasource datasource;
    final CameraDatasource cameraDatasource;
    final LocationDatasource locationDatasource;

    CameraRepositoryImpl(this.cameraDatasource, this.locationDatasource,this.datasource);

    @override
    Future<PhotoEntity> capturePhoto() async {
        final location = await locationDatasource.getCurrentLocation();
        final path = await cameraDatasource.takePicture();
        return PhotoEntity(
            path: path,
            location: LocationEntity(latitude: location.latitude, longitude: location.longitude, timestamp: DateTime.now()), timestamp:  DateTime.now(), id: '${DateTime.now().millisecondsSinceEpoch}',
        );
    }
    Future<List<CameraDescription>> getAvailableCameras() {
        return datasource.getAvailableCameras();
    }
}
