class LocationEntity {
  final double latitude;
  final double longitude;
  final DateTime timestamp;
  final String? address;

  LocationEntity({
    required this.latitude,
    required this.longitude,
    required this.timestamp,
    this.address,
  });

  LocationEntity copyWith({
    double? latitude,
    double? longitude,
    DateTime? timestamp,
    String? address,
  }) {
    return LocationEntity(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      timestamp: timestamp ?? this.timestamp,
      address: address ?? this.address,
    );
  }
}