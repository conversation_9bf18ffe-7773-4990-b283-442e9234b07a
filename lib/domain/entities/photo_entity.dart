import 'location_entity.dart';

class PhotoEntity {
  final String id;
  final String path;
  final String? processedPath;
  final LocationEntity? location;
  final String? address;
  final DateTime timestamp;
  final bool isVideo;
  final Duration? duration;

  PhotoEntity({
    required this.id,
    required this.path,
    this.processedPath,
    this.location,
    this.address,
    required this.timestamp,
    this.isVideo = false,
    this.duration,
  });

  PhotoEntity copyWith({
    String? id,
    String? path,
    String? processedPath,
    LocationEntity? location,
    String? address,
    DateTime? timestamp,
    bool? isVideo,
    Duration? duration,
  }) {
    return PhotoEntity(
      id: id ?? this.id,
      path: path ?? this.path,
      processedPath: processedPath ?? this.processedPath,
      location: location ?? this.location,
      address: address ?? this.address,
      timestamp: timestamp ?? this.timestamp,
      isVideo: isVideo ?? this.isVideo,
      duration: duration ?? this.duration,
    );
  }
}
