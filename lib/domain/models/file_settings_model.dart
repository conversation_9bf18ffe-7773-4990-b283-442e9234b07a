class FileSettingsModel {
  final bool includeDateTime;
  final bool includeSequenceNumber;
  final String customPrefix;
  final bool includeTimeZone;
  final bool includePlusCode;
  final bool isPro;
  final String selectedTemplate;

  FileSettingsModel({
    this.includeDateTime = true,
    this.includeSequenceNumber = false,
    this.customPrefix = 'GPSMapCamera',
    this.includeTimeZone = false,
    this.includePlusCode = false,
    this.isPro = false,
    this.selectedTemplate = 'default',
  });

  FileSettingsModel copyWith({
    bool? includeDateTime,
    bool? includeSequenceNumber,
    String? customPrefix,
    bool? includeTimeZone,
    bool? includePlusCode,
    bool? isPro,
    String? selectedTemplate,
  }) {
    return FileSettingsModel(
      includeDateTime: includeDateTime ?? this.includeDateTime,
      includeSequenceNumber: includeSequenceNumber ?? this.includeSequenceNumber,
      customPrefix: customPrefix ?? this.customPrefix,
      includeTimeZone: includeTimeZone ?? this.includeTimeZone,
      includePlusCode: includePlusCode ?? this.includePlusCode,
      isPro: isPro ?? this.isPro,
      selectedTemplate: selectedTemplate ?? this.selectedTemplate,
    );
  }
}