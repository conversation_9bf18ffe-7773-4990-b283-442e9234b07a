import 'package:flutter/material.dart';

class UserSettingsModel {
  final String language;
  final String languageCode;
  final String areaUnit;
  final String distanceUnit;
  final String perimeterUnit;

  const UserSettingsModel({
    this.language = 'English',
    this.languageCode = 'en',
    this.areaUnit = 'Square Meters',
    this.distanceUnit = 'Meters',
    this.perimeterUnit = 'Meters',
  });

  UserSettingsModel copyWith({
    String? language,
    String? languageCode,
    String? areaUnit,
    String? distanceUnit,
    String? perimeterUnit,
  }) {
    return UserSettingsModel(
      language: language ?? this.language,
      languageCode: languageCode ?? this.languageCode,
      areaUnit: areaUnit ?? this.areaUnit,
      distanceUnit: distanceUnit ?? this.distanceUnit,
      perimeterUnit: perimeterUnit ?? this.perimeterUnit,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'language': language,
      'languageCode': languageCode,
      'areaUnit': areaUnit,
      'distanceUnit': distanceUnit,
      'perimeterUnit': perimeterUnit,
    };
  }

  factory UserSettingsModel.fromJson(Map<String, dynamic> json) {
    return UserSettingsModel(
      language: json['language'] ?? 'English',
      languageCode: json['languageCode'] ?? 'en',
      areaUnit: json['areaUnit'] ?? 'Square Meters',
      distanceUnit: json['distanceUnit'] ?? 'Meters',
      perimeterUnit: json['perimeterUnit'] ?? 'Meters',
    );
  }

  /// Get locale from language code
  Locale get locale => Locale(languageCode, '');

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserSettingsModel &&
        other.language == language &&
        other.languageCode == languageCode &&
        other.areaUnit == areaUnit &&
        other.distanceUnit == distanceUnit &&
        other.perimeterUnit == perimeterUnit;
  }

  @override
  int get hashCode {
    return language.hashCode ^
        languageCode.hashCode ^
        areaUnit.hashCode ^
        distanceUnit.hashCode ^
        perimeterUnit.hashCode;
  }

  @override
  String toString() {
    return 'UserSettingsModel(language: $language, languageCode: $languageCode, areaUnit: $areaUnit, distanceUnit: $distanceUnit, perimeterUnit: $perimeterUnit)';
  }
}
