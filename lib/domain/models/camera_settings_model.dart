import 'package:camera/camera.dart';

enum CameraMode { photo, video, portrait, night, pro }
enum GridType { none, rule_of_thirds, square, golden_ratio }
enum TimerMode { off, three_seconds, five_seconds, ten_seconds }
enum ImageQuality { low, medium, high, ultra }
enum VideoQuality { sd_480p, hd_720p, fhd_1080p, uhd_4k }
enum FocusMode { auto, manual, continuous, single }
enum ExposureMode { auto, manual, spot, center, matrix }
enum WhiteBalanceMode { auto, daylight, cloudy, tungsten, fluorescent, manual }

class CameraSettingsModel {
  // Basic Settings
  final CameraMode cameraMode;
  final bool isGridEnabled;
  final GridType gridType;
  final TimerMode timerMode;
  final bool isLevelEnabled;
  final bool isHistogramEnabled;
  
  // Image/Video Quality
  final ImageQuality imageQuality;
  final VideoQuality videoQuality;
  final ResolutionPreset resolutionPreset;
  
  // Manual Controls
  final FocusMode focusMode;
  final ExposureMode exposureMode;
  final WhiteBalanceMode whiteBalanceMode;
  final double exposureCompensation; // -2.0 to +2.0
  final double manualFocus; // 0.0 to 1.0
  final int iso; // 100 to 3200
  final double shutterSpeed; // 1/4000 to 30 seconds
  final int whiteBalanceTemperature; // 2000K to 8000K
  
  // Advanced Features
  final bool isRawEnabled;
  final bool isHdrEnabled;
  final bool isImageStabilizationEnabled;
  final bool isNoiseReductionEnabled;
  final bool isFaceDetectionEnabled;
  final bool isSmileDetectionEnabled;
  final bool isLocationTaggingEnabled;
  
  // Video Specific
  final bool isVideoStabilizationEnabled;
  final int videoFrameRate; // 24, 30, 60, 120 fps
  final bool isSlowMotionEnabled;
  final bool isTimelapseEnabled;
  final double timelapseInterval; // seconds between frames
  
  // UI Settings
  final bool isVolumeButtonsAsShutter;
  final bool isQuickSettingsEnabled;
  final bool isTouchToFocusEnabled;
  final bool isZoomGesturesEnabled;
  final bool isSoundEnabled;
  
  const CameraSettingsModel({
    // Basic Settings
    this.cameraMode = CameraMode.photo,
    this.isGridEnabled = false,
    this.gridType = GridType.rule_of_thirds,
    this.timerMode = TimerMode.off,
    this.isLevelEnabled = false,
    this.isHistogramEnabled = false,
    
    // Image/Video Quality
    this.imageQuality = ImageQuality.high,
    this.videoQuality = VideoQuality.fhd_1080p,
    this.resolutionPreset = ResolutionPreset.high,
    
    // Manual Controls
    this.focusMode = FocusMode.auto,
    this.exposureMode = ExposureMode.auto,
    this.whiteBalanceMode = WhiteBalanceMode.auto,
    this.exposureCompensation = 0.0,
    this.manualFocus = 0.5,
    this.iso = 100,
    this.shutterSpeed = 1/60,
    this.whiteBalanceTemperature = 5500,
    
    // Advanced Features
    this.isRawEnabled = false,
    this.isHdrEnabled = false,
    this.isImageStabilizationEnabled = true,
    this.isNoiseReductionEnabled = true,
    this.isFaceDetectionEnabled = false,
    this.isSmileDetectionEnabled = false,
    this.isLocationTaggingEnabled = true,
    
    // Video Specific
    this.isVideoStabilizationEnabled = true,
    this.videoFrameRate = 30,
    this.isSlowMotionEnabled = false,
    this.isTimelapseEnabled = false,
    this.timelapseInterval = 1.0,
    
    // UI Settings
    this.isVolumeButtonsAsShutter = true,
    this.isQuickSettingsEnabled = true,
    this.isTouchToFocusEnabled = true,
    this.isZoomGesturesEnabled = true,
    this.isSoundEnabled = true,
  });

  CameraSettingsModel copyWith({
    CameraMode? cameraMode,
    bool? isGridEnabled,
    GridType? gridType,
    TimerMode? timerMode,
    bool? isLevelEnabled,
    bool? isHistogramEnabled,
    ImageQuality? imageQuality,
    VideoQuality? videoQuality,
    ResolutionPreset? resolutionPreset,
    FocusMode? focusMode,
    ExposureMode? exposureMode,
    WhiteBalanceMode? whiteBalanceMode,
    double? exposureCompensation,
    double? manualFocus,
    int? iso,
    double? shutterSpeed,
    int? whiteBalanceTemperature,
    bool? isRawEnabled,
    bool? isHdrEnabled,
    bool? isImageStabilizationEnabled,
    bool? isNoiseReductionEnabled,
    bool? isFaceDetectionEnabled,
    bool? isSmileDetectionEnabled,
    bool? isLocationTaggingEnabled,
    bool? isVideoStabilizationEnabled,
    int? videoFrameRate,
    bool? isSlowMotionEnabled,
    bool? isTimelapseEnabled,
    double? timelapseInterval,
    bool? isVolumeButtonsAsShutter,
    bool? isQuickSettingsEnabled,
    bool? isTouchToFocusEnabled,
    bool? isZoomGesturesEnabled,
    bool? isSoundEnabled,
  }) {
    return CameraSettingsModel(
      cameraMode: cameraMode ?? this.cameraMode,
      isGridEnabled: isGridEnabled ?? this.isGridEnabled,
      gridType: gridType ?? this.gridType,
      timerMode: timerMode ?? this.timerMode,
      isLevelEnabled: isLevelEnabled ?? this.isLevelEnabled,
      isHistogramEnabled: isHistogramEnabled ?? this.isHistogramEnabled,
      imageQuality: imageQuality ?? this.imageQuality,
      videoQuality: videoQuality ?? this.videoQuality,
      resolutionPreset: resolutionPreset ?? this.resolutionPreset,
      focusMode: focusMode ?? this.focusMode,
      exposureMode: exposureMode ?? this.exposureMode,
      whiteBalanceMode: whiteBalanceMode ?? this.whiteBalanceMode,
      exposureCompensation: exposureCompensation ?? this.exposureCompensation,
      manualFocus: manualFocus ?? this.manualFocus,
      iso: iso ?? this.iso,
      shutterSpeed: shutterSpeed ?? this.shutterSpeed,
      whiteBalanceTemperature: whiteBalanceTemperature ?? this.whiteBalanceTemperature,
      isRawEnabled: isRawEnabled ?? this.isRawEnabled,
      isHdrEnabled: isHdrEnabled ?? this.isHdrEnabled,
      isImageStabilizationEnabled: isImageStabilizationEnabled ?? this.isImageStabilizationEnabled,
      isNoiseReductionEnabled: isNoiseReductionEnabled ?? this.isNoiseReductionEnabled,
      isFaceDetectionEnabled: isFaceDetectionEnabled ?? this.isFaceDetectionEnabled,
      isSmileDetectionEnabled: isSmileDetectionEnabled ?? this.isSmileDetectionEnabled,
      isLocationTaggingEnabled: isLocationTaggingEnabled ?? this.isLocationTaggingEnabled,
      isVideoStabilizationEnabled: isVideoStabilizationEnabled ?? this.isVideoStabilizationEnabled,
      videoFrameRate: videoFrameRate ?? this.videoFrameRate,
      isSlowMotionEnabled: isSlowMotionEnabled ?? this.isSlowMotionEnabled,
      isTimelapseEnabled: isTimelapseEnabled ?? this.isTimelapseEnabled,
      timelapseInterval: timelapseInterval ?? this.timelapseInterval,
      isVolumeButtonsAsShutter: isVolumeButtonsAsShutter ?? this.isVolumeButtonsAsShutter,
      isQuickSettingsEnabled: isQuickSettingsEnabled ?? this.isQuickSettingsEnabled,
      isTouchToFocusEnabled: isTouchToFocusEnabled ?? this.isTouchToFocusEnabled,
      isZoomGesturesEnabled: isZoomGesturesEnabled ?? this.isZoomGesturesEnabled,
      isSoundEnabled: isSoundEnabled ?? this.isSoundEnabled,
    );
  }

  // Helper methods
  ResolutionPreset getResolutionForQuality() {
    switch (imageQuality) {
      case ImageQuality.low:
        return ResolutionPreset.low;
      case ImageQuality.medium:
        return ResolutionPreset.medium;
      case ImageQuality.high:
        return ResolutionPreset.high;
      case ImageQuality.ultra:
        return ResolutionPreset.ultraHigh;
    }
  }

  String getVideoQualityString() {
    switch (videoQuality) {
      case VideoQuality.sd_480p:
        return '480p';
      case VideoQuality.hd_720p:
        return '720p';
      case VideoQuality.fhd_1080p:
        return '1080p';
      case VideoQuality.uhd_4k:
        return '4K';
    }
  }

  String getShutterSpeedString() {
    if (shutterSpeed >= 1) {
      return '${shutterSpeed.toStringAsFixed(0)}"';
    } else {
      return '1/${(1/shutterSpeed).round()}';
    }
  }
}
