// API Keys Configuration
// Replace with your actual Google Maps API keys

class ApiKeys {
  // Google Maps API Key for Android
  static const String googleMapsAndroid = 'AIzaSyDO5YrN3XwnJ-C4h9ymXoQBFwfWmmU0IJM';
  
  // Google Maps API Key for iOS
  static const String googleMapsIOS = 'AIzaSyDO5YrN3XwnJ-C4h9ymXoQBFwfWmmU0IJM';
  
  // Instructions to get Google Maps API Key:
  // 1. Go to Google Cloud Console: https://console.cloud.google.com/
  // 2. Create a new project or select existing project
  // 3. Enable Google Maps SDK for Android and iOS
  // 4. Go to Credentials and create API Key
  // 5. Restrict the API key to your app's package name
  // 6. Replace the placeholder keys above with your actual keys
  
  // For Android: Restrict to Android apps and add your package name
  // For iOS: Restrict to iOS apps and add your bundle identifier
  
  // Security Note:
  // - Never commit real API keys to public repositories
  // - Use environment variables or secure storage in production
  // - Restrict API keys to specific apps and APIs
  // - Monitor API usage in Google Cloud Console
}

