import 'dart:convert';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import '../../domain/entities/location_entity.dart';
import '../../domain/entities/photo_entity.dart';
import '../../domain/models/file_settings_model.dart';
import '../../domain/models/user_settings_model.dart';
import '../../data/services/geocoding_service.dart';
import '../../data/services/image_processing_service.dart';
import '../../data/services/plus_code_service.dart';
import '../../data/services/user_settings_service.dart';
import '../../data/services/localization_service.dart';
import '../../data/services/unit_conversion_service.dart';

class AppProvider extends ChangeNotifier {
  final GeocodingService _geocodingService = GeocodingService();
  final ImageProcessingService _imageProcessingService = ImageProcessingService();
  final PlusCodeService _plusCodeService = PlusCodeService();
  final UserSettingsService _userSettingsService = UserSettingsService.instance;
  final LocalizationService _localizationService = LocalizationService.instance;
  final UnitConversionService _unitConversionService = UnitConversionService.instance;

  List<PhotoEntity> _photos = [];
  LocationEntity? _currentLocation;
  String? _currentAddress;
  FileSettingsModel _fileSettings = FileSettingsModel();
  UserSettingsModel _userSettings = const UserSettingsModel();
  Locale _currentLocale = const Locale('en', '');
  int _sequenceNumber = 1;

  // Constructor - load saved data
  AppProvider() {
    _loadSavedData();
  }

  // Load saved data from SharedPreferences
  Future<void> _loadSavedData() async {
    await _loadPhotos();
    await _loadFileSettings();
    await _loadUserSettings();
    await _loadCurrentLocale();
    await _loadSequenceNumber();
  }

  // Load photos from SharedPreferences
  Future<void> _loadPhotos() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final photosJson = prefs.getStringList('photos') ?? [];
      
      List<PhotoEntity> loadedPhotos = [];
      
      for (String photoJson in photosJson) {
        final Map<String, dynamic> photoMap = json.decode(photoJson);
        
        // Check if the file still exists
        final File photoFile = File(photoMap['path']);
        if (await photoFile.exists()) {
          // Create location entity if it exists in the saved data
          LocationEntity? location;
          if (photoMap['location'] != null) {
            location = LocationEntity(
              latitude: photoMap['location']['latitude'],
              longitude: photoMap['location']['longitude'],
              timestamp: DateTime.parse(photoMap['location']['timestamp']),
              address: photoMap['location']['address'],
            );
          }
          
          // Create photo entity with video properties
          final photo = PhotoEntity(
            id: photoMap['id'],
            path: photoMap['path'],
            processedPath: photoMap['processedPath'],
            location: location,
            address: photoMap['address'],
            timestamp: DateTime.parse(photoMap['timestamp']),
            isVideo: photoMap['isVideo'] ?? false,
            duration: photoMap['duration'] != null
                ? Duration(milliseconds: photoMap['duration'])
                : null,
          );
          
          loadedPhotos.add(photo);
        }
      }
      
      _photos = loadedPhotos;
      notifyListeners();
    } catch (e) {
      print('Error loading photos: $e');
    }
  }

  // Save photos to SharedPreferences
  Future<void> _savePhotos() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      List<String> photosJson = _photos.map((photo) {
        // Convert location to map if it exists
        Map<String, dynamic>? locationMap;
        if (photo.location != null) {
          locationMap = {
            'latitude': photo.location!.latitude,
            'longitude': photo.location!.longitude,
            'timestamp': photo.location!.timestamp.toIso8601String(),
            'address': photo.location!.address,
          };
        }
        
        // Create photo map with video properties
        return json.encode({
          'id': photo.id,
          'path': photo.path,
          'processedPath': photo.processedPath,
          'location': locationMap,
          'address': photo.address,
          'timestamp': photo.timestamp.toIso8601String(),
          'isVideo': photo.isVideo,
          'duration': photo.duration?.inMilliseconds,
        });
      }).toList();
      
      await prefs.setStringList('photos', photosJson);
    } catch (e) {
      print('Error saving photos: $e');
    }
  }

  // Load file settings from SharedPreferences
  Future<void> _loadFileSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString('fileSettings');
      
      if (settingsJson != null) {
        final Map<String, dynamic> settingsMap = json.decode(settingsJson);
        _fileSettings = FileSettingsModel(
          customPrefix: settingsMap['customPrefix'] ?? '',
          includeDateTime: settingsMap['includeDateTime'] ?? true,
          includeSequenceNumber: settingsMap['includeSequenceNumber'] ?? false,
          includeTimeZone: settingsMap['includeTimeZone'] ?? false,
          includePlusCode: settingsMap['includePlusCode'] ?? false,
          isPro: settingsMap['isPro'] ?? false,
          selectedTemplate: settingsMap['selectedTemplate'] ?? 'default', // Load template
        );
      }
      
      notifyListeners();
    } catch (e) {
      print('Error loading file settings: $e');
    }
  }

  // Save file settings to SharedPreferences
  Future<void> _saveFileSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final settingsMap = {
        'customPrefix': _fileSettings.customPrefix,
        'includeDateTime': _fileSettings.includeDateTime,
        'includeSequenceNumber': _fileSettings.includeSequenceNumber,
        'includeTimeZone': _fileSettings.includeTimeZone,
        'includePlusCode': _fileSettings.includePlusCode,
        'isPro': _fileSettings.isPro,
        'selectedTemplate': _fileSettings.selectedTemplate, // Save template
      };

      await prefs.setString('fileSettings', json.encode(settingsMap));
    } catch (e) {
      print('Error saving file settings: $e');
    }
  }

  // Load user settings
  Future<void> _loadUserSettings() async {
    try {
      _userSettings = await _userSettingsService.loadSettings();
      notifyListeners();
    } catch (e) {
      print('Error loading user settings: $e');
    }
  }

  // Load current locale
  Future<void> _loadCurrentLocale() async {
    try {
      _currentLocale = await _localizationService.getCurrentLocale();
      notifyListeners();
    } catch (e) {
      print('Error loading current locale: $e');
    }
  }

  // Load sequence number from SharedPreferences
  Future<void> _loadSequenceNumber() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _sequenceNumber = prefs.getInt('sequenceNumber') ?? 1;
      notifyListeners();
    } catch (e) {
      print('Error loading sequence number: $e');
    }
  }

  // Save sequence number to SharedPreferences
  Future<void> _saveSequenceNumber() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('sequenceNumber', _sequenceNumber);
    } catch (e) {
      print('Error saving sequence number: $e');
    }
  }

  // Getters
  List<PhotoEntity> get photos => _photos;
  LocationEntity? get currentLocation => _currentLocation;
  String? get currentAddress => _currentAddress;
  FileSettingsModel get fileSettings => _fileSettings;
  UserSettingsModel get userSettings => _userSettings;
  Locale get currentLocale => _currentLocale;
  UnitConversionService get unitConversionService => _unitConversionService;
  int get sequenceNumber => _sequenceNumber;

  // Setters
  void setCurrentLocation(LocationEntity location) {
    _currentLocation = location;
    notifyListeners();
  }

  void setCurrentAddress(String address) {
    _currentAddress = address;
    notifyListeners();
  }

  void setFileSettings(FileSettingsModel settings) {
    _fileSettings = settings;
    _saveFileSettings(); // Save first
    notifyListeners(); // Then notify listeners
  }

  Future<void> setUserSettings(UserSettingsModel settings) async {
    _userSettings = settings;
    await _userSettingsService.saveSettings(settings);

    // Update locale if language changed
    if (settings.languageCode != _currentLocale.languageCode) {
      await setLocale(Locale(settings.languageCode, ''));
    }

    notifyListeners();
  }

  Future<void> setLocale(Locale locale) async {
    _currentLocale = locale;
    await _localizationService.setLocale(locale);
    notifyListeners();
  }

  // Unit formatting methods
  String formatArea(double sqMeters) {
    return _unitConversionService.formatArea(sqMeters, _userSettings.areaUnit);
  }

  String formatDistance(double meters) {
    return _unitConversionService.formatDistance(meters, _userSettings.distanceUnit);
  }

  String formatPerimeter(double meters) {
    return _unitConversionService.formatDistance(meters, _userSettings.perimeterUnit);
  }

  String formatAreaSmart(double sqMeters) {
    return _unitConversionService.formatAreaSmart(sqMeters, _userSettings.areaUnit);
  }

  String formatDistanceSmart(double meters) {
    return _unitConversionService.formatDistanceSmart(meters, _userSettings.distanceUnit);
  }

  void incrementSequenceNumber() {
    _sequenceNumber++;
    notifyListeners();
    _saveSequenceNumber();
  }

  // Generate filename based on settings
  String generateFileName(LocationEntity? location) {
    List<String> parts = [];
    
    // Add custom prefix if set
    if (_fileSettings.customPrefix.isNotEmpty) {
      parts.add(_fileSettings.customPrefix);
    }
    
    // Add date and time if enabled
    if (_fileSettings.includeDateTime) {
      final now = DateTime.now();
      parts.add('${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}_'
          '${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}${now.second.toString().padLeft(2, '0')}');
    }
    
    // Add sequence number if enabled
    if (_fileSettings.includeSequenceNumber) {
      parts.add('_${_sequenceNumber.toString().padLeft(4, '0')}');
    }
    
    // Add timezone if enabled (Pro feature)
    if (_fileSettings.includeTimeZone && _fileSettings.isPro) {
      final now = DateTime.now();
      final timeZoneOffset = now.timeZoneOffset;
      final timeZoneString = '${timeZoneOffset.isNegative ? '-' : '+'}${timeZoneOffset.inHours.abs().toString().padLeft(2, '0')}${(timeZoneOffset.inMinutes % 60).toString().padLeft(2, '0')}';
      parts.add('_TZ$timeZoneString');
    }
    
    // Add plus code if enabled and location available (Pro feature)
    if (_fileSettings.includePlusCode && _fileSettings.isPro && location != null) {
      final plusCode = _plusCodeService.getPlusCode(location.latitude, location.longitude);
      if (plusCode != null) {
        parts.add('_PC$plusCode');
      }
    }
    
    return parts.join('');
  }

  Future<PhotoEntity> addPhoto(PhotoEntity photo) async {
    // Generate a proper filename
    final fileName = generateFileName(photo.location);
    
    // Create a new photo with the generated filename
    final updatedPhoto = photo.copyWith(
      id: fileName,
    );
    
    // Add photo to list first to show it immediately
    _photos.add(updatedPhoto);
    notifyListeners();
    
    // Process the photo with location information if available
    PhotoEntity finalPhoto = updatedPhoto;
    if (updatedPhoto.location != null && !updatedPhoto.isVideo) {
      // Only process images, not videos (videos are processed separately)
      finalPhoto = await _processPhotoWithLocation(updatedPhoto);
    }
    
    // Save photos to SharedPreferences
    await _savePhotos();
    
    // Increment sequence number if enabled
    if (_fileSettings.includeSequenceNumber) {
      incrementSequenceNumber();
    }
    
    return finalPhoto;
  }

  Future<PhotoEntity> _processPhotoWithLocation(PhotoEntity updatedPhoto) async {
    try {
      // Get address if not already available
      String? address = updatedPhoto.address;
      if (address == null && updatedPhoto.location != null) {
        address = await _geocodingService.getAddressFromLatLng(
          updatedPhoto.location!.latitude,
          updatedPhoto.location!.longitude,
        );
      }

      // Update location with address
      final updatedLocation = updatedPhoto.location!.copyWith(address: address);

      // Process image with text overlay
      final processedPath = await _imageProcessingService.addTextOverlayToImage(
        imagePath: updatedPhoto.path,
        text: _fileSettings.customPrefix.isNotEmpty ? _fileSettings.customPrefix : "GPS Map Camera",
        locationText: "Location: ${updatedLocation.latitude.toStringAsFixed(6)}, ${updatedLocation.longitude.toStringAsFixed(6)}",
        addressText: address != null ? "Address: $address" : null,
        timestamp: "Time: ${updatedPhoto.timestamp.toString()}",
        plusCode: _fileSettings.includePlusCode && _fileSettings.isPro ? 
          _plusCodeService.getPlusCode(updatedLocation.latitude, updatedLocation.longitude) : null,
      );

      // Update photo with processed image and updated location
      final index = _photos.indexOf(updatedPhoto);
      if (index != -1) {
        final finalPhoto = updatedPhoto.copyWith(
          processedPath: processedPath,
          location: updatedLocation,
          address: address,
        );
        
        _photos[index] = finalPhoto;
        notifyListeners();
        
        // Save updated photos to SharedPreferences
        await _savePhotos();
        
        return finalPhoto;
      }
    } catch (e) {
      print('Error getting address or processing image: $e');
    }
    
    return updatedPhoto;
  }

  void addCapturedImage(String path) {
    final photo = PhotoEntity(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      path: path,
      timestamp: DateTime.now(),
      location: _currentLocation,
    );
    addPhoto(photo);
  }

  // Add a method to get template-specific settings
  Map<String, dynamic> getTemplateSettings(String templateName) {
    switch (templateName) {
      case 'gps_map_camera':
        return {
          'name': 'GPS Map Camera',
          'prefix': 'GPSMapCamera',
          'primaryColor': Colors.teal,
          'secondaryColor': Colors.tealAccent,
          'logoPath': 'assets/images/gps_map_camera_logo.png',
        };
      case 'travel_logger':
        return {
          'name': 'Travel Logger',
          'prefix': 'TravelLog',
          'primaryColor': Colors.blue,
          'secondaryColor': Colors.lightBlueAccent,
          'logoPath': 'assets/images/travel_logger_logo.png',
        };
      case 'photo_tracker':
        return {
          'name': 'Photo Tracker',
          'prefix': 'PhotoTrack',
          'primaryColor': Colors.deepOrange,
          'secondaryColor': Colors.orangeAccent,
          'logoPath': 'assets/images/photo_tracker_logo.png',
        };
      case 'geo_snap':
        return {
          'name': 'GeoSnap',
          'prefix': 'GeoSnap',
          'primaryColor': Colors.purple,
          'secondaryColor': Colors.purpleAccent,
          'logoPath': 'assets/images/geo_snap_logo.png',
        };
      default:
        return {
          'name': 'Default',
          'prefix': 'GPSMapCamera',
          'primaryColor': Colors.teal,
          'secondaryColor': Colors.tealAccent,
          'logoPath': 'assets/images/default_logo.png',
        };
    }
  }

  // Get current template settings
  Map<String, dynamic> get currentTemplateSettings => 
      getTemplateSettings(_fileSettings.selectedTemplate);

  // Get all available templates
  List<Map<String, dynamic>> get availableTemplates => [
    getTemplateSettings('default'),
    getTemplateSettings('gps_map_camera'),
    getTemplateSettings('travel_logger'),
    getTemplateSettings('photo_tracker'),
    getTemplateSettings('geo_snap'),
  ];

  Future<void> deletePhoto(PhotoEntity photo) async {
    try {
      // Remove from list
      _photos.removeWhere((p) => p.id == photo.id);
      notifyListeners();
      
      // Delete the actual files
      try {
        final file = File(photo.path);
        if (await file.exists()) {
          await file.delete();
        }
        
        if (photo.processedPath != null) {
          final processedFile = File(photo.processedPath!);
          if (await processedFile.exists()) {
            await processedFile.delete();
          }
        }
      } catch (e) {
        print('Error deleting photo files: $e');
      }
      
      // Save updated photos list
      await _savePhotos();
    } catch (e) {
      print('Error deleting photo: $e');
      rethrow;
    }
  }

  // Debug method to clear all saved data and reload
  Future<void> clearAndReloadData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('photos');
      await prefs.remove('fileSettings');
      await prefs.remove('sequenceNumber');

      _photos.clear();
      _fileSettings = FileSettingsModel();
      _sequenceNumber = 1;

      notifyListeners();

      // Reload from file system
      await _loadSavedData();
    } catch (e) {
      print('Error clearing and reloading data: $e');
    }
  }

  // Debug method to print all photos with their properties
  void debugPrintPhotos() {
    print('=== DEBUG: Current Photos ===');
    for (int i = 0; i < _photos.length; i++) {
      final photo = _photos[i];
      print('Photo $i:');
      print('  ID: ${photo.id}');
      print('  Path: ${photo.path}');
      print('  IsVideo: ${photo.isVideo}');
      print('  Duration: ${photo.duration}');
      print('  Timestamp: ${photo.timestamp}');
      print('---');
    }
    print('=== END DEBUG ===');
  }
}
