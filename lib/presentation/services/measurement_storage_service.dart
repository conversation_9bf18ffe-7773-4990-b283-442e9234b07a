import 'package:hive_flutter/hive_flutter.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'dart:convert';

// Data models for measurements
class MeasurementRecord {
  final String id;
  final String name;
  final String type; // area, distance, circular, freeform
  final List<Map<String, double>> points;
  final double area;
  final double distance;
  final double perimeter;
  final String units;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;

  MeasurementRecord({
    required this.id,
    required this.name,
    required this.type,
    required this.points,
    required this.area,
    required this.distance,
    required this.perimeter,
    required this.units,
    required this.timestamp,
    required this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'points': points,
      'area': area,
      'distance': distance,
      'perimeter': perimeter,
      'units': units,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }

  factory MeasurementRecord.fromJson(Map<String, dynamic> json) {
    return MeasurementRecord(
      id: json['id'] ?? '',
      name: json['name'] ?? 'Untitled',
      type: json['type'] ?? 'unknown',
      points: _parsePointsStatic(json['points']),
      area: (json['area'] ?? 0.0).toDouble(),
      distance: (json['distance'] ?? 0.0).toDouble(),
      perimeter: (json['perimeter'] ?? 0.0).toDouble(),
      units: json['units'] ?? 'metric',
      timestamp: DateTime.tryParse(json['timestamp'] ?? '') ?? DateTime.now(),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  // Static helper method for parsing points in fromJson
  static List<Map<String, double>> _parsePointsStatic(dynamic pointsData) {
    if (pointsData == null) return [];

    try {
      if (pointsData is String) {
        pointsData = jsonDecode(pointsData);
      }

      if (pointsData is List) {
        return pointsData.map<Map<String, double>>((point) {
          if (point is Map) {
            return {
              'latitude': (point['latitude'] ?? 0.0).toDouble(),
              'longitude': (point['longitude'] ?? 0.0).toDouble(),
            };
          }
          return {'latitude': 0.0, 'longitude': 0.0};
        }).toList();
      }
    } catch (e) {
      print('Error parsing points: $e');
    }

    return [];
  }

}

class MeasurementStorageService {
  static const String _boxName = 'measurements';
  static const String _dbName = 'measurements.db';
  static const String _tableName = 'measurements';
  
  Box<String>? _hiveBox;
  Database? _database;

  // Initialize storage services
  Future<void> initialize() async {
    await _initializeHive();
    await _initializeSQLite();
  }

  Future<void> _initializeHive() async {
    await Hive.initFlutter();
    _hiveBox = await Hive.openBox<String>(_boxName);
  }

  Future<void> _initializeSQLite() async {
    final databasePath = await getDatabasesPath();
    final path = join(databasePath, _dbName);

    _database = await openDatabase(
      path,
      version: 1,
      onCreate: (db, version) {
        return db.execute('''
          CREATE TABLE $_tableName(
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            type TEXT NOT NULL,
            points TEXT NOT NULL,
            area REAL NOT NULL,
            distance REAL NOT NULL,
            perimeter REAL NOT NULL,
            units TEXT NOT NULL,
            timestamp TEXT NOT NULL,
            metadata TEXT NOT NULL
          )
        ''');
      },
    );
  }

  // Save measurement record
  Future<void> saveMeasurement(MeasurementRecord record) async {
    try {
      print('Attempting to save measurement: ${record.name} (${record.id})');
      print('Measurement type: ${record.type}');
      print('Points count: ${record.points.length}');

      // Validate measurement data
      if (record.id.isEmpty) {
        throw Exception('Measurement ID cannot be empty');
      }

      if (record.name.isEmpty) {
        throw Exception('Measurement name cannot be empty');
      }

      // Prepare data for saving
      final measurementJson = record.toJson();
      print('Prepared measurement data keys: ${measurementJson.keys.toList()}');

      // Save to Hive for quick access
      if (_hiveBox != null) {
        await _hiveBox!.put(record.id, jsonEncode(measurementJson));
        print('Saved to Hive successfully');
      } else {
        print('Warning: Hive box not initialized');
      }

      // Save to SQLite for complex queries
      if (_database != null) {
        await _database!.insert(
          _tableName,
          {
            'id': record.id,
            'name': record.name,
            'type': record.type,
            'points': jsonEncode(record.points),
            'area': record.area,
            'distance': record.distance,
            'perimeter': record.perimeter,
            'units': record.units,
            'timestamp': record.timestamp.toIso8601String(),
            'metadata': jsonEncode(record.metadata),
          },
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
        print('Saved to SQLite successfully');
      } else {
        print('Warning: SQLite database not initialized');
      }

      // Verify save by reading back
      final savedMeasurement = await getMeasurementById(record.id);
      if (savedMeasurement != null) {
        print('Measurement saved and verified successfully: ${record.id}');
      } else {
        print('Warning: Could not verify saved measurement');
      }

    } catch (e) {
      print('Error saving measurement: $e');
      print('Stack trace: ${StackTrace.current}');
      rethrow;
    }
  }

  // Get all measurements
  Future<List<MeasurementRecord>> getAllMeasurements() async {
    try {
      final List<Map<String, dynamic>> maps = await _database?.query(_tableName) ?? [];

      return maps.map((map) {
        return MeasurementRecord(
          id: map['id'] ?? '',
          name: map['name'] ?? 'Untitled',
          type: map['type'] ?? 'unknown',
          points: _parsePoints(map['points']),
          area: (map['area'] ?? 0.0).toDouble(),
          distance: (map['distance'] ?? 0.0).toDouble(),
          perimeter: (map['perimeter'] ?? 0.0).toDouble(),
          units: map['units'] ?? 'metric',
          timestamp: DateTime.tryParse(map['timestamp'] ?? '') ?? DateTime.now(),
          metadata: _parseMetadata(map['metadata']),
        );
      }).toList();
    } catch (e) {
      print('Error getting measurements: $e');
      return [];
    }
  }

  // Helper method to parse points data safely
  List<Map<String, double>> _parsePoints(dynamic pointsData) {
    if (pointsData == null) return [];

    try {
      if (pointsData is String) {
        // If it's a JSON string, decode it first
        pointsData = jsonDecode(pointsData);
      }

      if (pointsData is List) {
        return pointsData.map<Map<String, double>>((point) {
          if (point is Map) {
            return {
              'latitude': (point['latitude'] ?? 0.0).toDouble(),
              'longitude': (point['longitude'] ?? 0.0).toDouble(),
            };
          }
          return {'latitude': 0.0, 'longitude': 0.0};
        }).toList();
      }
    } catch (e) {
      print('Error parsing points: $e');
    }

    return [];
  }

  // Helper method to parse metadata safely
  Map<String, dynamic> _parseMetadata(dynamic metadataData) {
    if (metadataData == null) return {};

    try {
      if (metadataData is String) {
        return Map<String, dynamic>.from(jsonDecode(metadataData));
      } else if (metadataData is Map) {
        return Map<String, dynamic>.from(metadataData);
      }
    } catch (e) {
      print('Error parsing metadata: $e');
    }

    return {};
  }

  // Get measurement by ID
  Future<MeasurementRecord?> getMeasurementById(String id) async {
    try {
      // Try Hive first for speed
      final hiveData = _hiveBox?.get(id);
      if (hiveData != null) {
        return MeasurementRecord.fromJson(jsonDecode(hiveData));
      }

      // Fallback to SQLite
      final List<Map<String, dynamic>> maps = await _database?.query(
        _tableName,
        where: 'id = ?',
        whereArgs: [id],
      ) ?? [];

      if (maps.isNotEmpty) {
        final map = maps.first;
        return MeasurementRecord(
          id: map['id'] ?? '',
          name: map['name'] ?? 'Untitled',
          type: map['type'] ?? 'unknown',
          points: _parsePoints(map['points']),
          area: (map['area'] ?? 0.0).toDouble(),
          distance: (map['distance'] ?? 0.0).toDouble(),
          perimeter: (map['perimeter'] ?? 0.0).toDouble(),
          units: map['units'] ?? 'metric',
          timestamp: DateTime.tryParse(map['timestamp'] ?? '') ?? DateTime.now(),
          metadata: _parseMetadata(map['metadata']),
        );
      }

      return null;
    } catch (e) {
      print('Error getting measurement by ID: $e');
      return null;
    }
  }

  // Delete measurement
  Future<void> deleteMeasurement(String id) async {
    try {
      await _hiveBox?.delete(id);
      await _database?.delete(
        _tableName,
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      print('Error deleting measurement: $e');
      rethrow;
    }
  }

  // Get measurements by type
  Future<List<MeasurementRecord>> getMeasurementsByType(String type) async {
    try {
      final List<Map<String, dynamic>> maps = await _database?.query(
        _tableName,
        where: 'type = ?',
        whereArgs: [type],
      ) ?? [];

      return maps.map((map) {
        return MeasurementRecord(
          id: map['id'] ?? '',
          name: map['name'] ?? 'Untitled',
          type: map['type'] ?? 'unknown',
          points: _parsePoints(map['points']),
          area: (map['area'] ?? 0.0).toDouble(),
          distance: (map['distance'] ?? 0.0).toDouble(),
          perimeter: (map['perimeter'] ?? 0.0).toDouble(),
          units: map['units'] ?? 'metric',
          timestamp: DateTime.tryParse(map['timestamp'] ?? '') ?? DateTime.now(),
          metadata: _parseMetadata(map['metadata']),
        );
      }).toList();
    } catch (e) {
      print('Error getting measurements by type: $e');
      return [];
    }
  }

  // Export measurements to JSON
  Future<String> exportToJson() async {
    try {
      final measurements = await getAllMeasurements();
      final exportData = {
        'export_date': DateTime.now().toIso8601String(),
        'total_records': measurements.length,
        'measurements': measurements.map((m) => m.toJson()).toList(),
      };
      return jsonEncode(exportData);
    } catch (e) {
      print('Error exporting to JSON: $e');
      rethrow;
    }
  }

  // Import measurements from JSON
  Future<void> importFromJson(String jsonData) async {
    try {
      final data = jsonDecode(jsonData);
      final measurements = data['measurements'] as List;
      
      for (var measurementData in measurements) {
        final record = MeasurementRecord.fromJson(measurementData);
        await saveMeasurement(record);
      }
    } catch (e) {
      print('Error importing from JSON: $e');
      rethrow;
    }
  }

  // Clear all measurements
  Future<void> clearAllMeasurements() async {
    try {
      await _hiveBox?.clear();
      await _database?.delete(_tableName);
    } catch (e) {
      print('Error clearing measurements: $e');
      rethrow;
    }
  }

  // Close storage services
  Future<void> close() async {
    await _hiveBox?.close();
    await _database?.close();
  }
}
