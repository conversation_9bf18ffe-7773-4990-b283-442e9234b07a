import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:google_maps_flutter/google_maps_flutter.dart';

// Place data model
class PlaceInfo {
  final String placeId;
  final String name;
  final String address;
  final LatLng location;
  final String type;
  final double rating;
  final String? photoReference;
  final bool isOpen;
  final String? phoneNumber;
  final String? website;

  PlaceInfo({
    required this.placeId,
    required this.name,
    required this.address,
    required this.location,
    required this.type,
    required this.rating,
    this.photoReference,
    required this.isOpen,
    this.phoneNumber,
    this.website,
  });

  factory PlaceInfo.fromJson(Map<String, dynamic> json) {
    final geometry = json['geometry']['location'];
    return PlaceInfo(
      placeId: json['place_id'] ?? '',
      name: json['name'] ?? 'Unknown',
      address: json['vicinity'] ?? json['formatted_address'] ?? '',
      location: LatLng(
        geometry['lat']?.toDouble() ?? 0.0,
        geometry['lng']?.toDouble() ?? 0.0,
      ),
      type: json['types']?.first ?? 'establishment',
      rating: json['rating']?.toDouble() ?? 0.0,
      photoReference: json['photos']?.isNotEmpty == true 
          ? json['photos'][0]['photo_reference'] 
          : null,
      isOpen: json['opening_hours']?['open_now'] ?? false,
      phoneNumber: json['formatted_phone_number'],
      website: json['website'],
    );
  }
}

class PlacesService {
  static const String _apiKey = 'YOUR_GOOGLE_PLACES_API_KEY'; // Replace with your API key
  static const String _baseUrl = 'https://maps.googleapis.com/maps/api/place';

  // Search nearby places by category
  Future<List<PlaceInfo>> searchNearbyPlaces({
    required LatLng location,
    required String type,
    int radius = 5000,
    String? keyword,
  }) async {
    try {
      final url = Uri.parse(
        '$_baseUrl/nearbysearch/json?'
        'location=${location.latitude},${location.longitude}&'
        'radius=$radius&'
        'type=$type&'
        '${keyword != null ? 'keyword=$keyword&' : ''}'
        'key=$_apiKey'
      );

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final results = data['results'] as List;
        
        return results.map((place) => PlaceInfo.fromJson(place)).toList();
      } else {
        throw Exception('Failed to load places: ${response.statusCode}');
      }
    } catch (e) {
      print('Error searching nearby places: $e');
      return [];
    }
  }

  // Get place details by place ID
  Future<PlaceInfo?> getPlaceDetails(String placeId) async {
    try {
      final url = Uri.parse(
        '$_baseUrl/details/json?'
        'place_id=$placeId&'
        'fields=name,formatted_address,geometry,rating,photos,opening_hours,formatted_phone_number,website&'
        'key=$_apiKey'
      );

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final result = data['result'];
        
        if (result != null) {
          return PlaceInfo.fromJson(result);
        }
      }
      return null;
    } catch (e) {
      print('Error getting place details: $e');
      return null;
    }
  }

  // Search ATMs nearby
  Future<List<PlaceInfo>> searchATMs(LatLng location, {int radius = 2000}) async {
    return await searchNearbyPlaces(
      location: location,
      type: 'atm',
      radius: radius,
    );
  }

  // Search petrol bunks nearby
  Future<List<PlaceInfo>> searchPetrolBunks(LatLng location, {int radius = 5000}) async {
    return await searchNearbyPlaces(
      location: location,
      type: 'gas_station',
      radius: radius,
    );
  }

  // Search hospitals nearby
  Future<List<PlaceInfo>> searchHospitals(LatLng location, {int radius = 10000}) async {
    return await searchNearbyPlaces(
      location: location,
      type: 'hospital',
      radius: radius,
    );
  }

  // Search grocery stores nearby
  Future<List<PlaceInfo>> searchGroceryStores(LatLng location, {int radius = 3000}) async {
    return await searchNearbyPlaces(
      location: location,
      type: 'grocery_or_supermarket',
      radius: radius,
    );
  }

  // Search restaurants nearby
  Future<List<PlaceInfo>> searchRestaurants(LatLng location, {int radius = 2000}) async {
    return await searchNearbyPlaces(
      location: location,
      type: 'restaurant',
      radius: radius,
    );
  }

  // Search toilets nearby
  Future<List<PlaceInfo>> searchToilets(LatLng location, {int radius = 1000}) async {
    return await searchNearbyPlaces(
      location: location,
      type: 'establishment',
      radius: radius,
      keyword: 'toilet restroom bathroom',
    );
  }

  // Search parking nearby
  Future<List<PlaceInfo>> searchParking(LatLng location, {int radius = 1000}) async {
    return await searchNearbyPlaces(
      location: location,
      type: 'parking',
      radius: radius,
    );
  }

  // Search all categories
  Future<Map<String, List<PlaceInfo>>> searchAllCategories(LatLng location) async {
    final results = <String, List<PlaceInfo>>{};
    
    try {
      // Search all categories in parallel
      final futures = [
        searchATMs(location).then((places) => results['ATMs'] = places),
        searchPetrolBunks(location).then((places) => results['Petrol Bunks'] = places),
        searchHospitals(location).then((places) => results['Hospitals'] = places),
        searchGroceryStores(location).then((places) => results['Grocery Stores'] = places),
        searchRestaurants(location).then((places) => results['Restaurants'] = places),
        searchToilets(location).then((places) => results['Toilets'] = places),
        searchParking(location).then((places) => results['Parking'] = places),
      ];
      
      await Future.wait(futures);
      
      return results;
    } catch (e) {
      print('Error searching all categories: $e');
      return {};
    }
  }

  // Get photo URL for a place
  String? getPhotoUrl(String? photoReference, {int maxWidth = 400}) {
    if (photoReference == null) return null;
    
    return '$_baseUrl/photo?'
           'maxwidth=$maxWidth&'
           'photo_reference=$photoReference&'
           'key=$_apiKey';
  }

  // Text search for places
  Future<List<PlaceInfo>> textSearch({
    required String query,
    LatLng? location,
    int radius = 5000,
  }) async {
    try {
      String url = '$_baseUrl/textsearch/json?'
                  'query=$query&'
                  'key=$_apiKey';
      
      if (location != null) {
        url += '&location=${location.latitude},${location.longitude}&radius=$radius';
      }

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final results = data['results'] as List;
        
        return results.map((place) => PlaceInfo.fromJson(place)).toList();
      } else {
        throw Exception('Failed to search places: ${response.statusCode}');
      }
    } catch (e) {
      print('Error in text search: $e');
      return [];
    }
  }

  // Autocomplete place search
  Future<List<String>> autocomplete(String input, {LatLng? location}) async {
    try {
      String url = '$_baseUrl/autocomplete/json?'
                  'input=$input&'
                  'key=$_apiKey';
      
      if (location != null) {
        url += '&location=${location.latitude},${location.longitude}&radius=50000';
      }

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final predictions = data['predictions'] as List;
        
        return predictions.map((prediction) => prediction['description'] as String).toList();
      } else {
        throw Exception('Failed to get autocomplete: ${response.statusCode}');
      }
    } catch (e) {
      print('Error in autocomplete: $e');
      return [];
    }
  }

  // Get distance matrix between multiple points
  Future<Map<String, dynamic>?> getDistanceMatrix({
    required List<LatLng> origins,
    required List<LatLng> destinations,
    String units = 'metric',
    String mode = 'driving',
  }) async {
    try {
      final originsStr = origins.map((p) => '${p.latitude},${p.longitude}').join('|');
      final destinationsStr = destinations.map((p) => '${p.latitude},${p.longitude}').join('|');
      
      final url = Uri.parse(
        'https://maps.googleapis.com/maps/api/distancematrix/json?'
        'origins=$originsStr&'
        'destinations=$destinationsStr&'
        'units=$units&'
        'mode=$mode&'
        'key=$_apiKey'
      );

      final response = await http.get(url);

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to get distance matrix: ${response.statusCode}');
      }
    } catch (e) {
      print('Error getting distance matrix: $e');
      return null;
    }
  }
}
