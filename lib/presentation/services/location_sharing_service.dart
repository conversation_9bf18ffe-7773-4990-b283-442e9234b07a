import 'package:share_plus/share_plus.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'dart:io';
import 'dart:typed_data';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/services.dart';

class LocationSharingService {
  // Share current location
  static Future<void> shareCurrentLocation({
    required Position position,
    String? customMessage,
    String? locationName,
  }) async {
    try {
      // Get address from coordinates
      String address = await _getAddressFromCoordinates(
        position.latitude, 
        position.longitude
      );

      // Create location message
      String message = customMessage ?? 'Check out my current location!';
      String locationInfo = locationName ?? address;
      
      // Google Maps link
      String googleMapsLink = 'https://maps.google.com/?q=${position.latitude},${position.longitude}';
      
      // Apple Maps link (for iOS)
      String appleMapsLink = 'https://maps.apple.com/?q=${position.latitude},${position.longitude}';
      
      // WhatsApp location format
      String whatsappLink = 'https://wa.me/?text=${Uri.encodeComponent(
        '$message\n\n📍 Location: $locationInfo\n'
        '🌐 Coordinates: ${position.latitude.toStringAsFixed(6)}, ${position.longitude.toStringAsFixed(6)}\n'
        '🗺️ View on Google Maps: $googleMapsLink'
      )}';

      // Comprehensive share text
      String shareText = '''
$message

📍 Location: $locationInfo
🌐 Coordinates: ${position.latitude.toStringAsFixed(6)}, ${position.longitude.toStringAsFixed(6)}
📏 Accuracy: ${position.accuracy.toStringAsFixed(1)}m
⏰ Time: ${_formatDateTime(position.timestamp)}

🗺️ View on Maps:
Google Maps: $googleMapsLink
Apple Maps: $appleMapsLink

📱 Share on WhatsApp: $whatsappLink
      '''.trim();

      await Share.share(
        shareText,
        subject: 'Location Share - $locationInfo',
      );

    } catch (e) {
      print('Error sharing current location: $e');
      rethrow;
    }
  }

  // Share multiple measurement points
  static Future<void> shareMeasurementPoints({
    required List<Position> points,
    String? title,
    double? totalArea,
    double? totalDistance,
    String? measurementType,
  }) async {
    try {
      if (points.isEmpty) {
        throw Exception('No points to share');
      }

      String shareTitle = title ?? 'GPS Measurement Points';
      String type = measurementType ?? 'measurement';
      
      // Create points list
      StringBuffer pointsList = StringBuffer();
      for (int i = 0; i < points.length; i++) {
        String address = await _getAddressFromCoordinates(
          points[i].latitude, 
          points[i].longitude
        );
        
        pointsList.writeln(
          'Point ${i + 1}: ${points[i].latitude.toStringAsFixed(6)}, ${points[i].longitude.toStringAsFixed(6)}'
        );
        pointsList.writeln('Address: $address');
        pointsList.writeln('');
      }

      // Create measurement summary
      String measurementSummary = '';
      if (totalArea != null && totalArea > 0) {
        measurementSummary += '📐 Total Area: ${_formatArea(totalArea)}\n';
      }
      if (totalDistance != null && totalDistance > 0) {
        measurementSummary += '📏 Total Distance: ${_formatDistance(totalDistance)}\n';
      }

      // Create Google Maps link for all points
      String googleMapsLink = _createMultiPointGoogleMapsLink(points);

      // Create comprehensive share text
      String shareText = '''
$shareTitle

📊 Measurement Type: ${type.toUpperCase()}
📍 Total Points: ${points.length}

$measurementSummary

📋 Point Details:
$pointsList

🗺️ View all points on Google Maps:
$googleMapsLink

📱 Shared via GPS Map Camera App
⏰ ${_formatDateTime(DateTime.now())}
      '''.trim();

      await Share.share(
        shareText,
        subject: shareTitle,
      );

    } catch (e) {
      print('Error sharing measurement points: $e');
      rethrow;
    }
  }

  // Share specific location point
  static Future<void> shareLocationPoint({
    required LatLng location,
    String? pointName,
    String? description,
    String? customMessage,
  }) async {
    try {
      // Get address from coordinates
      String address = await _getAddressFromCoordinates(
        location.latitude, 
        location.longitude
      );

      String name = pointName ?? 'Location Point';
      String message = customMessage ?? 'Check out this location!';
      String desc = description ?? '';
      
      // Create location links
      String googleMapsLink = 'https://maps.google.com/?q=${location.latitude},${location.longitude}';
      String appleMapsLink = 'https://maps.apple.com/?q=${location.latitude},${location.longitude}';
      
      // WhatsApp share link
      String whatsappText = Uri.encodeComponent(
        '$message\n\n📍 $name\n'
        '🏠 Address: $address\n'
        '🌐 Coordinates: ${location.latitude.toStringAsFixed(6)}, ${location.longitude.toStringAsFixed(6)}\n'
        '🗺️ $googleMapsLink'
      );
      String whatsappLink = 'https://wa.me/?text=$whatsappText';

      // Comprehensive share text
      String shareText = '''
$message

📍 Location: $name
🏠 Address: $address
${desc.isNotEmpty ? '📝 Description: $desc\n' : ''}
🌐 Coordinates: ${location.latitude.toStringAsFixed(6)}, ${location.longitude.toStringAsFixed(6)}

🗺️ Open in Maps:
Google Maps: $googleMapsLink
Apple Maps: $appleMapsLink

📱 Quick Share:
WhatsApp: $whatsappLink

📱 Shared via GPS Map Camera App
⏰ ${_formatDateTime(DateTime.now())}
      '''.trim();

      await Share.share(
        shareText,
        subject: 'Location Share - $name',
      );

    } catch (e) {
      print('Error sharing location point: $e');
      rethrow;
    }
  }

  // Share location with custom format
  static Future<void> shareLocationCustomFormat({
    required LatLng location,
    required String format, // 'coordinates', 'address', 'maps_link', 'whatsapp', 'full'
    String? customMessage,
    String? locationName,
  }) async {
    try {
      String message = customMessage ?? 'Location shared';
      String name = locationName ?? 'Shared Location';
      
      String shareText = '';
      
      switch (format.toLowerCase()) {
        case 'coordinates':
          shareText = '$message\n📍 $name\n🌐 ${location.latitude.toStringAsFixed(6)}, ${location.longitude.toStringAsFixed(6)}';
          break;
          
        case 'address':
          String address = await _getAddressFromCoordinates(location.latitude, location.longitude);
          shareText = '$message\n📍 $name\n🏠 $address';
          break;
          
        case 'maps_link':
          String googleMapsLink = 'https://maps.google.com/?q=${location.latitude},${location.longitude}';
          shareText = '$message\n📍 $name\n🗺️ $googleMapsLink';
          break;
          
        case 'whatsapp':
          String address = await _getAddressFromCoordinates(location.latitude, location.longitude);
          String whatsappText = Uri.encodeComponent(
            '$message\n📍 $name\n🏠 $address\n🌐 ${location.latitude.toStringAsFixed(6)}, ${location.longitude.toStringAsFixed(6)}'
          );
          shareText = 'https://wa.me/?text=$whatsappText';
          break;
          
        case 'full':
        default:
          await shareLocationPoint(
            location: location,
            pointName: name,
            customMessage: message,
          );
          return;
      }

      await Share.share(shareText, subject: 'Location Share - $name');

    } catch (e) {
      print('Error sharing location with custom format: $e');
      rethrow;
    }
  }

  // Share location as image with map screenshot
  static Future<void> shareLocationAsImage({
    required LatLng location,
    required Uint8List mapScreenshot,
    String? locationName,
    String? description,
  }) async {
    try {
      // Save screenshot to temporary file
      final tempDir = await getTemporaryDirectory();
      final file = File('${tempDir.path}/location_map_${DateTime.now().millisecondsSinceEpoch}.png');
      await file.writeAsBytes(mapScreenshot);

      String name = locationName ?? 'Location';
      String desc = description ?? '';
      
      // Get address
      String address = await _getAddressFromCoordinates(location.latitude, location.longitude);
      
      String shareText = '''
📍 Location: $name
🏠 Address: $address
${desc.isNotEmpty ? '📝 Description: $desc\n' : ''}
🌐 Coordinates: ${location.latitude.toStringAsFixed(6)}, ${location.longitude.toStringAsFixed(6)}

📱 Shared via GPS Map Camera App
⏰ ${_formatDateTime(DateTime.now())}
      '''.trim();

      await Share.shareXFiles(
        [XFile(file.path)],
        text: shareText,
        subject: 'Location Map - $name',
      );

      // Clean up temporary file
      await file.delete();

    } catch (e) {
      print('Error sharing location as image: $e');
      rethrow;
    }
  }

  // Copy location to clipboard
  static Future<void> copyLocationToClipboard({
    required LatLng location,
    String format = 'coordinates', // 'coordinates', 'address', 'maps_link'
  }) async {
    try {
      String clipboardText = '';
      
      switch (format.toLowerCase()) {
        case 'coordinates':
          clipboardText = '${location.latitude.toStringAsFixed(6)}, ${location.longitude.toStringAsFixed(6)}';
          break;
          
        case 'address':
          String address = await _getAddressFromCoordinates(location.latitude, location.longitude);
          clipboardText = address;
          break;
          
        case 'maps_link':
          clipboardText = 'https://maps.google.com/?q=${location.latitude},${location.longitude}';
          break;
          
        default:
          clipboardText = '${location.latitude.toStringAsFixed(6)}, ${location.longitude.toStringAsFixed(6)}';
      }

      await Clipboard.setData(ClipboardData(text: clipboardText));

    } catch (e) {
      print('Error copying location to clipboard: $e');
      rethrow;
    }
  }

  // Helper methods
  static Future<String> _getAddressFromCoordinates(double latitude, double longitude) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(latitude, longitude);
      if (placemarks.isNotEmpty) {
        Placemark place = placemarks.first;
        return '${place.street ?? ''}, ${place.locality ?? ''}, ${place.administrativeArea ?? ''}, ${place.country ?? ''}'
            .replaceAll(RegExp(r'^,\s*|,\s*$'), '') // Remove leading/trailing commas
            .replaceAll(RegExp(r',\s*,'), ','); // Remove double commas
      }
      return 'Address not found';
    } catch (e) {
      return 'Lat: ${latitude.toStringAsFixed(6)}, Lng: ${longitude.toStringAsFixed(6)}';
    }
  }

  static String _createMultiPointGoogleMapsLink(List<Position> points) {
    if (points.isEmpty) return '';
    
    // Create waypoints for Google Maps
    String waypoints = points.map((p) => '${p.latitude},${p.longitude}').join('|');
    return 'https://maps.google.com/maps?waypoints=$waypoints';
  }

  static String _formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return '';
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  static String _formatDistance(double meters) {
    if (meters < 1000) {
      return '${meters.toStringAsFixed(1)}m';
    } else {
      return '${(meters / 1000).toStringAsFixed(2)}km';
    }
  }

  static String _formatArea(double sqMeters) {
    if (sqMeters < 1) {
      return '${sqMeters.toStringAsFixed(3)} m²';
    } else if (sqMeters < 10000) {
      return '${sqMeters.toStringAsFixed(1)} m²';
    } else {
      double hectares = sqMeters / 10000;
      double acres = sqMeters / 4046.86;
      return '${hectares.toStringAsFixed(2)} ha (${acres.toStringAsFixed(2)} acres)';
    }
  }
}
