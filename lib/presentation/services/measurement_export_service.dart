import 'dart:io';
import 'dart:typed_data';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:convert';
import 'measurement_storage_service.dart';

class MeasurementExportService {
  static const String _appName = 'GPS Map Camera';
  
  // Export measurement as PDF report
  static Future<File> exportMeasurementToPDF({
    required MeasurementRecord measurement,
    Uint8List? mapScreenshot,
    String? additionalNotes,
  }) async {
    final pdf = pw.Document();
    
    // Add PDF pages
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build: (pw.Context context) {
          return [
            // Header
            _buildPDFHeader(measurement),
            pw.SizedBox(height: 20),
            
            // Measurement summary
            _buildMeasurementSummary(measurement),
            pw.SizedBox(height: 20),
            
            // Map screenshot if available
            if (mapScreenshot != null) ...[
              _buildMapSection(mapScreenshot),
              pw.SizedBox(height: 20),
            ],
            
            // Points details
            _buildPointsDetails(measurement),
            pw.SizedBox(height: 20),
            
            // Additional notes
            if (additionalNotes != null && additionalNotes.isNotEmpty) ...[
              _buildNotesSection(additionalNotes),
              pw.SizedBox(height: 20),
            ],
            
            // Footer
            _buildPDFFooter(),
          ];
        },
      ),
    );
    
    // Save PDF to file
    final tempDir = await getTemporaryDirectory();
    final file = File('${tempDir.path}/measurement_${measurement.id}.pdf');
    await file.writeAsBytes(await pdf.save());
    
    return file;
  }

  // Export multiple measurements as combined PDF
  static Future<File> exportMultipleMeasurementsToPDF({
    required List<MeasurementRecord> measurements,
    String? reportTitle,
    String? additionalNotes,
  }) async {
    final pdf = pw.Document();
    
    // Cover page
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return _buildCoverPage(
            title: reportTitle ?? 'GPS Measurements Report',
            measurementCount: measurements.length,
          );
        },
      ),
    );
    
    // Summary page
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build: (pw.Context context) {
          return _buildSummaryPage(measurements);
        },
      ),
    );
    
    // Individual measurement pages
    for (int i = 0; i < measurements.length; i++) {
      final measurement = measurements[i];
      
      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          header: (pw.Context context) {
            return pw.Container(
              alignment: pw.Alignment.centerRight,
              margin: const pw.EdgeInsets.only(bottom: 3.0 * PdfPageFormat.mm),
              padding: const pw.EdgeInsets.only(bottom: 3.0 * PdfPageFormat.mm),
              decoration: const pw.BoxDecoration(
                border: pw.Border(bottom: pw.BorderSide(width: 0.5, color: PdfColors.grey)),
              ),
              child: pw.Text(
                'Measurement ${i + 1} of ${measurements.length}',
                style: pw.Theme.of(context).defaultTextStyle.copyWith(color: PdfColors.grey),
              ),
            );
          },
          build: (pw.Context context) {
            return [
              _buildPDFHeader(measurement),
              pw.SizedBox(height: 20),
              _buildMeasurementSummary(measurement),
              pw.SizedBox(height: 20),
              _buildPointsDetails(measurement),
            ];
          },
        ),
      );
    }
    
    // Notes page if provided
    if (additionalNotes != null && additionalNotes.isNotEmpty) {
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          build: (pw.Context context) {
            return _buildNotesSection(additionalNotes);
          },
        ),
      );
    }
    
    // Save PDF to file
    final tempDir = await getTemporaryDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final file = File('${tempDir.path}/measurements_report_$timestamp.pdf');
    await file.writeAsBytes(await pdf.save());
    
    return file;
  }

  // Export measurement as JSON
  static Future<File> exportMeasurementToJSON({
    required MeasurementRecord measurement,
    bool includeMetadata = true,
  }) async {
    final exportData = {
      'export_info': {
        'app_name': _appName,
        'export_date': DateTime.now().toIso8601String(),
        'format_version': '1.0',
      },
      'measurement': measurement.toJson(),
      if (includeMetadata) 'metadata': {
        'device_info': 'GPS Map Camera Export',
        'coordinate_system': 'WGS84',
        'accuracy_note': 'Measurements calculated using Vincenty\'s formula',
      },
    };
    
    final tempDir = await getTemporaryDirectory();
    final file = File('${tempDir.path}/measurement_${measurement.id}.json');
    await file.writeAsString(jsonEncode(exportData));
    
    return file;
  }

  // Export measurement as CSV
  static Future<File> exportMeasurementToCSV({
    required MeasurementRecord measurement,
  }) async {
    final buffer = StringBuffer();
    
    // CSV Header
    buffer.writeln('Point,Latitude,Longitude,Address');
    
    // Points data
    for (int i = 0; i < measurement.points.length; i++) {
      final point = measurement.points[i];
      buffer.writeln('${i + 1},${point['latitude']},${point['longitude']},""');
    }
    
    // Summary data
    buffer.writeln('');
    buffer.writeln('Summary');
    buffer.writeln('Measurement Name,${measurement.name}');
    buffer.writeln('Type,${measurement.type}');
    buffer.writeln('Total Area,${measurement.area} sq meters');
    buffer.writeln('Total Distance,${measurement.distance} meters');
    buffer.writeln('Perimeter,${measurement.perimeter} meters');
    buffer.writeln('Date,${measurement.timestamp.toIso8601String()}');
    
    final tempDir = await getTemporaryDirectory();
    final file = File('${tempDir.path}/measurement_${measurement.id}.csv');
    await file.writeAsString(buffer.toString());
    
    return file;
  }

  // Share measurement file
  static Future<void> shareMeasurementFile({
    required File file,
    required String measurementName,
    String? customMessage,
  }) async {
    final message = customMessage ?? 
        'GPS Measurement Report: $measurementName\n\nGenerated by $_appName';
    
    await Share.shareXFiles(
      [XFile(file.path)],
      text: message,
      subject: 'GPS Measurement - $measurementName',
    );
  }

  // Print measurement report
  static Future<void> printMeasurementReport({
    required MeasurementRecord measurement,
    Uint8List? mapScreenshot,
    String? additionalNotes,
  }) async {
    final pdfFile = await exportMeasurementToPDF(
      measurement: measurement,
      mapScreenshot: mapScreenshot,
      additionalNotes: additionalNotes,
    );
    
    final pdfBytes = await pdfFile.readAsBytes();
    await Printing.layoutPdf(onLayout: (PdfPageFormat format) async => pdfBytes);
  }

  // PDF Building Helper Methods
  static pw.Widget _buildPDFHeader(MeasurementRecord measurement) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Text(
              _appName,
              style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold),
            ),
            pw.Text(
              'GPS Measurement Report',
              style: pw.TextStyle(fontSize: 16, color: PdfColors.grey),
            ),
          ],
        ),
        pw.SizedBox(height: 10),
        pw.Divider(),
        pw.SizedBox(height: 10),
        pw.Text(
          measurement.name,
          style: pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold),
        ),
        pw.Text(
          'Generated on ${_formatDateTime(DateTime.now())}',
          style: pw.TextStyle(fontSize: 12, color: PdfColors.grey),
        ),
      ],
    );
  }

  static pw.Widget _buildMeasurementSummary(MeasurementRecord measurement) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Measurement Summary',
            style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            children: [
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    _buildSummaryRow('Type:', measurement.type.toUpperCase()),
                    _buildSummaryRow('Total Points:', '${measurement.points.length}'),
                    _buildSummaryRow('Date:', _formatDateTime(measurement.timestamp)),
                  ],
                ),
              ),
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    if (measurement.area > 0)
                      _buildSummaryRow('Area:', _formatArea(measurement.area)),
                    if (measurement.distance > 0)
                      _buildSummaryRow('Distance:', _formatDistance(measurement.distance)),
                    if (measurement.perimeter > 0)
                      _buildSummaryRow('Perimeter:', _formatDistance(measurement.perimeter)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildSummaryRow(String label, String value) {
    return pw.Padding(
      padding: const pw.EdgeInsets.only(bottom: 4),
      child: pw.Row(
        children: [
          pw.SizedBox(
            width: 80,
            child: pw.Text(label, style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
          ),
          pw.Text(value),
        ],
      ),
    );
  }

  static pw.Widget _buildMapSection(Uint8List mapScreenshot) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Map View',
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        pw.Container(
          width: double.infinity,
          height: 300,
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.grey300),
          ),
          child: pw.Image(pw.MemoryImage(mapScreenshot), fit: pw.BoxFit.cover),
        ),
      ],
    );
  }

  static pw.Widget _buildPointsDetails(MeasurementRecord measurement) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Point Details',
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey300),
          children: [
            // Header
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey100),
              children: [
                _buildTableCell('Point', isHeader: true),
                _buildTableCell('Latitude', isHeader: true),
                _buildTableCell('Longitude', isHeader: true),
              ],
            ),
            // Data rows
            ...measurement.points.asMap().entries.map((entry) {
              final index = entry.key;
              final point = entry.value;
              return pw.TableRow(
                children: [
                  _buildTableCell('${index + 1}'),
                  _buildTableCell('${point['latitude']?.toStringAsFixed(6)}'),
                  _buildTableCell('${point['longitude']?.toStringAsFixed(6)}'),
                ],
              );
            }).toList(),
          ],
        ),
      ],
    );
  }

  static pw.Widget _buildTableCell(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
      ),
    );
  }

  static pw.Widget _buildNotesSection(String notes) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Additional Notes',
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.all(16),
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.grey300),
            borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
          ),
          child: pw.Text(notes),
        ),
      ],
    );
  }

  static pw.Widget _buildPDFFooter() {
    return pw.Container(
      alignment: pw.Alignment.center,
      child: pw.Column(
        children: [
          pw.Divider(),
          pw.SizedBox(height: 10),
          pw.Text(
            'Generated by $_appName',
            style: pw.TextStyle(fontSize: 10, color: PdfColors.grey),
          ),
          pw.Text(
            'Professional GPS Measurement & Land Surveying App',
            style: pw.TextStyle(fontSize: 8, color: PdfColors.grey),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildCoverPage({required String title, required int measurementCount}) {
    return pw.Center(
      child: pw.Column(
        mainAxisAlignment: pw.MainAxisAlignment.center,
        children: [
          pw.Text(
            _appName,
            style: pw.TextStyle(fontSize: 32, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 20),
          pw.Text(
            title,
            style: pw.TextStyle(fontSize: 24),
          ),
          pw.SizedBox(height: 40),
          pw.Text(
            'Total Measurements: $measurementCount',
            style: pw.TextStyle(fontSize: 16),
          ),
          pw.SizedBox(height: 20),
          pw.Text(
            'Generated on ${_formatDateTime(DateTime.now())}',
            style: pw.TextStyle(fontSize: 14, color: PdfColors.grey),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildSummaryPage(List<MeasurementRecord> measurements) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Measurements Summary',
          style: pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 20),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey300),
          children: [
            // Header
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey100),
              children: [
                _buildTableCell('Name', isHeader: true),
                _buildTableCell('Type', isHeader: true),
                _buildTableCell('Area', isHeader: true),
                _buildTableCell('Distance', isHeader: true),
                _buildTableCell('Date', isHeader: true),
              ],
            ),
            // Data rows
            ...measurements.map((measurement) {
              return pw.TableRow(
                children: [
                  _buildTableCell(measurement.name),
                  _buildTableCell(measurement.type),
                  _buildTableCell(measurement.area > 0 ? _formatArea(measurement.area) : '-'),
                  _buildTableCell(measurement.distance > 0 ? _formatDistance(measurement.distance) : '-'),
                  _buildTableCell(_formatDate(measurement.timestamp)),
                ],
              );
            }).toList(),
          ],
        ),
      ],
    );
  }

  // Helper methods
  static String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  static String _formatDate(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }

  static String _formatDistance(double meters) {
    if (meters < 1000) {
      return '${meters.toStringAsFixed(1)}m';
    } else {
      return '${(meters / 1000).toStringAsFixed(2)}km';
    }
  }

  static String _formatArea(double sqMeters) {
    if (sqMeters < 1) {
      return '${sqMeters.toStringAsFixed(3)} m²';
    } else if (sqMeters < 10000) {
      return '${sqMeters.toStringAsFixed(1)} m²';
    } else {
      double hectares = sqMeters / 10000;
      double acres = sqMeters / 4046.86;
      return '${hectares.toStringAsFixed(2)} ha (${acres.toStringAsFixed(2)} acres)';
    }
  }
}
