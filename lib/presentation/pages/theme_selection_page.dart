import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import '../../data/services/theme_manager.dart';
import '../../data/services/pro_status_manager.dart';
import '../../data/services/rewarded_interstitial_ad_manager.dart';
import '../widgets/reward_dialog.dart';

class ThemeSelectionPage extends StatefulWidget {
  const ThemeSelectionPage({super.key});

  @override
  State<ThemeSelectionPage> createState() => _ThemeSelectionPageState();
}

class _ThemeSelectionPageState extends State<ThemeSelectionPage> {
  final ThemeManager _themeManager = ThemeManager();
  final ProStatusManager _proManager = ProStatusManager();
  final RewardedInterstitialAdManager _adManager = RewardedInterstitialAdManager();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Choose Theme'),
        backgroundColor: _themeManager.currentThemeData.primaryColor,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Pro Status Card
            Card(
              color: _proManager.isProUser ? Colors.green[50] : Colors.orange[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Icon(
                      _proManager.isProUser ? Icons.diamond : Icons.info,
                      color: _proManager.isProUser ? Colors.green : Colors.orange,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _proManager.proStatusText,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            _proManager.isProUser
                                ? 'All themes unlocked!'
                                : 'Watch ads to unlock themes or upgrade to Pro',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            const Text(
              'Available Themes',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Theme Grid
            Expanded(
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                  childAspectRatio: 1.2,
                ),
                itemCount: _themeManager.availableThemes.length,
                itemBuilder: (context, index) {
                  final themeName = _themeManager.availableThemes[index];
                  final isCurrentTheme = themeName == _themeManager.currentTheme;
                  final isDefaultTheme = themeName == 'default';
                  final canUseTheme = _proManager.isProUser || isDefaultTheme;
                  
                  return _buildThemeCard(
                    themeName: themeName,
                    isCurrentTheme: isCurrentTheme,
                    canUseTheme: canUseTheme,
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThemeCard({
    required String themeName,
    required bool isCurrentTheme,
    required bool canUseTheme,
  }) {
    final themeColor = _themeManager.getThemeColor(themeName);
    final displayName = _themeManager.getThemeDisplayName(themeName);
    
    return Card(
      elevation: isCurrentTheme ? 8 : 2,
      child: InkWell(
        onTap: () => _onThemeSelected(themeName, canUseTheme),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: isCurrentTheme
                ? Border.all(color: themeColor, width: 3)
                : null,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Theme Color Preview
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: themeColor,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: themeColor.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: isCurrentTheme
                    ? const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 30,
                      )
                    : null,
              ),
              
              const SizedBox(height: 12),
              
              // Theme Name
              Text(
                displayName,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 4),
              
              // Status Indicator
              if (!canUseTheme)
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.play_circle_fill,
                      size: 16,
                      color: Colors.orange[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Watch Ad',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.orange[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                )
              else if (isCurrentTheme)
                Text(
                  'Current',
                  style: TextStyle(
                    fontSize: 10,
                    color: themeColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _onThemeSelected(String themeName, bool canUseTheme) {
    if (canUseTheme) {
      // Can use theme directly
      _themeManager.setTheme(themeName);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Theme changed to ${_themeManager.getThemeDisplayName(themeName)}'),
          backgroundColor: Colors.green,
        ),
      );
    } else {
      // Need to watch ad first
      _showRewardedAdForTheme(themeName);
    }
  }

  void _showRewardedAdForTheme(String themeName) {
    if (!_adManager.isAdAvailable) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Ad not ready. Please try again in a moment.'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    _adManager.showAdIfAvailable(
      onUserEarnedReward: (AdWithoutView ad, RewardItem reward) {
        // Show reward dialog and unlock theme
        RewardDialog.show(
          context,
          reward: reward,
          onContinue: () {
            _themeManager.setTheme(themeName);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Theme unlocked: ${_themeManager.getThemeDisplayName(themeName)}'),
                backgroundColor: Colors.green,
              ),
            );
            setState(() {}); // Refresh UI
          },
        );
      },
      onAdClosed: () {
        // Ad closed without reward
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Watch the complete ad to unlock the theme'),
            backgroundColor: Colors.orange,
          ),
        );
      },
    );
  }
}
