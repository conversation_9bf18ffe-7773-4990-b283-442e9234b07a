import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../data/services/onboarding_service.dart';
import '../../data/services/tutorial_service.dart';
import '../../data/tutorial_data.dart';
import 'homepage.dart';
import 'auth/login_screen.dart';
import '../../presentation/providers/app_state_provider.dart';
import '../widgets/onboarding_page_widget.dart';
import '../widgets/tutorial_overlay_widget.dart';
import '../../main.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen>
    with TickerProviderStateMixin {
  final PageController _pageController = PageController();
  final OnboardingService _onboardingService = OnboardingService();
  final TutorialService _tutorialService = TutorialService();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  int _currentPage = 0;
  final int _totalPages = 3;

  final List<OnboardingPageData> _pages = [
    OnboardingPageData(
      title: "Welcome to GPS Map Camera",
      subtitle: "Capture moments with precise location data",
      description: "Transform your photos into powerful location-aware memories. Every shot tells a story of where you've been.",
      icon: Icons.camera_alt,
      gradient: [Colors.teal, Colors.cyan],
      features: [
        "📍 GPS coordinates embedded in photos",
        "🗺️ Interactive map integration",
        "📱 Professional camera features"
      ],
      isInteractive: false,
      pageType: OnboardingPageType.welcome,
    ),
    OnboardingPageData(
      title: "Professional Camera",
      subtitle: "Capture stunning photos with location data",
      description: "Our advanced camera automatically embeds GPS coordinates, timestamps, and location details into every photo you take.",
      icon: Icons.photo_camera,
      gradient: [Colors.blue, Colors.indigo],
      features: [
        "📸 High-quality photo capture",
        "📍 Automatic GPS tagging",
        "⏰ Timestamp overlay",
        "🏠 Address information"
      ],
      isInteractive: true,
      pageType: OnboardingPageType.camera,
      actionText: "Try Camera",
    ),
    OnboardingPageData(
      title: "Interactive Map View",
      subtitle: "Visualize your photos on a beautiful map",
      description: "See all your photos plotted on an interactive map. Discover patterns in your photography and relive your journeys.",
      icon: Icons.map,
      gradient: [Colors.green, Colors.teal],
      features: [
        "🗺️ Interactive Google Maps integration",
        "📌 Photo markers on map",
        "🔍 Zoom and explore locations",
        "📍 Detailed location information"
      ],
      isInteractive: true,
      pageType: OnboardingPageType.map,
      actionText: "Explore Map",
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < _totalPages - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeOnboarding();
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _skipOnboarding() async {
    await _onboardingService.markOnboardingSkipped();

    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => const LoginScreen(),
        ),
      );
    }
  }

  void _completeOnboarding() async {
    await _onboardingService.markOnboardingComplete();

    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => const LoginScreen(),
        ),
      );
    }
  }

  void _handleInteractiveAction(OnboardingPageType pageType) {
    switch (pageType) {
      case OnboardingPageType.camera:
        _navigateToCamera();
        break;
      case OnboardingPageType.gallery:
        _navigateToGallery();
        break;
      case OnboardingPageType.map:
        _navigateToMap();
        break;
      case OnboardingPageType.welcome:
        break;
    }
  }

  void _navigateToCamera() {
    // Navigate directly to camera - tutorial will show there
    Navigator.of(context).pushNamed('/camera');
  }

  void _navigateToGallery() {
    // Navigate directly to gallery - tutorial will show there
    Navigator.of(context).pushNamed('/gallery');
  }

  void _navigateToMap() {
    // Navigate directly to map - tutorial will show there
    Navigator.of(context).pushNamed('/map');
  }

  @override
  Widget build(BuildContext context) {
    final appProvider = Provider.of<AppProvider>(context);
    final primaryColor = Colors.teal;

    return Scaffold(
      body: AnimatedContainer(
        duration: const Duration(milliseconds: 800),
        curve: Curves.easeInOut,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              _pages[_currentPage].gradient[0],
              _pages[_currentPage].gradient[1],
              _pages[_currentPage].gradient[1].withOpacity(0.8),
            ],
            stops: const [0.0, 0.7, 1.0],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Skip button
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const SizedBox(width: 60),
                    Text(
                      '${_currentPage + 1} of $_totalPages',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: TextButton.icon(
                        onPressed: _skipOnboarding,
                        icon: const Icon(
                          Icons.skip_next,
                          color: Colors.white,
                          size: 18,
                        ),
                        label: const Text(
                          'Skip',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              // Page content
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  onPageChanged: (index) {
                    setState(() {
                      _currentPage = index;
                    });
                    _animationController.reset();
                    _animationController.forward();
                  },
                  itemCount: _totalPages,
                  itemBuilder: (context, index) {
                    return FadeTransition(
                      opacity: _fadeAnimation,
                      child: OnboardingPageWidget(
                        data: _pages[index],
                        onInteractiveAction: () => _handleInteractiveAction(_pages[index].pageType),
                      ),
                    );
                  },
                ),
              ),
              
              // Bottom navigation
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 24.0),
                child: Column(
                  children: [
                    // Page indicators
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(
                        _totalPages,
                        (index) => AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                          margin: const EdgeInsets.symmetric(horizontal: 3),
                          width: _currentPage == index ? 24 : 8,
                          height: _currentPage == index ? 8 : 6,
                          decoration: BoxDecoration(
                            color: _currentPage == index
                                ? Colors.white
                                : Colors.white.withOpacity(0.4),
                            borderRadius: BorderRadius.circular(4),
                            boxShadow: _currentPage == index
                                ? [
                                    BoxShadow(
                                      color: Colors.white.withOpacity(0.5),
                                      blurRadius: 6,
                                      spreadRadius: 1,
                                    ),
                                  ]
                                : null,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 20),

                    // Navigation buttons
                    Row(
                      children: [
                        // Previous button
                        if (_currentPage > 0)
                          Expanded(
                            flex: 2,
                            child: TextButton.icon(
                              onPressed: _previousPage,
                              icon: const Icon(Icons.arrow_back, color: Colors.white, size: 18),
                              label: const Text(
                                'Previous',
                                style: TextStyle(color: Colors.white, fontSize: 14),
                              ),
                            ),
                          ),

                        if (_currentPage > 0) const SizedBox(width: 12),

                        // Next/Get Started button
                        Expanded(
                          flex: 3,
                          child: AnimatedContainer(
                            duration: const Duration(milliseconds: 300),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(25),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.white.withOpacity(0.3),
                                  blurRadius: 15,
                                  spreadRadius: 2,
                                  offset: const Offset(0, 5),
                                ),
                              ],
                            ),
                            child: ElevatedButton.icon(
                              onPressed: _nextPage,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.white,
                                foregroundColor: _pages[_currentPage].gradient[0],
                                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(25),
                                ),
                                elevation: 8,
                              ),
                              icon: AnimatedSwitcher(
                                duration: const Duration(milliseconds: 300),
                                child: Icon(
                                  _currentPage == _totalPages - 1 ? Icons.rocket_launch : Icons.arrow_forward,
                                  key: ValueKey(_currentPage == _totalPages - 1),
                                  size: 18,
                                ),
                              ),
                              label: AnimatedSwitcher(
                                duration: const Duration(milliseconds: 300),
                                child: Text(
                                  _currentPage == _totalPages - 1 ? 'Get Started' : 'Next',
                                  key: ValueKey(_currentPage == _totalPages - 1),
                                  style: const TextStyle(
                                    fontSize: 15,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

enum OnboardingPageType {
  welcome,
  camera,
  gallery,
  map,
}

class OnboardingPageData {
  final String title;
  final String subtitle;
  final String description;
  final IconData icon;
  final List<Color> gradient;
  final List<String> features;
  final bool isInteractive;
  final OnboardingPageType pageType;
  final String? actionText;

  OnboardingPageData({
    required this.title,
    required this.subtitle,
    required this.description,
    required this.icon,
    required this.gradient,
    required this.features,
    this.isInteractive = false,
    required this.pageType,
    this.actionText,
  });
}
