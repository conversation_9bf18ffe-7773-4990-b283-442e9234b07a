import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'dart:async';
import '../services/measurement_storage_service.dart';
import '../widgets/banner_ad_widget.dart';
import '../../generated/l10n/app_localizations.dart';

class MarkerCreationPage extends StatefulWidget {
  final LatLng initialLocation;
  final Function(Map<String, dynamic>) onMarkerSaved;

  const MarkerCreationPage({
    Key? key,
    required this.initialLocation,
    required this.onMarkerSaved,
  }) : super(key: key);

  @override
  State<MarkerCreationPage> createState() => _MarkerCreationPageState();
}

class _MarkerCreationPageState extends State<MarkerCreationPage> {
  GoogleMapController? _mapController;
  Set<Marker> _markers = {};
  LatLng? _selectedLocation;
  String _selectedAddress = '';
  String _markerName = '';
  String _markerDescription = '';
  String _markerCategory = 'General';

  // Map view options
  bool _showSatelliteView = false;
  
  final List<String> _categories = [
    'General',
    'Important',
    'Landmark',
    'Building',
    'Parking',
    'Restaurant',
    'Shop',
    'Hospital',
    'School',
    'Office',
    'Home',
    'Other',
  ];

  final Map<String, IconData> _categoryIcons = {
    'General': Icons.place,
    'Important': Icons.star,
    'Landmark': Icons.location_city,
    'Building': Icons.business,
    'Parking': Icons.local_parking,
    'Restaurant': Icons.restaurant,
    'Shop': Icons.shopping_cart,
    'Hospital': Icons.local_hospital,
    'School': Icons.school,
    'Office': Icons.work,
    'Home': Icons.home,
    'Other': Icons.location_on,
  };

  final Map<String, Color> _categoryColors = {
    'General': Colors.blue,
    'Important': Colors.red,
    'Landmark': Colors.purple,
    'Building': Colors.brown,
    'Parking': Colors.green,
    'Restaurant': Colors.orange,
    'Shop': Colors.pink,
    'Hospital': Colors.red,
    'School': Colors.blue,
    'Office': Colors.grey,
    'Home': Colors.green,
    'Other': Colors.black,
  };

  @override
  void initState() {
    super.initState();
    _initializeLocation();
  }

  Future<void> _initializeLocation() async {
    try {
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      
      if (_mapController != null) {
        _mapController!.animateCamera(
          CameraUpdate.newLatLngZoom(
            LatLng(position.latitude, position.longitude),
            16.0,
          ),
        );
      }
    } catch (e) {
      if (kDebugMode) print('Error getting location: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n?.locationMarker ?? 'Create Location Marker'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        actions: [
          if (_selectedLocation != null) ...[
            IconButton(
              icon: const Icon(Icons.clear),
              onPressed: _removeMarker,
              tooltip: 'Remove Marker',
            ),
            IconButton(
              icon: const Icon(Icons.check_circle),
              onPressed: _saveMarker,
              tooltip: 'Save Marker',
            ),
          ],
        ],
      ),
      
      body: Column(
        children: [
          // Top Banner Ad
          const BannerAdWidget(
            margin: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            alwaysShow: true,
          ),
          
          // Instructions
          if (_selectedLocation == null)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              color: Colors.orange.shade50,
              child: Column(
                children: [
                  Icon(Icons.place, size: 32, color: Colors.orange.shade700),
                  const SizedBox(height: 8),
                  Text(
                    'Tap on the map to place a location marker',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.orange.shade700,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Mark important locations for future reference',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.orange.shade600,
                    ),
                  ),
                ],
              ),
            ),
          
          // Map
          Expanded(
            child: Stack(
              children: [
                GoogleMap(
                  onMapCreated: _onMapCreated,
                  onTap: _onMapTap,
                  initialCameraPosition: CameraPosition(
                    target: widget.initialLocation,
                    zoom: 16,
                  ),
                  mapType: _showSatelliteView ? MapType.satellite : MapType.normal,
                  markers: _markers,
                  myLocationEnabled: true,
                  myLocationButtonEnabled: false,
                  mapToolbarEnabled: false,
                ),
                
                // Marker info overlay
                if (_selectedLocation != null)
                  Positioned(
                    top: 16,
                    left: 16,
                    right: 16,
                    child: _buildMarkerInfoCard(),
                  ),

                // Satellite view toggle
                Positioned(
                  top: 16,
                  right: 16,
                  child: _buildSatelliteToggle(),
                ),
              ],
            ),
          ),
          
          // Marker details form
          if (_selectedLocation != null)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Category selection
                  const Text(
                    'Category',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: _categories.map((category) {
                      final isSelected = _markerCategory == category;
                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            _markerCategory = category;
                          });
                          _updateMarkerVisualization();
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: isSelected 
                                ? _categoryColors[category]
                                : Colors.grey.shade200,
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                _categoryIcons[category],
                                size: 16,
                                color: isSelected ? Colors.white : Colors.grey.shade600,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                category,
                                style: TextStyle(
                                  color: isSelected ? Colors.white : Colors.grey.shade600,
                                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Save button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _showMarkerDetailsDialog,
                      icon: const Icon(Icons.save),
                      label: const Text('Save Location Marker'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildMarkerInfoCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _categoryIcons[_markerCategory],
                  color: _categoryColors[_markerCategory],
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Location Marker - $_markerCategory',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.delete_outline, color: Colors.red.shade600),
                  onPressed: _removeMarker,
                  tooltip: 'Remove Marker',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text('Coordinates: ${_selectedLocation!.latitude.toStringAsFixed(6)}, ${_selectedLocation!.longitude.toStringAsFixed(6)}'),
            if (_selectedAddress.isNotEmpty)
              Text('Address: $_selectedAddress'),
          ],
        ),
      ),
    );
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
  }

  void _onMapTap(LatLng latLng) {
    setState(() {
      _selectedLocation = latLng;
    });
    _updateMarkerVisualization();
    _getAddressFromCoordinates(latLng);
  }

  void _updateMarkerVisualization() {
    if (_selectedLocation == null) return;

    setState(() {
      _markers = {
        Marker(
          markerId: const MarkerId('location_marker'),
          position: _selectedLocation!,
          infoWindow: InfoWindow(
            title: _markerCategory,
            snippet: 'Tap to save this location',
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(
            _getCategoryHue(_markerCategory),
          ),
        ),
      };
    });
  }

  double _getCategoryHue(String category) {
    switch (category) {
      case 'Important': return BitmapDescriptor.hueRed;
      case 'Landmark': return BitmapDescriptor.hueViolet;
      case 'Building': return BitmapDescriptor.hueOrange;
      case 'Parking': return BitmapDescriptor.hueGreen;
      case 'Restaurant': return BitmapDescriptor.hueOrange;
      case 'Shop': return BitmapDescriptor.hueMagenta;
      case 'Hospital': return BitmapDescriptor.hueRed;
      case 'School': return BitmapDescriptor.hueBlue;
      case 'Office': return BitmapDescriptor.hueYellow;
      case 'Home': return BitmapDescriptor.hueGreen;
      default: return BitmapDescriptor.hueBlue;
    }
  }

  Future<void> _getAddressFromCoordinates(LatLng latLng) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(
        latLng.latitude,
        latLng.longitude,
      );
      
      if (placemarks.isNotEmpty) {
        Placemark place = placemarks.first;
        String address = '${place.street ?? ''}, ${place.locality ?? ''}, ${place.administrativeArea ?? ''}, ${place.country ?? ''}'
            .replaceAll(RegExp(r'^,\s*|,\s*$'), '')
            .replaceAll(RegExp(r',\s*,'), ',');
        
        setState(() {
          _selectedAddress = address;
        });
      }
    } catch (e) {
      if (kDebugMode) print('Error getting address: $e');
    }
  }

  void _showMarkerDetailsDialog() {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();

    // Generate default name
    final now = DateTime.now();
    nameController.text = '$_markerCategory Marker ${now.day}/${now.month}/${now.year}';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Save Location Marker'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Marker summary
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Marker Summary',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.orange.shade700,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('Category: $_markerCategory'),
                    Text('Coordinates: ${_selectedLocation!.latitude.toStringAsFixed(6)}, ${_selectedLocation!.longitude.toStringAsFixed(6)}'),
                    if (_selectedAddress.isNotEmpty)
                      Text('Address: $_selectedAddress'),
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
              
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'Marker Name',
                  border: OutlineInputBorder(),
                ),
                maxLength: 50,
              ),
              
              const SizedBox(height: 16),
              
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description (Optional)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
                maxLength: 200,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (nameController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please enter a marker name'),
                    backgroundColor: Colors.orange,
                  ),
                );
                return;
              }
              
              await _saveMarkerData(nameController.text.trim(), descriptionController.text.trim());
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Close page
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('Save Marker'),
          ),
        ],
      ),
    );
  }

  Future<void> _saveMarkerData(String name, String description) async {
    if (_selectedLocation == null) return;

    try {
      final storageService = MeasurementStorageService();
      await storageService.initialize();

      final markerData = MeasurementRecord(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: name,
        type: 'marker',
        points: [{
          'latitude': _selectedLocation!.latitude,
          'longitude': _selectedLocation!.longitude,
        }],
        area: 0.0,
        distance: 0.0,
        perimeter: 0.0,
        units: 'metric',
        timestamp: DateTime.now(),
        metadata: {
          'app_version': '1.0',
          'measurement_type': 'marker',
          'category': _markerCategory,
          'description': description,
          'address': _selectedAddress,
        },
      );

      await storageService.saveMeasurement(markerData);
      
      widget.onMarkerSaved({
        'name': name,
        'category': _markerCategory,
        'description': description,
        'address': _selectedAddress,
        'coordinates': _selectedLocation,
      });

      _showSaveSuccessDialog();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error saving marker: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _saveMarker() {
    if (_selectedLocation != null) {
      _showMarkerDetailsDialog();
    }
  }

  void _removeMarker() {
    setState(() {
      _selectedLocation = null;
      _selectedAddress = '';
      _markerName = '';
      _markerDescription = '';
      _markerCategory = 'General';
      _markers.clear();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Marker removed'),
        backgroundColor: Colors.orange,
        duration: Duration(seconds: 2),
      ),
    );
  }

  Widget _buildSatelliteToggle() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () {
            setState(() {
              _showSatelliteView = !_showSatelliteView;
            });
          },
          child: Container(
            padding: const EdgeInsets.all(12),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _showSatelliteView ? Icons.map : Icons.satellite_alt,
                  color: _showSatelliteView ? Colors.green : Colors.blue,
                  size: 24,
                ),
                const SizedBox(height: 4),
                Text(
                  _showSatelliteView ? 'Normal' : 'Satellite',
                  style: TextStyle(
                    color: _showSatelliteView ? Colors.green : Colors.blue,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showSaveSuccessDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.orange.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.check_circle,
                color: Colors.orange.shade700,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'Marker Saved Successfully!',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Your location marker has been saved to your device.',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.purple.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.purple.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.storage, color: Colors.purple.shade700, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Saved Location',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.purple.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '📱 Local Device Storage',
                    style: TextStyle(color: Colors.purple.shade600),
                  ),
                  Text(
                    '💾 Accessible from "Saved Data" menu',
                    style: TextStyle(color: Colors.purple.shade600),
                  ),
                  Text(
                    '📤 Export as PDF, JSON, or CSV',
                    style: TextStyle(color: Colors.purple.shade600),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Close page
            },
            child: const Text('Continue'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Close page
              // Navigate to saved measurements
            },
            icon: const Icon(Icons.folder_rounded),
            label: const Text('View Saved Data'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}
