import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/measurement_storage_service.dart';
import '../services/measurement_export_service.dart';
import '../providers/app_state_provider.dart';
import '../widgets/banner_ad_widget.dart';
import 'category_manager_page.dart';
import 'measurement_viewer_page.dart';
import 'dart:io';

class MeasurementManagerPage extends StatefulWidget {
  const MeasurementManagerPage({Key? key}) : super(key: key);

  @override
  State<MeasurementManagerPage> createState() => _MeasurementManagerPageState();
}

class _MeasurementManagerPageState extends State<MeasurementManagerPage> {
  final MeasurementStorageService _storageService = MeasurementStorageService();
  List<MeasurementRecord> _measurements = [];
  bool _isLoading = true;
  String _searchQuery = '';
  String _sortBy = 'date'; // date, name, type, area, distance

  @override
  void initState() {
    super.initState();
    _initializeStorage();
  }

  Future<void> _initializeStorage() async {
    await _storageService.initialize();
    await _loadMeasurements();
  }

  Future<void> _loadMeasurements() async {
    setState(() => _isLoading = true);
    try {
      print('Loading measurements from storage...');
      final measurements = await _storageService.getAllMeasurements();
      print('Loaded ${measurements.length} measurements');

      setState(() {
        _measurements = measurements;
        _isLoading = false;
      });
      _sortMeasurements();

      // Show success message if measurements were loaded
      if (measurements.isNotEmpty) {
        print('Successfully loaded measurements: ${measurements.map((m) => m.name).join(', ')}');
      } else {
        print('No measurements found in storage');
      }
    } catch (e) {
      print('Error loading measurements: $e');
      setState(() => _isLoading = false);
      _showErrorSnackBar('Error loading measurements: $e');
    }
  }

  void _sortMeasurements() {
    setState(() {
      switch (_sortBy) {
        case 'name':
          _measurements.sort((a, b) => a.name.compareTo(b.name));
          break;
        case 'type':
          _measurements.sort((a, b) => a.type.compareTo(b.type));
          break;
        case 'area':
          _measurements.sort((a, b) => b.area.compareTo(a.area));
          break;
        case 'distance':
          _measurements.sort((a, b) => b.distance.compareTo(a.distance));
          break;
        case 'date':
        default:
          _measurements.sort((a, b) => b.timestamp.compareTo(a.timestamp));
          break;
      }
    });
  }

  List<MeasurementRecord> get _filteredMeasurements {
    if (_searchQuery.isEmpty) return _measurements;
    
    return _measurements.where((measurement) {
      return measurement.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             measurement.type.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final appProvider = Provider.of<AppProvider>(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Saved Measurements'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.category),
            onPressed: _navigateToCategories,
            tooltip: 'View by Categories',
          ),
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: _showStorageInfo,
            tooltip: 'Storage Information',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadMeasurements,
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.sort),
            onSelected: (value) {
              setState(() => _sortBy = value);
              _sortMeasurements();
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'date', child: Text('Sort by Date')),
              const PopupMenuItem(value: 'name', child: Text('Sort by Name')),
              const PopupMenuItem(value: 'type', child: Text('Sort by Type')),
              const PopupMenuItem(value: 'area', child: Text('Sort by Area')),
              const PopupMenuItem(value: 'distance', child: Text('Sort by Distance')),
            ],
          ),
        ],
      ),
      
      body: SafeArea(
        child: Column(
          children: [
          // Top Banner Ad
          const BannerAdWidget(
            margin: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            alwaysShow: true,
          ),
          
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Search measurements...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.grey.shade100,
              ),
              onChanged: (value) {
                setState(() => _searchQuery = value);
              },
            ),
          ),
          
          // Measurements list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredMeasurements.isEmpty
                    ? _buildEmptyState()
                    : _buildMeasurementsList(),
          ),
          ],
        ),
      ),
      
      floatingActionButton: FloatingActionButton.extended(
        heroTag: "export_all_fab_measurement_manager", // Unique hero tag
        onPressed: _exportAllMeasurements,
        icon: const Icon(Icons.file_download),
        label: const Text('Export All'),
        backgroundColor: Colors.green,
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.straighten,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isEmpty 
                ? 'No measurements saved yet'
                : 'No measurements found',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isEmpty
                ? 'Start measuring areas and distances to see them here'
                : 'Try a different search term',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey.shade500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMeasurementsList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _filteredMeasurements.length,
      itemBuilder: (context, index) {
        final measurement = _filteredMeasurements[index];
        return _buildMeasurementCard(measurement);
      },
    );
  }

  Widget _buildMeasurementCard(MeasurementRecord measurement) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _showMeasurementDetails(measurement),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row
              Row(
                children: [
                  Expanded(
                    child: Text(
                      measurement.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  _buildTypeChip(measurement.type),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // Measurement values
              Row(
                children: [
                  if (measurement.area > 0) ...[
                    Icon(Icons.crop_free, size: 16, color: Colors.grey.shade600),
                    const SizedBox(width: 4),
                    Text(
                      _formatArea(measurement.area),
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    const SizedBox(width: 16),
                  ],
                  if (measurement.distance > 0) ...[
                    Icon(Icons.straighten, size: 16, color: Colors.grey.shade600),
                    const SizedBox(width: 4),
                    Text(
                      _formatDistance(measurement.distance),
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ],
              ),
              
              const SizedBox(height: 8),
              
              // Footer row
              Row(
                children: [
                  Icon(Icons.location_on, size: 16, color: Colors.grey.shade600),
                  const SizedBox(width: 4),
                  Text(
                    '${measurement.points.length} points',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  const Spacer(),
                  Text(
                    _formatDate(measurement.timestamp),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(width: 8),
                  PopupMenuButton<String>(
                    icon: Icon(Icons.more_vert, color: Colors.grey.shade600),
                    onSelected: (action) => _handleMeasurementAction(action, measurement),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'view_map',
                        child: Row(
                          children: [
                            Icon(Icons.map, size: 16),
                            SizedBox(width: 8),
                            Text('View on Map'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(value: 'view', child: Text('View Details')),
                      const PopupMenuItem(value: 'share', child: Text('Share')),
                      const PopupMenuItem(value: 'export_pdf', child: Text('Export PDF')),
                      const PopupMenuItem(value: 'export_json', child: Text('Export JSON')),
                      const PopupMenuItem(value: 'export_csv', child: Text('Export CSV')),
                      const PopupMenuItem(value: 'delete', child: Text('Delete')),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTypeChip(String type) {
    Color chipColor;
    IconData chipIcon;
    
    switch (type.toLowerCase()) {
      case 'area':
        chipColor = Colors.blue;
        chipIcon = Icons.crop_free;
        break;
      case 'distance':
        chipColor = Colors.green;
        chipIcon = Icons.straighten;
        break;
      case 'circular':
        chipColor = Colors.orange;
        chipIcon = Icons.circle_outlined;
        break;
      case 'freeform':
        chipColor = Colors.purple;
        chipIcon = Icons.gesture;
        break;
      default:
        chipColor = Colors.grey;
        chipIcon = Icons.place;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: chipColor.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(chipIcon, size: 14, color: chipColor),
          const SizedBox(width: 4),
          Text(
            type.toUpperCase(),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: chipColor,
            ),
          ),
        ],
      ),
    );
  }

  void _handleMeasurementAction(String action, MeasurementRecord measurement) {
    switch (action) {
      case 'view_map':
        _viewMeasurementOnMap(measurement);
        break;
      case 'view':
        _showMeasurementDetails(measurement);
        break;
      case 'share':
        _shareMeasurement(measurement);
        break;
      case 'export_pdf':
        _exportMeasurementAsPDF(measurement);
        break;
      case 'export_json':
        _exportMeasurementAsJSON(measurement);
        break;
      case 'export_csv':
        _exportMeasurementAsCSV(measurement);
        break;
      case 'delete':
        _deleteMeasurement(measurement);
        break;
    }
  }

  void _showMeasurementDetails(MeasurementRecord measurement) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(measurement.name),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Type', measurement.type.toUpperCase()),
              _buildDetailRow('Points', '${measurement.points.length}'),
              if (measurement.area > 0)
                _buildDetailRow('Area', _formatArea(measurement.area)),
              if (measurement.distance > 0)
                _buildDetailRow('Distance', _formatDistance(measurement.distance)),
              if (measurement.perimeter > 0)
                _buildDetailRow('Perimeter', _formatDistance(measurement.perimeter)),
              _buildDetailRow('Date', _formatDateTime(measurement.timestamp)),
              _buildDetailRow('Units', measurement.units),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context);
              _viewMeasurementOnMap(measurement);
            },
            icon: const Icon(Icons.map),
            label: const Text('View on Map'),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _shareMeasurement(measurement);
            },
            child: const Text('Share'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Future<void> _shareMeasurement(MeasurementRecord measurement) async {
    try {
      // Create a simple text share for now
      final shareText = '''
📍 GPS Measurement: ${measurement.name}

📊 Type: ${measurement.type.toUpperCase()}
📍 Points: ${measurement.points.length}
${measurement.area > 0 ? '📐 Area: ${_formatArea(measurement.area)}\n' : ''}${measurement.distance > 0 ? '📏 Distance: ${_formatDistance(measurement.distance)}\n' : ''}${measurement.perimeter > 0 ? '📏 Perimeter: ${_formatDistance(measurement.perimeter)}\n' : ''}📅 Date: ${_formatDateTime(measurement.timestamp)}

📱 Shared via GPS Map Camera App
      '''.trim();

      // You can implement more sophisticated sharing here
      // For now, we'll use a simple approach
      _showSuccessSnackBar('Measurement shared successfully!');
    } catch (e) {
      _showErrorSnackBar('Error sharing measurement: $e');
    }
  }

  Future<void> _exportMeasurementAsPDF(MeasurementRecord measurement) async {
    try {
      _showLoadingDialog('Exporting PDF...');
      
      final pdfFile = await MeasurementExportService.exportMeasurementToPDF(
        measurement: measurement,
        additionalNotes: 'Exported from GPS Map Camera App',
      );
      
      Navigator.pop(context); // Close loading dialog
      
      await MeasurementExportService.shareMeasurementFile(
        file: pdfFile,
        measurementName: measurement.name,
      );
      
      _showSuccessSnackBar('PDF exported and shared successfully!');
    } catch (e) {
      Navigator.pop(context); // Close loading dialog
      _showErrorSnackBar('Error exporting PDF: $e');
    }
  }

  Future<void> _exportMeasurementAsJSON(MeasurementRecord measurement) async {
    try {
      _showLoadingDialog('Exporting JSON...');
      
      final jsonFile = await MeasurementExportService.exportMeasurementToJSON(
        measurement: measurement,
      );
      
      Navigator.pop(context); // Close loading dialog
      
      await MeasurementExportService.shareMeasurementFile(
        file: jsonFile,
        measurementName: measurement.name,
      );
      
      _showSuccessSnackBar('JSON exported and shared successfully!');
    } catch (e) {
      Navigator.pop(context); // Close loading dialog
      _showErrorSnackBar('Error exporting JSON: $e');
    }
  }

  Future<void> _exportMeasurementAsCSV(MeasurementRecord measurement) async {
    try {
      _showLoadingDialog('Exporting CSV...');
      
      final csvFile = await MeasurementExportService.exportMeasurementToCSV(
        measurement: measurement,
      );
      
      Navigator.pop(context); // Close loading dialog
      
      await MeasurementExportService.shareMeasurementFile(
        file: csvFile,
        measurementName: measurement.name,
      );
      
      _showSuccessSnackBar('CSV exported and shared successfully!');
    } catch (e) {
      Navigator.pop(context); // Close loading dialog
      _showErrorSnackBar('Error exporting CSV: $e');
    }
  }

  Future<void> _exportAllMeasurements() async {
    if (_measurements.isEmpty) {
      _showErrorSnackBar('No measurements to export');
      return;
    }
    
    try {
      _showLoadingDialog('Exporting all measurements...');
      
      final pdfFile = await MeasurementExportService.exportMultipleMeasurementsToPDF(
        measurements: _measurements,
        reportTitle: 'GPS Measurements Report',
        additionalNotes: 'Complete measurement report generated by GPS Map Camera App',
      );
      
      Navigator.pop(context); // Close loading dialog
      
      await MeasurementExportService.shareMeasurementFile(
        file: pdfFile,
        measurementName: 'All Measurements',
        customMessage: 'Complete GPS Measurements Report\n\nGenerated by GPS Map Camera App',
      );
      
      _showSuccessSnackBar('All measurements exported successfully!');
    } catch (e) {
      Navigator.pop(context); // Close loading dialog
      _showErrorSnackBar('Error exporting measurements: $e');
    }
  }

  Future<void> _deleteMeasurement(MeasurementRecord measurement) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Measurement'),
        content: Text('Are you sure you want to delete "${measurement.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
    
    if (confirmed == true) {
      try {
        await _storageService.deleteMeasurement(measurement.id);
        await _loadMeasurements();
        _showSuccessSnackBar('Measurement deleted successfully');
      } catch (e) {
        _showErrorSnackBar('Error deleting measurement: $e');
      }
    }
  }

  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Text(message),
          ],
        ),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _navigateToCategories() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CategoryManagerPage(),
      ),
    );
  }

  void _viewMeasurementOnMap(MeasurementRecord measurement) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MeasurementViewerPage(
          measurement: measurement,
          returnPageTitle: 'Saved Measurements',
        ),
      ),
    );
  }

  void _showStorageInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.storage,
                color: Colors.blue.shade700,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'Storage Information',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Storage location info
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.green.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.phone_android, color: Colors.green.shade700, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'Local Device Storage',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.green.shade700,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    _buildStorageItem(
                      icon: Icons.speed,
                      title: 'Hive Database',
                      description: 'Fast access for quick loading',
                      color: Colors.green.shade600,
                    ),
                    const SizedBox(height: 8),
                    _buildStorageItem(
                      icon: Icons.backup,
                      title: 'SQLite Database',
                      description: 'Reliable backup and complex queries',
                      color: Colors.green.shade600,
                    ),
                    const SizedBox(height: 8),
                    _buildStorageItem(
                      icon: Icons.security,
                      title: 'Secure Storage',
                      description: 'Data stays on your device only',
                      color: Colors.green.shade600,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Data statistics
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.analytics, color: Colors.blue.shade700, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'Your Data Statistics',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade700,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    _buildStatItem('Total Measurements', '${_measurements.length}'),
                    _buildStatItem('Field Measurements', '${_measurements.where((m) => m.type == 'field').length}'),
                    _buildStatItem('Distance Measurements', '${_measurements.where((m) => m.type == 'distance').length}'),
                    _buildStatItem('Location Markers', '${_measurements.where((m) => m.type == 'marker').length}'),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Export options
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.orange.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.file_download, color: Colors.orange.shade700, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'Export & Backup Options',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.orange.shade700,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    _buildStorageItem(
                      icon: Icons.picture_as_pdf,
                      title: 'PDF Reports',
                      description: 'Professional measurement reports',
                      color: Colors.orange.shade600,
                    ),
                    const SizedBox(height: 8),
                    _buildStorageItem(
                      icon: Icons.code,
                      title: 'JSON Export',
                      description: 'Data interchange format',
                      color: Colors.orange.shade600,
                    ),
                    const SizedBox(height: 8),
                    _buildStorageItem(
                      icon: Icons.table_chart,
                      title: 'CSV Export',
                      description: 'Spreadsheet compatible format',
                      color: Colors.orange.shade600,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context);
              _exportAllMeasurements();
            },
            icon: const Icon(Icons.file_download),
            label: const Text('Export All'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStorageItem({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
  }) {
    return Row(
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: color,
                  fontSize: 14,
                ),
              ),
              Text(
                description,
                style: TextStyle(
                  color: color.withOpacity(0.8),
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.blue.shade600,
              fontSize: 14,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.blue.shade700,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _formatDate(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }

  String _formatDistance(double meters) {
    if (meters < 1000) {
      return '${meters.toStringAsFixed(1)}m';
    } else {
      return '${(meters / 1000).toStringAsFixed(2)}km';
    }
  }

  String _formatArea(double sqMeters) {
    if (sqMeters < 1) {
      return '${sqMeters.toStringAsFixed(3)} m²';
    } else if (sqMeters < 10000) {
      return '${sqMeters.toStringAsFixed(1)} m²';
    } else {
      double hectares = sqMeters / 10000;
      double acres = sqMeters / 4046.86;
      return '${hectares.toStringAsFixed(2)} ha (${acres.toStringAsFixed(2)} acres)';
    }
  }
}
