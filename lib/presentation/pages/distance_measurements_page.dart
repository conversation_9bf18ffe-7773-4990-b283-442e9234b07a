import 'package:flutter/material.dart';
import '../services/measurement_storage_service.dart';
import '../services/measurement_export_service.dart';
import '../widgets/banner_ad_widget.dart';
import 'measurement_viewer_page.dart';

class DistanceMeasurementsPage extends StatefulWidget {
  const DistanceMeasurementsPage({Key? key}) : super(key: key);

  @override
  State<DistanceMeasurementsPage> createState() => _DistanceMeasurementsPageState();
}

class _DistanceMeasurementsPageState extends State<DistanceMeasurementsPage> {
  final MeasurementStorageService _storageService = MeasurementStorageService();
  List<MeasurementRecord> _distanceMeasurements = [];
  bool _isLoading = true;
  String _searchQuery = '';
  String _sortBy = 'date';

  @override
  void initState() {
    super.initState();
    _initializeStorage();
  }

  Future<void> _initializeStorage() async {
    await _storageService.initialize();
    await _loadDistanceMeasurements();
  }

  Future<void> _loadDistanceMeasurements() async {
    setState(() => _isLoading = true);
    try {
      final measurements = await _storageService.getMeasurementsByType('distance');
      setState(() {
        _distanceMeasurements = measurements;
        _isLoading = false;
      });
      _sortMeasurements();
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('Error loading distance measurements: $e');
    }
  }

  void _sortMeasurements() {
    setState(() {
      switch (_sortBy) {
        case 'name':
          _distanceMeasurements.sort((a, b) => a.name.compareTo(b.name));
          break;
        case 'distance':
          _distanceMeasurements.sort((a, b) => b.distance.compareTo(a.distance));
          break;
        case 'date':
        default:
          _distanceMeasurements.sort((a, b) => b.timestamp.compareTo(a.timestamp));
          break;
      }
    });
  }

  List<MeasurementRecord> get _filteredMeasurements {
    if (_searchQuery.isEmpty) return _distanceMeasurements;
    
    return _distanceMeasurements.where((measurement) {
      return measurement.name.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Distance Measurements'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDistanceMeasurements,
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.sort),
            onSelected: (value) {
              setState(() => _sortBy = value);
              _sortMeasurements();
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'date', child: Text('Sort by Date')),
              const PopupMenuItem(value: 'name', child: Text('Sort by Name')),
              const PopupMenuItem(value: 'distance', child: Text('Sort by Distance')),
            ],
          ),
        ],
      ),
      
      body: Column(
        children: [
          // Top Banner Ad
          const BannerAdWidget(
            margin: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            alwaysShow: true,
          ),
          
          // Category header
          Container(
            width: double.infinity,
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue.shade50, Colors.blue.shade100],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade200,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.straighten_rounded, color: Colors.blue.shade700),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Distance Measurements',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade800,
                        ),
                      ),
                      Text(
                        '${_distanceMeasurements.length} distance measurements saved',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.blue.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Search bar
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Search distance measurements...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.grey.shade100,
              ),
              onChanged: (value) {
                setState(() => _searchQuery = value);
              },
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Measurements list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredMeasurements.isEmpty
                    ? _buildEmptyState()
                    : _buildMeasurementsList(),
          ),
        ],
      ),
      
      floatingActionButton: _distanceMeasurements.isNotEmpty
          ? FloatingActionButton.extended(
              heroTag: "export_distance_fab",
              onPressed: _exportAllDistances,
              icon: const Icon(Icons.file_download),
              label: const Text('Export Distances'),
              backgroundColor: Colors.blue,
            )
          : null,
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.straighten_rounded,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isEmpty 
                ? 'No distance measurements yet'
                : 'No distances found',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isEmpty
                ? 'Create distance measurements to see them here'
                : 'Try a different search term',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey.shade500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMeasurementsList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _filteredMeasurements.length,
      itemBuilder: (context, index) {
        final measurement = _filteredMeasurements[index];
        return _buildDistanceCard(measurement);
      },
    );
  }

  Widget _buildDistanceCard(MeasurementRecord measurement) {
    final segmentDistances = measurement.metadata['segment_distances'] as List<dynamic>? ?? [];
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _showDistanceDetails(measurement),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              colors: [Colors.blue.shade50, Colors.white],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade100,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(Icons.straighten_rounded, size: 16, color: Colors.blue.shade700),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      measurement.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Text(
                    _formatDate(measurement.timestamp),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Distance details
              Row(
                children: [
                  Expanded(
                    child: _buildDetailItem(
                      icon: Icons.straighten,
                      label: 'Total Distance',
                      value: _formatDistance(measurement.distance),
                      color: Colors.blue,
                    ),
                  ),
                  Expanded(
                    child: _buildDetailItem(
                      icon: Icons.location_on,
                      label: 'Points',
                      value: '${measurement.points.length}',
                      color: Colors.orange,
                    ),
                  ),
                  Expanded(
                    child: _buildDetailItem(
                      icon: Icons.timeline,
                      label: 'Segments',
                      value: '${segmentDistances.length}',
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _showDistanceDetails(measurement),
                      icon: const Icon(Icons.visibility, size: 16),
                      label: const Text('View'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.blue,
                        side: BorderSide(color: Colors.blue.shade300),
                      ),
                    ),
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _viewOnMap(measurement),
                      icon: const Icon(Icons.map, size: 16),
                      label: const Text('Map'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.orange,
                        side: BorderSide(color: Colors.orange.shade300),
                      ),
                    ),
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _exportDistance(measurement),
                      icon: const Icon(Icons.share, size: 16),
                      label: const Text('Export'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.green,
                        side: BorderSide(color: Colors.green.shade300),
                      ),
                    ),
                  ),
                  const SizedBox(width: 6),
                  IconButton(
                    onPressed: () => _deleteDistance(measurement),
                    icon: const Icon(Icons.delete_outline),
                    color: Colors.red,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, size: 20, color: color),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  void _showDistanceDetails(MeasurementRecord measurement) {
    final segmentDistances = measurement.metadata['segment_distances'] as List<dynamic>? ?? [];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(measurement.name),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Type', 'Distance Measurement'),
              _buildDetailRow('Points', '${measurement.points.length}'),
              _buildDetailRow('Total Distance', _formatDistance(measurement.distance)),
              _buildDetailRow('Segments', '${segmentDistances.length}'),
              _buildDetailRow('Date', _formatDateTime(measurement.timestamp)),
              if (measurement.metadata['notes']?.isNotEmpty == true)
                _buildDetailRow('Notes', measurement.metadata['notes']),
              
              if (segmentDistances.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Text(
                  'Segment Distances:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                ...segmentDistances.asMap().entries.map((entry) {
                  final index = entry.key;
                  final distance = entry.value;
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Text(
                      'Segment ${index + 1}: ${_formatDistance(distance.toDouble())}',
                      style: const TextStyle(fontSize: 12),
                    ),
                  );
                }).toList(),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _exportDistance(measurement);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
            child: const Text('Export'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Future<void> _exportDistance(MeasurementRecord measurement) async {
    try {
      _showLoadingDialog('Exporting distance...');
      
      final pdfFile = await MeasurementExportService.exportMeasurementToPDF(
        measurement: measurement,
        additionalNotes: 'Distance measurement exported from GPS Map Camera',
      );
      
      Navigator.pop(context); // Close loading dialog
      
      await MeasurementExportService.shareMeasurementFile(
        file: pdfFile,
        measurementName: measurement.name,
      );
      
      _showSuccessSnackBar('Distance exported successfully!');
    } catch (e) {
      Navigator.pop(context); // Close loading dialog
      _showErrorSnackBar('Error exporting distance: $e');
    }
  }

  Future<void> _exportAllDistances() async {
    if (_distanceMeasurements.isEmpty) return;
    
    try {
      _showLoadingDialog('Exporting all distances...');
      
      final pdfFile = await MeasurementExportService.exportMultipleMeasurementsToPDF(
        measurements: _distanceMeasurements,
        reportTitle: 'Distance Measurements Report',
        additionalNotes: 'Complete distance measurements report from GPS Map Camera',
      );
      
      Navigator.pop(context); // Close loading dialog
      
      await MeasurementExportService.shareMeasurementFile(
        file: pdfFile,
        measurementName: 'All Distance Measurements',
      );
      
      _showSuccessSnackBar('All distances exported successfully!');
    } catch (e) {
      Navigator.pop(context); // Close loading dialog
      _showErrorSnackBar('Error exporting distances: $e');
    }
  }

  Future<void> _deleteDistance(MeasurementRecord measurement) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Distance'),
        content: Text('Are you sure you want to delete "${measurement.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
    
    if (confirmed == true) {
      try {
        await _storageService.deleteMeasurement(measurement.id);
        await _loadDistanceMeasurements();
        _showSuccessSnackBar('Distance deleted successfully');
      } catch (e) {
        _showErrorSnackBar('Error deleting distance: $e');
      }
    }
  }

  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Text(message),
          ],
        ),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _viewOnMap(MeasurementRecord measurement) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MeasurementViewerPage(measurement: measurement),
      ),
    );
  }

  // Helper methods
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _formatDate(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }

  String _formatDistance(double meters) {
    if (meters < 1000) {
      return '${meters.toStringAsFixed(1)}m';
    } else {
      return '${(meters / 1000).toStringAsFixed(2)}km';
    }
  }
}
