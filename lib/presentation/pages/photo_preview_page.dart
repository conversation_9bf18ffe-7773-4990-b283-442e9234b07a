import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_state_provider.dart';
import '../../domain/entities/location_entity.dart';
import '../../domain/entities/photo_entity.dart';
import '../../data/services/geocoding_service.dart';

class PhotoPreviewPage extends StatefulWidget {
  final String imagePath;
  final LocationEntity? location;

  const PhotoPreviewPage({
    Key? key,
    required this.imagePath,
    this.location,
  }) : super(key: key);

  @override
  State<PhotoPreviewPage> createState() => _PhotoPreviewPageState();
}

class _PhotoPreviewPageState extends State<PhotoPreviewPage> {
  final GeocodingService _geocodingService = GeocodingService();
  String? _address;
  bool _isLoadingAddress = false;

  @override
  void initState() {
    super.initState();
    if (widget.location != null) {
      _getAddress();
    }
  }

  Future<void> _getAddress() async {
    setState(() {
      _isLoadingAddress = true;
    });

    try {
      final address = await _geocodingService.getAddressFromLatLng(
        widget.location!.latitude,
        widget.location!.longitude,
      );

      setState(() {
        _address = address;
        _isLoadingAddress = false;
      });
    } catch (e) {
      setState(() {
        _address = "Address not available";
        _isLoadingAddress = false;
      });
    }
  }

  void _savePhoto() {
    final appState = Provider.of<AppProvider>(context, listen: false);
    final photo = PhotoEntity(
      id: DateTime
          .now()
          .millisecondsSinceEpoch
          .toString(),
      path: widget.imagePath,
      location: widget.location,
      address: _address,
      timestamp: DateTime.now(),
    );
    appState.addPhoto(photo);
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Photo Preview'),
        actions: [
          IconButton(
            icon: const Icon(Icons.check),
            onPressed: _savePhoto,
          ),
        ],
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(
            child: Image.file(
              File(widget.imagePath),
              fit: BoxFit.contain,
            ),
          ),
          if (widget.location != null)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Location:',
                    style: Theme
                        .of(context)
                        .textTheme
                        .titleMedium,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Lat: ${widget.location!.latitude.toStringAsFixed(6)}, '
                        'Lng: ${widget.location!.longitude.toStringAsFixed(6)}',
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Address:',
                    style: Theme
                        .of(context)
                        .textTheme
                        .titleMedium,
                  ),
                  const SizedBox(height: 4),
                  _isLoadingAddress
                      ? const CircularProgressIndicator()
                      : Text(_address ?? 'Address not available'),
                ],
              ),
            ),
        ],
      ),
    );
  }
}