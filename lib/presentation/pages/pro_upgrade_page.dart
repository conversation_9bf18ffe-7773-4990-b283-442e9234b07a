import 'package:flutter/material.dart';
import '../../data/services/razorpay_service.dart';
import '../../data/services/pro_status_manager.dart';

class ProUpgradePage extends StatefulWidget {
  const ProUpgradePage({super.key});

  @override
  State<ProUpgradePage> createState() => _ProUpgradePageState();
}

class _ProUpgradePageState extends State<ProUpgradePage> {
  final RazorpayService _razorpayService = RazorpayService();
  final ProStatusManager _proManager = ProStatusManager();

  String _selectedPlan = 'yearly';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // Razorpay is already initialized in main.dart
    print('ProUpgradePage initialized');
  }

  @override
  void dispose() {
    _razorpayService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // Allow back navigation if not loading
        if (!_isLoading) {
          return true;
        }

        // Show confirmation if payment is in progress
        final shouldPop = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Cancel Payment?'),
            content: const Text('Payment is in progress. Are you sure you want to go back?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Stay'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Cancel Payment'),
              ),
            ],
          ),
        );

        if (shouldPop == true) {
          setState(() => _isLoading = false);
        }

        return shouldPop ?? false;
      },
      child: Scaffold(
      appBar: AppBar(
        title: const Text('Upgrade to Pro'),
        backgroundColor: Colors.purple,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Pro Benefits Card
            Card(
              color: Colors.purple[50],
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.diamond, color: Colors.purple[600], size: 30),
                        const SizedBox(width: 12),
                        const Text(
                          'GPS Map Camera Pro',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Unlock Premium Features:',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildBenefitItem('🚫', 'No Ads - Completely ad-free experience'),
                    _buildBenefitItem('🎨', 'All Themes - Access to all premium themes'),
                    _buildBenefitItem('📸', 'Advanced Camera Features'),
                    _buildBenefitItem('🗺️', 'Premium Map Styles'),
                    _buildBenefitItem('💾', 'Unlimited Cloud Storage'),
                    _buildBenefitItem('⚡', 'Priority Support'),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Subscription Plans
            const Text(
              'Choose Your Plan',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            _buildPlanCard('monthly', '₹99', 'Monthly', 'Billed monthly'),
            _buildPlanCard('yearly', '₹999', 'Yearly', 'Save 17% - Most Popular!'),
            _buildPlanCard('lifetime', '₹2,999', 'Lifetime', 'One-time payment'),
            
            const SizedBox(height: 24),
            
            // Purchase Button
            SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _purchasePro,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isLoading
                    ? const CircularProgressIndicator(color: Colors.white)
                    : Text(
                        'Purchase Pro - ₹${RazorpayService.paymentPlans[_selectedPlan]!['amount']}',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Test Mode Notice
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange[200]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: Colors.orange[600]),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Test Mode: Use test card 4111 1111 1111 1111 for testing',
                      style: TextStyle(
                        color: Colors.orange[800],
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      ), // Close WillPopScope child (Scaffold)
    ); // Close WillPopScope
  }

  Widget _buildBenefitItem(String emoji, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Text(emoji, style: const TextStyle(fontSize: 16)),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlanCard(String planKey, String price, String title, String subtitle) {
    final isSelected = _selectedPlan == planKey;
    final isPopular = planKey == 'yearly';
    
    return GestureDetector(
      onTap: () => setState(() => _selectedPlan = planKey),
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? Colors.purple : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12),
          color: isSelected ? Colors.purple[50] : Colors.white,
        ),
        child: Stack(
          children: [
            if (isPopular)
              Positioned(
                top: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: const BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(12),
                      bottomLeft: Radius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'POPULAR',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Radio<String>(
                    value: planKey,
                    groupValue: _selectedPlan,
                    onChanged: (value) => setState(() => _selectedPlan = value!),
                    activeColor: Colors.purple,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          subtitle,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Text(
                    price,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.purple,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _purchasePro() async {
    setState(() => _isLoading = true);
    print('Starting Pro purchase for plan: $_selectedPlan');

    try {
      await _razorpayService.buyProSubscription(
        planType: _selectedPlan,
        userEmail: '<EMAIL>', // Default email
        userPhone: '9999999999', // Default phone
        onSuccess: () {
          print('Payment successful');
          if (mounted) {
            setState(() => _isLoading = false);
            _showSuccessDialog();
          }
        },
        onError: () {
          print('Payment failed or cancelled');
          if (mounted) {
            setState(() => _isLoading = false);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Payment cancelled or failed. Please try again.'),
                backgroundColor: Colors.orange,
                duration: Duration(seconds: 2),
              ),
            );
          }
        },
      );
    } catch (e) {
      print('Error starting payment: $e');
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }



  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 30),
            SizedBox(width: 12),
            Text('Success!'),
          ],
        ),
        content: const Text(
          'Congratulations! You are now a Pro user. All ads have been removed and premium features are unlocked.',
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Go back to previous page
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
            child: const Text('Continue', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
