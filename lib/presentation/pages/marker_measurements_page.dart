import 'package:flutter/material.dart';
import '../services/measurement_storage_service.dart';
import '../services/measurement_export_service.dart';
import '../widgets/banner_ad_widget.dart';
import 'measurement_viewer_page.dart';

class MarkerMeasurementsPage extends StatefulWidget {
  const MarkerMeasurementsPage({Key? key}) : super(key: key);

  @override
  State<MarkerMeasurementsPage> createState() => _MarkerMeasurementsPageState();
}

class _MarkerMeasurementsPageState extends State<MarkerMeasurementsPage> {
  final MeasurementStorageService _storageService = MeasurementStorageService();
  List<MeasurementRecord> _markerMeasurements = [];
  bool _isLoading = true;
  String _searchQuery = '';
  String _sortBy = 'date';
  String _filterCategory = 'all';

  final List<String> _categories = [
    'all', 'General', 'Important', 'Landmark', 'Building', 'Parking',
    'Restaurant', 'Shop', 'Hospital', 'School', 'Office', 'Home', 'Other'
  ];

  final Map<String, IconData> _categoryIcons = {
    'General': Icons.place,
    'Important': Icons.star,
    'Landmark': Icons.location_city,
    'Building': Icons.business,
    'Parking': Icons.local_parking,
    'Restaurant': Icons.restaurant,
    'Shop': Icons.shopping_cart,
    'Hospital': Icons.local_hospital,
    'School': Icons.school,
    'Office': Icons.work,
    'Home': Icons.home,
    'Other': Icons.location_on,
  };

  final Map<String, Color> _categoryColors = {
    'General': Colors.blue,
    'Important': Colors.red,
    'Landmark': Colors.purple,
    'Building': Colors.brown,
    'Parking': Colors.green,
    'Restaurant': Colors.orange,
    'Shop': Colors.pink,
    'Hospital': Colors.red,
    'School': Colors.blue,
    'Office': Colors.grey,
    'Home': Colors.green,
    'Other': Colors.black,
  };

  @override
  void initState() {
    super.initState();
    _initializeStorage();
  }

  Future<void> _initializeStorage() async {
    await _storageService.initialize();
    await _loadMarkerMeasurements();
  }

  Future<void> _loadMarkerMeasurements() async {
    setState(() => _isLoading = true);
    try {
      final measurements = await _storageService.getMeasurementsByType('marker');
      setState(() {
        _markerMeasurements = measurements;
        _isLoading = false;
      });
      _sortMeasurements();
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('Error loading marker measurements: $e');
    }
  }

  void _sortMeasurements() {
    setState(() {
      switch (_sortBy) {
        case 'name':
          _markerMeasurements.sort((a, b) => a.name.compareTo(b.name));
          break;
        case 'category':
          _markerMeasurements.sort((a, b) {
            final categoryA = a.metadata['category'] ?? 'Other';
            final categoryB = b.metadata['category'] ?? 'Other';
            return categoryA.compareTo(categoryB);
          });
          break;
        case 'date':
        default:
          _markerMeasurements.sort((a, b) => b.timestamp.compareTo(a.timestamp));
          break;
      }
    });
  }

  List<MeasurementRecord> get _filteredMeasurements {
    var filtered = _markerMeasurements;
    
    // Filter by category
    if (_filterCategory != 'all') {
      filtered = filtered.where((measurement) {
        final category = measurement.metadata['category'] ?? 'Other';
        return category == _filterCategory;
      }).toList();
    }
    
    // Filter by search query
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((measurement) {
        return measurement.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               (measurement.metadata['category'] ?? '').toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }
    
    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Location Markers'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadMarkerMeasurements,
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.sort),
            onSelected: (value) {
              setState(() => _sortBy = value);
              _sortMeasurements();
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'date', child: Text('Sort by Date')),
              const PopupMenuItem(value: 'name', child: Text('Sort by Name')),
              const PopupMenuItem(value: 'category', child: Text('Sort by Category')),
            ],
          ),
        ],
      ),
      
      body: Column(
        children: [
          // Top Banner Ad
          const BannerAdWidget(
            margin: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            alwaysShow: true,
          ),
          
          // Category header
          Container(
            width: double.infinity,
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.orange.shade50, Colors.orange.shade100],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.orange.shade200),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade200,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.place_rounded, color: Colors.orange.shade700),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Location Markers',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.orange.shade800,
                        ),
                      ),
                      Text(
                        '${_markerMeasurements.length} location markers saved',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.orange.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Search and filter bar
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: InputDecoration(
                      hintText: 'Search markers...',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: Colors.grey.shade100,
                    ),
                    onChanged: (value) {
                      setState(() => _searchQuery = value);
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: DropdownButton<String>(
                    value: _filterCategory,
                    underline: const SizedBox(),
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    items: _categories.map((category) {
                      return DropdownMenuItem(
                        value: category,
                        child: Text(category == 'all' ? 'All Categories' : category),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() => _filterCategory = value ?? 'all');
                    },
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Measurements list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredMeasurements.isEmpty
                    ? _buildEmptyState()
                    : _buildMeasurementsList(),
          ),
        ],
      ),
      
      floatingActionButton: _markerMeasurements.isNotEmpty
          ? FloatingActionButton.extended(
              heroTag: "export_marker_fab",
              onPressed: _exportAllMarkers,
              icon: const Icon(Icons.file_download),
              label: const Text('Export Markers'),
              backgroundColor: Colors.orange,
            )
          : null,
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.place_rounded,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isEmpty && _filterCategory == 'all'
                ? 'No location markers yet'
                : 'No markers found',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isEmpty && _filterCategory == 'all'
                ? 'Create location markers to see them here'
                : 'Try a different search or filter',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey.shade500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMeasurementsList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _filteredMeasurements.length,
      itemBuilder: (context, index) {
        final measurement = _filteredMeasurements[index];
        return _buildMarkerCard(measurement);
      },
    );
  }

  Widget _buildMarkerCard(MeasurementRecord measurement) {
    final category = measurement.metadata['category'] ?? 'Other';
    final description = measurement.metadata['description'] ?? '';
    final address = measurement.metadata['address'] ?? '';
    final icon = _categoryIcons[category] ?? Icons.place;
    final color = _categoryColors[category] ?? Colors.grey;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _showMarkerDetails(measurement),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              colors: [color.withOpacity(0.05), Colors.white],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(icon, size: 16, color: color),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      measurement.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      category,
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // Description
              if (description.isNotEmpty) ...[
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade700,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
              ],
              
              // Address
              if (address.isNotEmpty) ...[
                Row(
                  children: [
                    Icon(Icons.location_on, size: 14, color: Colors.grey.shade600),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        address,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
              ],
              
              // Footer row
              Row(
                children: [
                  Text(
                    _formatDate(measurement.timestamp),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const Spacer(),
                  OutlinedButton.icon(
                    onPressed: () => _showMarkerDetails(measurement),
                    icon: const Icon(Icons.visibility, size: 14),
                    label: const Text('View'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: color,
                      side: BorderSide(color: color.withOpacity(0.5)),
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      minimumSize: Size.zero,
                    ),
                  ),
                  const SizedBox(width: 6),
                  OutlinedButton.icon(
                    onPressed: () => _viewOnMap(measurement),
                    icon: const Icon(Icons.map, size: 14),
                    label: const Text('Map'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.orange,
                      side: BorderSide(color: Colors.orange.withOpacity(0.5)),
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      minimumSize: Size.zero,
                    ),
                  ),
                  const SizedBox(width: 6),
                  IconButton(
                    onPressed: () => _deleteMarker(measurement),
                    icon: const Icon(Icons.delete_outline),
                    color: Colors.red,
                    constraints: const BoxConstraints(),
                    padding: EdgeInsets.zero,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showMarkerDetails(MeasurementRecord measurement) {
    final category = measurement.metadata['category'] ?? 'Other';
    final description = measurement.metadata['description'] ?? '';
    final address = measurement.metadata['address'] ?? '';
    final coordinates = measurement.points.isNotEmpty ? measurement.points.first : null;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(measurement.name),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Type', 'Location Marker'),
              _buildDetailRow('Category', category),
              if (description.isNotEmpty)
                _buildDetailRow('Description', description),
              if (address.isNotEmpty)
                _buildDetailRow('Address', address),
              if (coordinates != null) ...[
                _buildDetailRow('Latitude', coordinates['latitude']?.toStringAsFixed(6) ?? ''),
                _buildDetailRow('Longitude', coordinates['longitude']?.toStringAsFixed(6) ?? ''),
              ],
              _buildDetailRow('Date', _formatDateTime(measurement.timestamp)),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _exportMarker(measurement);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('Export'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Future<void> _exportMarker(MeasurementRecord measurement) async {
    try {
      _showLoadingDialog('Exporting marker...');
      
      final pdfFile = await MeasurementExportService.exportMeasurementToPDF(
        measurement: measurement,
        additionalNotes: 'Location marker exported from GPS Map Camera',
      );
      
      Navigator.pop(context); // Close loading dialog
      
      await MeasurementExportService.shareMeasurementFile(
        file: pdfFile,
        measurementName: measurement.name,
      );
      
      _showSuccessSnackBar('Marker exported successfully!');
    } catch (e) {
      Navigator.pop(context); // Close loading dialog
      _showErrorSnackBar('Error exporting marker: $e');
    }
  }

  Future<void> _exportAllMarkers() async {
    if (_markerMeasurements.isEmpty) return;
    
    try {
      _showLoadingDialog('Exporting all markers...');
      
      final pdfFile = await MeasurementExportService.exportMultipleMeasurementsToPDF(
        measurements: _markerMeasurements,
        reportTitle: 'Location Markers Report',
        additionalNotes: 'Complete location markers report from GPS Map Camera',
      );
      
      Navigator.pop(context); // Close loading dialog
      
      await MeasurementExportService.shareMeasurementFile(
        file: pdfFile,
        measurementName: 'All Location Markers',
      );
      
      _showSuccessSnackBar('All markers exported successfully!');
    } catch (e) {
      Navigator.pop(context); // Close loading dialog
      _showErrorSnackBar('Error exporting markers: $e');
    }
  }

  Future<void> _deleteMarker(MeasurementRecord measurement) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Marker'),
        content: Text('Are you sure you want to delete "${measurement.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
    
    if (confirmed == true) {
      try {
        await _storageService.deleteMeasurement(measurement.id);
        await _loadMarkerMeasurements();
        _showSuccessSnackBar('Marker deleted successfully');
      } catch (e) {
        _showErrorSnackBar('Error deleting marker: $e');
      }
    }
  }

  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Text(message),
          ],
        ),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _viewOnMap(MeasurementRecord measurement) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MeasurementViewerPage(measurement: measurement),
      ),
    );
  }

  // Helper methods
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _formatDate(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }
}
