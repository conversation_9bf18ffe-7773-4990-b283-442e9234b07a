import 'package:flutter/material.dart';
import 'dart:math';
import '../services/measurement_storage_service.dart';
import '../services/measurement_export_service.dart';
import '../widgets/banner_ad_widget.dart';
import 'measurement_viewer_page.dart';

class FieldMeasurementsPage extends StatefulWidget {
  const FieldMeasurementsPage({Key? key}) : super(key: key);

  @override
  State<FieldMeasurementsPage> createState() => _FieldMeasurementsPageState();
}

class _FieldMeasurementsPageState extends State<FieldMeasurementsPage> {
  final MeasurementStorageService _storageService = MeasurementStorageService();
  List<MeasurementRecord> _fieldMeasurements = [];
  bool _isLoading = true;
  String _searchQuery = '';
  String _sortBy = 'date';

  @override
  void initState() {
    super.initState();
    _initializeStorage();
  }

  Future<void> _initializeStorage() async {
    await _storageService.initialize();
    await _loadFieldMeasurements();
  }

  Future<void> _loadFieldMeasurements() async {
    setState(() => _isLoading = true);
    try {
      final measurements = await _storageService.getMeasurementsByType('field');
      setState(() {
        _fieldMeasurements = measurements;
        _isLoading = false;
      });
      _sortMeasurements();
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('Error loading field measurements: $e');
    }
  }

  void _sortMeasurements() {
    setState(() {
      switch (_sortBy) {
        case 'name':
          _fieldMeasurements.sort((a, b) => a.name.compareTo(b.name));
          break;
        case 'area':
          _fieldMeasurements.sort((a, b) => b.area.compareTo(a.area));
          break;
        case 'date':
        default:
          _fieldMeasurements.sort((a, b) => b.timestamp.compareTo(a.timestamp));
          break;
      }
    });
  }

  List<MeasurementRecord> get _filteredMeasurements {
    if (_searchQuery.isEmpty) return _fieldMeasurements;
    
    return _fieldMeasurements.where((measurement) {
      return measurement.name.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Field Measurements'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadFieldMeasurements,
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.sort),
            onSelected: (value) {
              setState(() => _sortBy = value);
              _sortMeasurements();
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'date', child: Text('Sort by Date')),
              const PopupMenuItem(value: 'name', child: Text('Sort by Name')),
              const PopupMenuItem(value: 'area', child: Text('Sort by Area')),
            ],
          ),
        ],
      ),
      
      body: SafeArea(
        child: Column(
          children: [
          // Top Banner Ad
          const BannerAdWidget(
            margin: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            alwaysShow: true,
          ),
          
          // Category header
          Container(
            width: double.infinity,
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.green.shade50, Colors.green.shade100],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.green.shade200),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green.shade200,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.crop_free_rounded, color: Colors.green.shade700),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Field Measurements',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade800,
                        ),
                      ),
                      Text(
                        '${_fieldMeasurements.length} field measurements saved',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.green.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Search bar
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Search field measurements...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.grey.shade100,
              ),
              onChanged: (value) {
                setState(() => _searchQuery = value);
              },
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Measurements list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredMeasurements.isEmpty
                    ? _buildEmptyState()
                    : _buildMeasurementsList(),
          ),
          ],
        ),
      ),
      
      floatingActionButton: _fieldMeasurements.isNotEmpty
          ? FloatingActionButton.extended(
              heroTag: "export_field_fab",
              onPressed: _exportAllFields,
              icon: const Icon(Icons.file_download),
              label: const Text('Export Fields'),
              backgroundColor: Colors.green,
            )
          : null,
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.crop_free_rounded,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isEmpty 
                ? 'No field measurements yet'
                : 'No fields found',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isEmpty
                ? 'Create field measurements to see them here'
                : 'Try a different search term',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey.shade500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMeasurementsList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _filteredMeasurements.length,
      itemBuilder: (context, index) {
        final measurement = _filteredMeasurements[index];
        return _buildFieldCard(measurement);
      },
    );
  }

  Widget _buildFieldCard(MeasurementRecord measurement) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Responsive sizing based on screen width
        final screenWidth = MediaQuery.of(context).size.width;
        final isSmallScreen = screenWidth < 360;
        final isMediumScreen = screenWidth < 600;

        // Adaptive padding and spacing
        final cardPadding = isSmallScreen ? 10.0 : (isMediumScreen ? 12.0 : 16.0);
        final cardMargin = isSmallScreen ? 8.0 : 12.0;
        final borderRadius = isSmallScreen ? 8.0 : 12.0;

        return Card(
          margin: EdgeInsets.only(bottom: cardMargin),
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(borderRadius)),
          child: InkWell(
            borderRadius: BorderRadius.circular(borderRadius),
            onTap: () => _showFieldDetails(measurement),
            child: Container(
              padding: EdgeInsets.all(cardPadding),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(borderRadius),
                gradient: LinearGradient(
                  colors: [Colors.green.shade50, Colors.white],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row with responsive sizing
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(isSmallScreen ? 4 : 6),
                    decoration: BoxDecoration(
                      color: Colors.green.shade100,
                      borderRadius: BorderRadius.circular(isSmallScreen ? 4 : 6),
                    ),
                    child: Icon(
                      Icons.crop_free_rounded,
                      size: isSmallScreen ? 14 : 16,
                      color: Colors.green.shade700
                    ),
                  ),
                  SizedBox(width: isSmallScreen ? 6 : 8),
                  Expanded(
                    child: Text(
                      measurement.name,
                      style: TextStyle(
                        fontSize: isSmallScreen ? 13 : (isMediumScreen ? 13 : 15),
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Text(
                    _formatDate(measurement.timestamp),
                    style: TextStyle(
                      fontSize: isSmallScreen ? 10 : 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),

              SizedBox(height: isSmallScreen ? 8 : 12),

              // Field details with responsive layout
              Row(
                children: [
                  Expanded(
                    child: _buildDetailItem(
                      icon: Icons.crop_free,
                      label: 'Area',
                      value: _formatArea(measurement.area),
                      color: Colors.green,
                      isSmallScreen: isSmallScreen,
                      isMediumScreen: isMediumScreen,
                    ),
                  ),
                  Expanded(
                    child: _buildDetailItem(
                      icon: Icons.straighten,
                      label: 'Perimeter',
                      value: _formatDistance(measurement.perimeter),
                      color: Colors.blue,
                      isSmallScreen: isSmallScreen,
                      isMediumScreen: isMediumScreen,
                    ),
                  ),
                  Expanded(
                    child: _buildDetailItem(
                      icon: Icons.location_on,
                      label: 'Points',
                      value: '${measurement.points.length}',
                      color: Colors.orange,
                      isSmallScreen: isSmallScreen,
                      isMediumScreen: isMediumScreen,
                    ),
                  ),
                ],
              ),

              // Show segment distances if available
              if (_hasSegmentDistances(measurement)) ...[
                SizedBox(height: isSmallScreen ? 8 : 12),
                _buildSegmentDistancesDisplay(measurement, isSmallScreen, isMediumScreen),
              ],

              SizedBox(height: isSmallScreen ? 8 : 12),

              // Action buttons with responsive layout
              isSmallScreen
                ? _buildCompactActionButtons(measurement)
                : _buildFullActionButtons(measurement, isSmallScreen, isMediumScreen),
            ],
          ),
        ),
      ),
    );
      },
    );
  }

  Widget _buildDetailItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
    required bool isSmallScreen,
    required bool isMediumScreen,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          size: isSmallScreen ? 16 : (isMediumScreen ? 18 : 20),
          color: color
        ),
        SizedBox(height: isSmallScreen ? 2 : 4),
        Text(
          value,
          style: TextStyle(
            fontSize: isSmallScreen ? 11 : (isMediumScreen ? 12 : 14),
            fontWeight: FontWeight.bold,
            color: color,
          ),
          textAlign: TextAlign.center,
          overflow: TextOverflow.ellipsis,
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: isSmallScreen ? 8 : (isMediumScreen ? 9 : 10),
            color: Colors.grey.shade600,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  // Compact action buttons for small screens
  Widget _buildCompactActionButtons(MeasurementRecord measurement) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _viewFieldOnMap(measurement),
                icon: const Icon(Icons.map, size: 12),
                label: const Text('Map', style: TextStyle(fontSize: 8)),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.blue,
                  side: BorderSide(color: Colors.blue.shade300),
                  padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                ),
              ),
            ),
            const SizedBox(width: 2),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _showFieldDetails(measurement),
                icon: const Icon(Icons.visibility, size: 12),
                label: const Text('View', style: TextStyle(fontSize: 8)),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.green,
                  side: BorderSide(color: Colors.green.shade300),
                  padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _exportField(measurement),
                icon: const Icon(Icons.share, size: 12),
                label: const Text('Export', style: TextStyle(fontSize: 8)),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.orange,
                  side: BorderSide(color: Colors.orange.shade300),
                  padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                ),
              ),
            ),
            const SizedBox(width: 2),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _deleteField(measurement),
                icon: const Icon(Icons.delete_outline, size: 12),
                label: const Text('Delete', style: TextStyle(fontSize: 8)),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.red,
                  side: BorderSide(color: Colors.red.shade300),
                  padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Full action buttons for medium and large screens
  Widget _buildFullActionButtons(MeasurementRecord measurement, bool isSmallScreen, bool isMediumScreen) {
    final iconSize = isMediumScreen ? 12.0 : 14.0;
    final fontSize = isMediumScreen ? 9.0 : 10.0;
    final spacing = isMediumScreen ? 3.0 : 4.0;

    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => _viewFieldOnMap(measurement),
            icon: Icon(Icons.map, size: iconSize),
            label: Text('Map', style: TextStyle(fontSize: fontSize)),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.blue,
              side: BorderSide(color: Colors.blue.shade300),
            ),
          ),
        ),
        SizedBox(width: spacing),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => _showFieldDetails(measurement),
            icon: Icon(Icons.visibility, size: iconSize),
            label: Text('View', style: TextStyle(fontSize: fontSize)),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.green,
              side: BorderSide(color: Colors.green.shade300),
            ),
          ),
        ),
        SizedBox(width: spacing),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => _exportField(measurement),
            icon: Icon(Icons.share, size: iconSize),
            label: Text('Export', style: TextStyle(fontSize: fontSize)),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.orange,
              side: BorderSide(color: Colors.orange.shade300),
            ),
          ),
        ),
        SizedBox(width: spacing),
        IconButton(
          onPressed: () => _deleteField(measurement),
          icon: Icon(Icons.delete_outline, size: iconSize),
          color: Colors.red,
          tooltip: 'Delete Field',
        ),
      ],
    );
  }

  void _showFieldDetails(MeasurementRecord measurement) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(measurement.name),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Type', 'Field Measurement'),
              _buildDetailRow('Points', '${measurement.points.length}'),
              _buildDetailRow('Area', _formatArea(measurement.area)),
              _buildDetailRow('Perimeter', _formatDistance(measurement.perimeter)),
              _buildDetailRow('Date', _formatDateTime(measurement.timestamp)),
              if (measurement.metadata['notes']?.isNotEmpty == true)
                _buildDetailRow('Notes', measurement.metadata['notes']),
              if (_hasSegmentDistances(measurement)) ...[
                const SizedBox(height: 8),
                _buildDetailSegmentDistances(measurement),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _exportField(measurement);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
            child: const Text('Export'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  bool _hasSegmentDistances(MeasurementRecord measurement) {
    final segmentDistances = measurement.metadata['segment_distances'] as List<dynamic>?;
    return segmentDistances != null && segmentDistances.isNotEmpty;
  }

  Widget _buildSegmentDistancesDisplay(MeasurementRecord measurement, bool isSmallScreen, bool isMediumScreen) {
    final segmentDistances = measurement.metadata['segment_distances'] as List<dynamic>? ?? [];

    if (segmentDistances.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: EdgeInsets.all(isSmallScreen ? 8 : 12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.timeline,
                size: isSmallScreen ? 14 : 16,
                color: Colors.blue.shade700,
              ),
              SizedBox(width: isSmallScreen ? 4 : 6),
              Text(
                'Segment Distances',
                style: TextStyle(
                  fontSize: isSmallScreen ? 11 : (isMediumScreen ? 12 : 13),
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
              ),
            ],
          ),
          SizedBox(height: isSmallScreen ? 6 : 8),
          Wrap(
            spacing: isSmallScreen ? 4 : 6,
            runSpacing: isSmallScreen ? 2 : 4,
            children: [
              for (int i = 0; i < segmentDistances.length; i++)
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: isSmallScreen ? 6 : 8,
                    vertical: isSmallScreen ? 2 : 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade100,
                    borderRadius: BorderRadius.circular(isSmallScreen ? 4 : 6),
                    border: Border.all(color: Colors.blue.shade300),
                  ),
                  child: Text(
                    '${i + 1}→${i + 2}: ${_formatDistance(segmentDistances[i].toDouble())}',
                    style: TextStyle(
                      fontSize: isSmallScreen ? 9 : (isMediumScreen ? 10 : 11),
                      fontWeight: FontWeight.w600,
                      color: Colors.blue.shade800,
                    ),
                  ),
                ),
              // Add closing segment if it's a complete field (3+ points)
              if (measurement.points.length >= 3)
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: isSmallScreen ? 6 : 8,
                    vertical: isSmallScreen ? 2 : 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green.shade100,
                    borderRadius: BorderRadius.circular(isSmallScreen ? 4 : 6),
                    border: Border.all(color: Colors.green.shade300),
                  ),
                  child: Text(
                    '${measurement.points.length}→1: ${_formatDistance(_calculateClosingDistance(measurement))}',
                    style: TextStyle(
                      fontSize: isSmallScreen ? 9 : (isMediumScreen ? 10 : 11),
                      fontWeight: FontWeight.w600,
                      color: Colors.green.shade800,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  double _calculateClosingDistance(MeasurementRecord measurement) {
    if (measurement.points.length < 3) return 0.0;

    final firstPoint = measurement.points.first;
    final lastPoint = measurement.points.last;

    // Calculate distance between last and first point
    final lat1 = firstPoint['latitude']!;
    final lon1 = firstPoint['longitude']!;
    final lat2 = lastPoint['latitude']!;
    final lon2 = lastPoint['longitude']!;

    // Haversine formula for distance calculation
    const double earthRadius = 6371000; // Earth radius in meters
    final double dLat = (lat2 - lat1) * (3.14159265359 / 180);
    final double dLon = (lon2 - lon1) * (3.14159265359 / 180);
    final double a = (sin(dLat / 2) * sin(dLat / 2)) +
        (cos(lat1 * (3.14159265359 / 180)) * cos(lat2 * (3.14159265359 / 180)) *
         sin(dLon / 2) * sin(dLon / 2));
    final double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    return earthRadius * c;
  }

  Widget _buildDetailSegmentDistances(MeasurementRecord measurement) {
    final segmentDistances = measurement.metadata['segment_distances'] as List<dynamic>? ?? [];

    if (segmentDistances.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Segment Distances:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue.shade200),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Individual segment distances
              for (int i = 0; i < segmentDistances.length; i++)
                Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Point ${i + 1} → Point ${i + 2}:',
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.blue.shade700,
                        ),
                      ),
                      Text(
                        _formatDistance(segmentDistances[i].toDouble()),
                        style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w600,
                          color: Colors.blue.shade800,
                        ),
                      ),
                    ],
                  ),
                ),
              // Closing segment for complete fields
              if (measurement.points.length >= 3)
                Padding(
                  padding: const EdgeInsets.only(top: 4, bottom: 4),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Point ${measurement.points.length} → Point 1 (Closing):',
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.green.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        _formatDistance(_calculateClosingDistance(measurement)),
                        style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w600,
                          color: Colors.green.shade800,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Future<void> _exportField(MeasurementRecord measurement) async {
    try {
      _showLoadingDialog('Exporting field...');
      
      final pdfFile = await MeasurementExportService.exportMeasurementToPDF(
        measurement: measurement,
        additionalNotes: 'Field measurement exported from GPS Map Camera',
      );
      
      Navigator.pop(context); // Close loading dialog
      
      await MeasurementExportService.shareMeasurementFile(
        file: pdfFile,
        measurementName: measurement.name,
      );
      
      _showSuccessSnackBar('Field exported successfully!');
    } catch (e) {
      Navigator.pop(context); // Close loading dialog
      _showErrorSnackBar('Error exporting field: $e');
    }
  }

  Future<void> _exportAllFields() async {
    if (_fieldMeasurements.isEmpty) return;
    
    try {
      _showLoadingDialog('Exporting all fields...');
      
      final pdfFile = await MeasurementExportService.exportMultipleMeasurementsToPDF(
        measurements: _fieldMeasurements,
        reportTitle: 'Field Measurements Report',
        additionalNotes: 'Complete field measurements report from GPS Map Camera',
      );
      
      Navigator.pop(context); // Close loading dialog
      
      await MeasurementExportService.shareMeasurementFile(
        file: pdfFile,
        measurementName: 'All Field Measurements',
      );
      
      _showSuccessSnackBar('All fields exported successfully!');
    } catch (e) {
      Navigator.pop(context); // Close loading dialog
      _showErrorSnackBar('Error exporting fields: $e');
    }
  }

  Future<void> _deleteField(MeasurementRecord measurement) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Field'),
        content: Text('Are you sure you want to delete "${measurement.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
    
    if (confirmed == true) {
      try {
        await _storageService.deleteMeasurement(measurement.id);
        await _loadFieldMeasurements();
        _showSuccessSnackBar('Field deleted successfully');
      } catch (e) {
        _showErrorSnackBar('Error deleting field: $e');
      }
    }
  }

  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Text(message),
          ],
        ),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _viewFieldOnMap(MeasurementRecord measurement) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MeasurementViewerPage(
          measurement: measurement,
          returnPageTitle: 'Field Measurements',
        ),
      ),
    );
  }

  // Helper methods
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _formatDate(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }

  String _formatDistance(double meters) {
    if (meters < 1000) {
      return '${meters.toStringAsFixed(1)}m';
    } else {
      return '${(meters / 1000).toStringAsFixed(2)}km';
    }
  }

  String _formatArea(double sqMeters) {
    if (sqMeters < 1) {
      return '${sqMeters.toStringAsFixed(3)} m²';
    } else if (sqMeters < 10000) {
      return '${sqMeters.toStringAsFixed(1)} m²';
    } else {
      double hectares = sqMeters / 10000;
      double acres = sqMeters / 4046.86;
      return '${hectares.toStringAsFixed(2)} ha (${acres.toStringAsFixed(2)} acres)';
    }
  }
}
