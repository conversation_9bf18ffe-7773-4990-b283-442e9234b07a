import 'package:flutter/material.dart';
import '../../generated/l10n/app_localizations.dart';

class HelpTutorialPage extends StatelessWidget {
  const HelpTutorialPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n?.howToUse ?? 'How to Use'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionCard(
              title: '📷 Camera Features',
              content: [
                '• Tap the camera button to take photos with GPS location',
                '• Photos automatically include location coordinates',
                '• Timestamp and address are embedded in the image',
                '• Use the grid lines for better composition',
                '• Switch between front and back cameras',
              ],
            ),
            
            _buildSectionCard(
              title: '🗺️ Map View',
              content: [
                '• View all your photos on an interactive map',
                '• Tap on markers to see photo details',
                '• Switch between normal and satellite view',
                '• Zoom in/out to explore different areas',
                '• Long press to add custom markers',
              ],
            ),
            
            _buildSectionCard(
              title: '📏 Measurement Tools',
              content: [
                '• Field Measurement: Measure area of land/fields',
                '• Distance Measurement: Measure distances between points',
                '• Location Marker: Add and save specific locations',
                '• Choose between tap, walk, or hybrid input methods',
                '• Results are saved automatically',
              ],
            ),
            
            _buildSectionCard(
              title: '🖼️ Gallery',
              content: [
                '• Browse all your captured photos',
                '• View photos in grid or list format',
                '• Tap to view full-size images',
                '• Share photos with location data',
                '• Delete unwanted photos',
              ],
            ),
            
            _buildSectionCard(
              title: '⚙️ Settings',
              content: [
                '• Change app language preferences',
                '• Set measurement units (meters, feet, etc.)',
                '• Customize area and distance units',
                '• Access help and feedback options',
                '• Save your preferences automatically',
              ],
            ),
            
            _buildSectionCard(
              title: '💡 Tips & Tricks',
              content: [
                '• Enable location services for accurate GPS data',
                '• Take photos in good lighting for better quality',
                '• Use measurement tools for professional surveying',
                '• Regular backup of important photos',
                '• Check app permissions in device settings',
              ],
            ),
            
            const SizedBox(height: 20),
            
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.teal.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.teal.shade200),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.support_agent,
                    size: 48,
                    color: Colors.teal,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Need More Help?',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'If you need additional assistance, please use the Feedback option in Settings to contact our support team.',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required List<String> content,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.teal,
              ),
            ),
            const SizedBox(height: 12),
            ...content.map((item) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Text(
                item,
                style: const TextStyle(fontSize: 14, height: 1.4),
              ),
            )).toList(),
          ],
        ),
      ),
    );
  }
}
