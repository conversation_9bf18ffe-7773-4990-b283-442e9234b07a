import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import 'dart:math';
import 'dart:ui' as ui;
import 'dart:typed_data';
import '../services/measurement_storage_service.dart';
import '../widgets/banner_ad_widget.dart';
import '../providers/app_state_provider.dart';
import '../../generated/l10n/app_localizations.dart';

// Input methods for field measurement
enum InputMethod { tap, walk, hybrid }

class FieldMeasurementPage extends StatefulWidget {
  final LatLng initialLocation;
  final Function(Map<String, dynamic>) onFieldSaved;

  const FieldMeasurementPage({
    Key? key,
    required this.initialLocation,
    required this.onFieldSaved,
  }) : super(key: key);

  @override
  State<FieldMeasurementPage> createState() => _FieldMeasurementPageState();
}

class _FieldMeasurementPageState extends State<FieldMeasurementPage> {
  GoogleMapController? _mapController;
  Set<Marker> _markers = {};
  Set<Polygon> _polygons = {};
  List<Position> _fieldPoints = [];
  double _fieldArea = 0.0;
  double _perimeter = 0.0;
  List<double> _fieldSegmentDistances = [];

  // Input method selection
  InputMethod _inputMethod = InputMethod.tap;
  bool _isRecording = false;
  StreamSubscription<Position>? _positionStream;
  Position? _currentPosition;

  // Map view options
  bool _showSatelliteView = false;

  // Distance marker caching
  final Map<String, BitmapDescriptor> _distanceMarkerCache = {};

  @override
  void dispose() {
    _positionStream?.cancel();
    _distanceMarkerCache.clear(); // Clear distance marker cache
    super.dispose();
  }
  bool _isPolygonClosed = false;
  String _fieldName = '';
  String _fieldNotes = '';

  @override
  void initState() {
    super.initState();
    _initializeLocation();
  }

  Future<void> _initializeLocation() async {
    try {
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.best,
      );
      
      if (_mapController != null) {
        _mapController!.animateCamera(
          CameraUpdate.newLatLngZoom(
            LatLng(position.latitude, position.longitude),
            16.0,
          ),
        );
      }
    } catch (e) {
      if (kDebugMode) print('Error getting location: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n?.fieldMeasurement ?? 'Field Measurement'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          if (_fieldPoints.length >= 3)
            IconButton(
              icon: const Icon(Icons.check_circle),
              onPressed: _completeField,
            ),
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: _clearField,
          ),
        ],
      ),
      
      body: Column(
        children: [
          // Top Banner Ad
          const BannerAdWidget(
            margin: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            alwaysShow: true,
          ),
          
          // Instructions
          if (_fieldPoints.isEmpty)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              color: Colors.green.shade50,
              child: Column(
                children: [
                  Icon(Icons.crop_free, size: 32, color: Colors.green.shade700),
                  const SizedBox(height: 8),
                  Text(
                    _getInstructionText(),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green.shade700,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Add at least 3 points to create a field area',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.green.shade600,
                    ),
                  ),
                ],
              ),
            ),


          
          // Map
          Expanded(
            child: Stack(
              children: [
                GoogleMap(
                  onMapCreated: _onMapCreated,
                  onTap: _onMapTap,
                  initialCameraPosition: CameraPosition(
                    target: widget.initialLocation,
                    zoom: 16,
                  ),
                  mapType: _showSatelliteView ? MapType.satellite : MapType.normal,
                  markers: _markers,
                  polygons: _polygons,
                  myLocationEnabled: true,
                  myLocationButtonEnabled: false,
                  mapToolbarEnabled: false,
                ),
                


                // Floating method selector
                Positioned(
                  top: 16,
                  right: 16,
                  child: _buildFloatingMethodSelector(),
                ),

                // Satellite view toggle
                Positioned(
                  top: 16,
                  left: 16,
                  child: _buildSatelliteToggle(),
                ),

                // GPS recording status (floating)
                if (_isRecording)
                  Positioned(
                    bottom: 100,
                    left: 16,
                    right: 16,
                    child: _buildGPSRecordingStatus(),
                  ),
              ],
            ),
          ),
          
          // Bottom controls
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    _fieldPoints.isEmpty
                        ? 'Start marking field boundaries'
                        : '${_fieldPoints.length} points marked',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                if (_fieldPoints.length >= 3) ...[
                  ElevatedButton.icon(
                    onPressed: _completeField,
                    icon: const Icon(Icons.save),
                    label: const Text('Save Field'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFieldInfoCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.crop_free, color: Colors.green.shade700),
                const SizedBox(width: 8),
                const Text(
                  'Field Measurement',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text('Points: ${_fieldPoints.length}'),
            if (_fieldArea > 0) ...[
              const SizedBox(height: 12),
              _buildMeasurementDisplayCard(),
            ],

            // Show individual segment distances
            if (_fieldSegmentDistances.isNotEmpty) ...[
              const SizedBox(height: 8),
              const Text(
                'Point-to-Point Distances:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
              const SizedBox(height: 4),
              ...List.generate(_fieldSegmentDistances.length, (index) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 2),
                  child: Consumer<AppProvider>(
                    builder: (context, appProvider, child) {
                      final l10n = AppLocalizations.of(context);
                      return Text(
                        '${l10n?.points ?? 'Point'} ${index + 1} → ${l10n?.points ?? 'Point'} ${index + 2}: ${appProvider.formatDistance(_fieldSegmentDistances[index])}',
                        style: const TextStyle(fontSize: 12),
                      );
                    },
                  ),
                );
              }),
            ],
          ],
        ),
      ),
    );
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
  }

  void _onMapTap(LatLng latLng) {
    if (kDebugMode) {
      print('Map tapped at: ${latLng.latitude}, ${latLng.longitude}');
      print('Current points: ${_fieldPoints.length}');
    }

    if (_inputMethod == InputMethod.tap || _inputMethod == InputMethod.hybrid) {
      _addFieldPoint(latLng);
    }
  }

  void _addFieldPoint(LatLng latLng) {
    // Allow placing points close together - removal only happens when tapping directly on markers
    if (kDebugMode) {
      print('Adding field point at: ${latLng.latitude}, ${latLng.longitude}');
      print('Points before adding: ${_fieldPoints.length}');
    }

    // If not removing a point, add a new one
    final position = Position(
      latitude: latLng.latitude,
      longitude: latLng.longitude,
      timestamp: DateTime.now(),
      accuracy: 0.5, // Set to 0.5 meter accuracy for practical precision
      altitude: 0.0,
      heading: 0.0,
      speed: 0.0,
      speedAccuracy: 0.0,
      altitudeAccuracy: 0.0,
      headingAccuracy: 0.0,
    );

    setState(() {
      _fieldPoints.add(position);
    });

    if (kDebugMode) {
      print('Points after adding: ${_fieldPoints.length}');
    }

    _updateFieldVisualization();
    _calculateFieldMeasurements();
  }

  void _updateFieldVisualization() {
    // Update markers
    Set<Marker> markers = {};
    for (int i = 0; i < _fieldPoints.length; i++) {
      final point = _fieldPoints[i];

      // Calculate distance from previous point
      String distanceInfo = '';
      if (i > 0 && _fieldSegmentDistances.isNotEmpty && i - 1 < _fieldSegmentDistances.length) {
        double segmentDistance = _fieldSegmentDistances[i - 1];
        final appProvider = Provider.of<AppProvider>(context, listen: false);
        distanceInfo = '\nDistance from Point $i: ${appProvider.formatDistance(segmentDistance)}';
      }

      markers.add(
        Marker(
          markerId: MarkerId('field_point_$i'),
          position: LatLng(point.latitude, point.longitude),
          infoWindow: InfoWindow(
            title: 'Point ${i + 1}',
            snippet: 'Tap to remove this point$distanceInfo',
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(
            i == 0 ? BitmapDescriptor.hueGreen : BitmapDescriptor.hueBlue,
          ),
          onTap: () => _removeFieldPoint(i),
        ),
      );
    }

    // Distance markers will be added asynchronously by _addDistanceMarkersAsync()
    if (kDebugMode) {
      print('Field points: ${_fieldPoints.length}, Segment distances: ${_fieldSegmentDistances.length}');
      if (_fieldPoints.length >= 3) {
        print('Distance markers to be created: ${_fieldPoints.length} (including closing segment)');
      } else {
        print('Distance markers to be created: ${_fieldPoints.length - 1}');
      }
    }

    // Update polygon if we have at least 3 points
    Set<Polygon> polygons = {};
    if (_fieldPoints.length >= 3) {
      List<LatLng> polygonPoints = _fieldPoints
          .map((p) => LatLng(p.latitude, p.longitude))
          .toList();

      polygons.add(
        Polygon(
          polygonId: const PolygonId('field_area'),
          points: polygonPoints,
          fillColor: Colors.green.withOpacity(0.3),
          strokeColor: Colors.green,
          strokeWidth: 2,
          geodesic: true,
        ),
      );
    }

    setState(() {
      _markers = markers;
      _polygons = polygons;
    });

    // Add distance markers asynchronously after the main markers are set
    _addDistanceMarkersAsync();
  }

  Future<void> _addDistanceMarkersAsync() async {
    if (_fieldPoints.length < 2) return;

    final appProvider = Provider.of<AppProvider>(context, listen: false);
    List<Marker> distanceMarkers = [];

    // Add distance markers for consecutive segments
    for (int i = 0; i < _fieldPoints.length - 1; i++) {
      if (_fieldSegmentDistances.isNotEmpty && i < _fieldSegmentDistances.length) {
        final point1 = _fieldPoints[i];
        final point2 = _fieldPoints[i + 1];

        // Calculate midpoint between two consecutive points
        double midLat = (point1.latitude + point2.latitude) / 2;
        double midLng = (point1.longitude + point2.longitude) / 2;

        String distanceText = appProvider.formatDistance(_fieldSegmentDistances[i]);

        if (kDebugMode) {
          print('Creating distance marker $i: $distanceText at ($midLat, $midLng)');
        }

        try {
          BitmapDescriptor icon = await _getCachedDistanceMarker(distanceText);
          distanceMarkers.add(
            Marker(
              markerId: MarkerId('field_line_distance_$i'),
              position: LatLng(midLat, midLng),
              infoWindow: InfoWindow.noText,
              icon: icon,
              anchor: const Offset(0.5, 0.5),
              consumeTapEvents: false,
            ),
          );
        } catch (error) {
          if (kDebugMode) {
            print('Error creating distance marker $i: $error');
          }
        }
      }
    }

    // Add closing distance marker for complete fields (3+ points)
    if (_fieldPoints.length >= 3) {
      final firstPoint = _fieldPoints.first;
      final lastPoint = _fieldPoints.last;

      // Calculate midpoint between last and first point (closing segment)
      double closingMidLat = (lastPoint.latitude + firstPoint.latitude) / 2;
      double closingMidLng = (lastPoint.longitude + firstPoint.longitude) / 2;

      // Calculate closing distance
      double closingDistance = Geolocator.distanceBetween(
        lastPoint.latitude,
        lastPoint.longitude,
        firstPoint.latitude,
        firstPoint.longitude,
      );

      String closingDistanceText = appProvider.formatDistance(closingDistance);

      if (kDebugMode) {
        print('Creating closing distance marker: $closingDistanceText at ($closingMidLat, $closingMidLng)');
      }

      try {
        BitmapDescriptor icon = await _getCachedDistanceMarker(closingDistanceText);
        distanceMarkers.add(
          Marker(
            markerId: const MarkerId('field_closing_distance'),
            position: LatLng(closingMidLat, closingMidLng),
            infoWindow: InfoWindow.noText,
            icon: icon,
            anchor: const Offset(0.5, 0.5),
            consumeTapEvents: false,
          ),
        );
      } catch (error) {
        if (kDebugMode) {
          print('Error creating closing distance marker: $error');
        }
      }
    }

    // Add all distance markers to the map
    if (mounted && distanceMarkers.isNotEmpty) {
      setState(() {
        _markers.addAll(distanceMarkers);
      });

      if (kDebugMode) {
        print('Added ${distanceMarkers.length} distance markers to map');
        print('Total markers on map: ${_markers.length}');
      }
    }
  }

  void _calculateFieldMeasurements() {
    if (_fieldPoints.length < 2) {
      setState(() {
        _fieldArea = 0.0;
        _perimeter = 0.0;
        _fieldSegmentDistances.clear();
      });
      return;
    }

    try {
      // Calculate segment distances between consecutive points
      List<double> segmentDistances = [];
      for (int i = 0; i < _fieldPoints.length - 1; i++) {
        double segmentDistance = Geolocator.distanceBetween(
          _fieldPoints[i].latitude,
          _fieldPoints[i].longitude,
          _fieldPoints[i + 1].latitude,
          _fieldPoints[i + 1].longitude,
        );
        segmentDistances.add(segmentDistance);
      }

      // Calculate area and perimeter only if we have at least 3 points
      double area = 0.0;
      double perimeter = 0.0;

      if (_fieldPoints.length >= 3) {
        // Calculate area using shoelace formula
        area = _calculateGeodeticArea(_fieldPoints);

        // Calculate perimeter (including closing segment)
        perimeter = segmentDistances.fold(0.0, (sum, distance) => sum + distance);

        // Add closing segment distance (from last point back to first)
        double closingDistance = Geolocator.distanceBetween(
          _fieldPoints.last.latitude,
          _fieldPoints.last.longitude,
          _fieldPoints.first.latitude,
          _fieldPoints.first.longitude,
        );
        perimeter += closingDistance;
      }

      setState(() {
        _fieldArea = area;
        _perimeter = perimeter;
        _fieldSegmentDistances = segmentDistances;
      });
    } catch (e) {
      if (kDebugMode) print('Error calculating field measurements: $e');
    }
  }

  double _calculateGeodeticArea(List<Position> points) {
    if (points.length < 3) return 0.0;

    try {
      const double earthRadius = 6378137.0; // WGS84 Earth radius in meters
      double area = 0.0;

      List<double> lats = [];
      List<double> lngs = [];
      
      for (var point in points) {
        lats.add(point.latitude * pi / 180);
        lngs.add(point.longitude * pi / 180);
      }

      // Close the polygon
      lats.add(lats.first);
      lngs.add(lngs.first);

      for (int i = 0; i < lats.length - 1; i++) {
        area += (lngs[i + 1] - lngs[i]) * (2 + sin(lats[i]) + sin(lats[i + 1]));
      }

      area = area.abs() * earthRadius * earthRadius / 2;
      
      return area.isNaN || area.isInfinite ? 0.0 : area;
    } catch (e) {
      if (kDebugMode) print('Error in geodetic area calculation: $e');
      return 0.0;
    }
  }

  void _completeField() {
    if (_fieldPoints.length < 3) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please add at least 3 points to create a field'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    _showSaveFieldDialog();
  }

  void _showSaveFieldDialog() {
    final nameController = TextEditingController();
    final notesController = TextEditingController();

    // Generate default name
    final now = DateTime.now();
    nameController.text = 'Field ${now.day}/${now.month}/${now.year} ${now.hour}:${now.minute.toString().padLeft(2, '0')}';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Save Field Measurement'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Field summary
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Field Summary',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green.shade700,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildCompactMeasurementDisplay(),
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
              
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'Field Name',
                  border: OutlineInputBorder(),
                ),
                maxLength: 50,
              ),
              
              const SizedBox(height: 16),
              
              TextField(
                controller: notesController,
                decoration: const InputDecoration(
                  labelText: 'Notes (Optional)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
                maxLength: 200,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (nameController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please enter a field name'),
                    backgroundColor: Colors.orange,
                  ),
                );
                return;
              }
              
              await _saveField(nameController.text.trim(), notesController.text.trim());
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Close page
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
            child: const Text('Save Field'),
          ),
        ],
      ),
    );
  }

  Future<void> _saveField(String name, String notes) async {
    try {
      final storageService = MeasurementStorageService();
      await storageService.initialize();

      final fieldData = MeasurementRecord(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: name,
        type: 'field',
        points: _fieldPoints.map((point) => {
          'latitude': point.latitude,
          'longitude': point.longitude,
        }).toList(),
        area: _fieldArea,
        distance: 0.0,
        perimeter: _perimeter,
        units: 'metric',
        timestamp: DateTime.now(),
        metadata: {
          'app_version': '1.0',
          'measurement_type': 'field',
          'notes': notes,
          'point_count': _fieldPoints.length,
          'segment_distances': _fieldSegmentDistances,
        },
      );

      await storageService.saveMeasurement(fieldData);
      
      widget.onFieldSaved({
        'name': name,
        'area': _fieldArea,
        'perimeter': _perimeter,
        'points': _fieldPoints.length,
      });

      _showSaveSuccessDialog();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error saving field: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _clearField() {
    setState(() {
      _fieldPoints.clear();
      _markers.clear();
      _polygons.clear();
      _fieldArea = 0.0;
      _perimeter = 0.0;
      _fieldSegmentDistances.clear();
      _distanceMarkerCache.clear(); // Clear distance marker cache
    });
  }

  Widget _buildCompactMethodButton(InputMethod method, IconData icon, String label) {
    bool isSelected = _inputMethod == method;
    Color color = method == InputMethod.tap
        ? Colors.blue
        : method == InputMethod.walk
            ? Colors.green
            : Colors.orange;

    return GestureDetector(
      onTap: () {
        setState(() {
          _inputMethod = method;
          if (_isRecording && method == InputMethod.tap) {
            _stopRecording();
          }
        });
      },
      child: Container(
        width: 80,
        height: 32,
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
        decoration: BoxDecoration(
          color: isSelected ? color : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: isSelected ? color : Colors.grey.shade300,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.white : Colors.grey.shade600,
              size: 16,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.grey.shade600,
                fontSize: 11,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getInstructionText() {
    switch (_inputMethod) {
      case InputMethod.tap:
        return 'Tap on the map to mark field boundaries';
      case InputMethod.walk:
        return 'Walk around the field boundary to record GPS points';
      case InputMethod.hybrid:
        return 'Tap on map or walk around to mark boundaries';
    }
  }

  Future<void> _startRecording() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Location permission is required for GPS recording'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }
      }

      setState(() {
        _isRecording = true;
      });

      const LocationSettings locationSettings = LocationSettings(
        accuracy: LocationAccuracy.best,
        distanceFilter: 0, // Record every position change for maximum precision
        timeLimit: Duration(seconds: 30), // Optimal time for 0.5m precision
      );

      _positionStream = Geolocator.getPositionStream(locationSettings: locationSettings).listen(
        (Position position) {
          if (_isRecording) {
            _addFieldPointFromGPS(position);
          }
        },
        onError: (error) {
          if (kDebugMode) print('GPS recording error: $error');
          _stopRecording();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('GPS recording error: $error'),
              backgroundColor: Colors.red,
            ),
          );
        },
      );

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('GPS recording started'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error starting GPS recording: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _stopRecording() {
    setState(() {
      _isRecording = false;
    });
    _positionStream?.cancel();
    _positionStream = null;

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('GPS recording stopped'),
        backgroundColor: Colors.orange,
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _addFieldPointFromGPS(Position position) {
    setState(() {
      _fieldPoints.add(position);
    });

    _updateFieldVisualization();
    _calculateFieldMeasurements();
  }

  void _removeFieldPoint(int index) {
    setState(() {
      _fieldPoints.removeAt(index);
      _updateFieldVisualization();
      _calculateFieldMeasurements();
    });
  }

  Widget _buildFloatingMethodSelector() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildCompactMethodButton(InputMethod.tap, Icons.touch_app, 'Tap'),
          const SizedBox(height: 4),
          _buildCompactMethodButton(InputMethod.walk, Icons.directions_walk, 'Walk'),
          const SizedBox(height: 4),
          _buildCompactMethodButton(InputMethod.hybrid, Icons.merge_type, 'Hybrid'),

          // GPS control for walk/hybrid modes
          if (_inputMethod == InputMethod.walk || _inputMethod == InputMethod.hybrid) ...[
            const SizedBox(height: 8),
            Container(
              width: 80,
              height: 1,
              color: Colors.grey.shade300,
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: 80,
              height: 32,
              child: ElevatedButton(
                onPressed: _isRecording ? _stopRecording : _startRecording,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _isRecording ? Colors.red : Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                  minimumSize: const Size(80, 32),
                  maximumSize: const Size(80, 32),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      _isRecording ? Icons.stop : Icons.play_arrow,
                      size: 14,
                    ),
                    const SizedBox(width: 2),
                    Text(
                      _isRecording ? 'Stop' : 'GPS',
                      style: const TextStyle(fontSize: 10),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildGPSRecordingStatus() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.green.shade700,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: const BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          const Text(
            'Recording GPS points...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '${_fieldPoints.length} points',
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 11,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSatelliteToggle() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () {
            setState(() {
              _showSatelliteView = !_showSatelliteView;
            });
          },
          child: Container(
            padding: const EdgeInsets.all(12),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _showSatelliteView ? Icons.map : Icons.satellite_alt,
                  color: _showSatelliteView ? Colors.green : Colors.blue,
                  size: 24,
                ),
                const SizedBox(height: 4),
                Text(
                  _showSatelliteView ? 'Normal' : 'Satellite',
                  style: TextStyle(
                    color: _showSatelliteView ? Colors.green : Colors.blue,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Comprehensive measurement display card with unit formatting
  Widget _buildMeasurementDisplayCard() {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        final l10n = AppLocalizations.of(context);

        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 6,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Icon(
                    Icons.crop_free,
                    color: Colors.green.shade600,
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    l10n?.fieldMeasurement ?? 'Field Measurement',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.green.shade700,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Area Display
              _buildMeasurementRow(
                icon: Icons.crop_free,
                label: l10n?.area ?? 'Area',
                value: appProvider.formatArea(_fieldArea),
                color: Colors.blue,
              ),

              const SizedBox(height: 8),

              // Perimeter Display
              _buildMeasurementRow(
                icon: Icons.straighten,
                label: l10n?.perimeter ?? 'Perimeter',
                value: appProvider.formatPerimeter(_perimeter),
                color: Colors.orange,
              ),

              const SizedBox(height: 8),

              // Points Count
              _buildMeasurementRow(
                icon: Icons.place,
                label: l10n?.points ?? 'Points',
                value: '${_fieldPoints.length}',
                color: Colors.purple,
              ),

              // Unit Information
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      l10n?.unitSettings ?? 'Unit Settings',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey.shade700,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: _buildUnitInfo(
                            label: l10n?.areaUnit ?? 'Area Unit',
                            unit: appProvider.userSettings.areaUnit,
                            icon: Icons.crop_free,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildUnitInfo(
                            label: l10n?.perimeterUnit ?? 'Perimeter Unit',
                            unit: appProvider.userSettings.perimeterUnit,
                            icon: Icons.straighten,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMeasurementRow({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(
            icon,
            color: color,
            size: 16,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildUnitInfo({
    required String label,
    required String unit,
    required IconData icon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: 16,
              color: Colors.grey.shade600,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.blue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: Colors.blue.withOpacity(0.3)),
          ),
          child: Text(
            unit,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Colors.blue.shade700,
            ),
          ),
        ),
      ],
    );
  }

  // Compact measurement display for bottom panel
  Widget _buildCompactMeasurementDisplay() {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        final l10n = AppLocalizations.of(context);

        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.green.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.green.shade200),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with icon
              Row(
                children: [
                  Icon(
                    Icons.analytics,
                    color: Colors.green.shade600,
                    size: 18,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    l10n?.measurements ?? 'Measurements',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.green.shade700,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Measurements in compact format
              Row(
                children: [
                  Expanded(
                    child: _buildCompactMeasurementItem(
                      icon: Icons.place,
                      label: l10n?.points ?? 'Points',
                      value: '${_fieldPoints.length}',
                      color: Colors.purple,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildCompactMeasurementItem(
                      icon: Icons.crop_free,
                      label: l10n?.area ?? 'Area',
                      value: appProvider.formatArea(_fieldArea),
                      color: Colors.blue,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              _buildCompactMeasurementItem(
                icon: Icons.straighten,
                label: l10n?.perimeter ?? 'Perimeter',
                value: appProvider.formatPerimeter(_perimeter),
                color: Colors.orange,
                fullWidth: true,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCompactMeasurementItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
    bool fullWidth = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: color,
            size: 16,
          ),
          const SizedBox(width: 6),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 9,
                    color: Colors.grey.shade600,
                  ),
                ),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Removed old formatting methods - now using AppProvider.formatArea() and AppProvider.formatDistance()

  // Get cached distance marker to avoid recreating identical markers
  Future<BitmapDescriptor> _getCachedDistanceMarker(String distance) async {
    String cacheKey = distance;

    if (_distanceMarkerCache.containsKey(cacheKey)) {
      return _distanceMarkerCache[cacheKey]!;
    }

    BitmapDescriptor marker = await _createDistanceTextMarker(distance);
    _distanceMarkerCache[cacheKey] = marker;
    return marker;
  }

  // Create custom distance text marker that's always visible
  Future<BitmapDescriptor> _createDistanceTextMarker(String distance) async {
    try {
      final ui.PictureRecorder pictureRecorder = ui.PictureRecorder();
      final Canvas canvas = Canvas(pictureRecorder);

      // Optimized marker dimensions for larger font
      const double width = 200.0;
      const double height = 70.0;
      const double radius = 12.0;

      // Enhanced colors for better visibility
      Color backgroundColor = Colors.green.shade600;
      Color borderColor = Colors.green.shade800;
      Color textColor = Colors.white;

      // Draw outer border
      final RRect outerRect = RRect.fromRectAndRadius(
        const Rect.fromLTWH(0, 0, width, height),
        const Radius.circular(radius),
      );

      final Paint borderPaint = Paint()
        ..color = borderColor
        ..style = PaintingStyle.fill;
      canvas.drawRRect(outerRect, borderPaint);

      // Draw inner background with 2px padding
      final RRect innerRect = RRect.fromRectAndRadius(
        const Rect.fromLTWH(2, 2, width - 4, height - 4),
        const Radius.circular(radius - 2),
      );

      final Paint backgroundPaint = Paint()
        ..color = backgroundColor
        ..style = PaintingStyle.fill;
      canvas.drawRRect(innerRect, backgroundPaint);

      // Draw distance text with enhanced visibility
      final textPainter = TextPainter(
        text: TextSpan(
          text: distance,
          style: TextStyle(
            color: textColor,
            fontSize: 32, // Large font for better visibility
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                color: Colors.black.withOpacity(0.8),
                offset: const Offset(1, 1),
                blurRadius: 3,
              ),
              Shadow(
                color: Colors.black.withOpacity(0.4),
                offset: const Offset(0.5, 0.5),
                blurRadius: 1,
              ),
            ],
          ),
        ),
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();
      final double textX = (width - textPainter.width) / 2;
      final double textY = (height - textPainter.height) / 2;
      textPainter.paint(canvas, Offset(textX, textY));

      // Convert to image
      final ui.Picture picture = pictureRecorder.endRecording();
      final ui.Image image = await picture.toImage(width.toInt(), height.toInt());
      final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final Uint8List uint8List = byteData!.buffer.asUint8List();

      return BitmapDescriptor.fromBytes(uint8List);
    } catch (e) {
      if (kDebugMode) print('Error creating distance text marker: $e');
      // Fallback to default marker
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen);
    }
  }



  void _showSaveSuccessDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.green.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.check_circle,
                color: Colors.green.shade700,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'Field Saved Successfully!',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Your field measurement has been saved to your device.',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.storage, color: Colors.blue.shade700, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Saved Location',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '📱 Local Device Storage',
                    style: TextStyle(color: Colors.blue.shade600),
                  ),
                  Text(
                    '💾 Accessible from "Saved Data" menu',
                    style: TextStyle(color: Colors.blue.shade600),
                  ),
                  Text(
                    '📤 Export as PDF, JSON, or CSV',
                    style: TextStyle(color: Colors.blue.shade600),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Close page
            },
            child: const Text('Continue'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Close page
              // Navigate to saved measurements
            },
            icon: const Icon(Icons.folder_rounded),
            label: const Text('View Saved Data'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}
