import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:permission_handler/permission_handler.dart';
import 'package:geolocator/geolocator.dart';
import 'package:provider/provider.dart';
import '../../main.dart';
import '../providers/app_state_provider.dart';
import '../widgets/banner_ad_widget.dart';
import '../widgets/advanced_zoom_control.dart';
import '../widgets/camera_grid_overlay.dart';
import '../widgets/tutorial_highlight_widget.dart';
import '../../domain/entities/location_entity.dart';
import '../../domain/entities/photo_entity.dart';
import '../../generated/l10n/app_localizations.dart';
import '../../domain/models/camera_settings_model.dart';
import '../../data/services/geocoding_service.dart';
import '../../data/services/media_storage_service.dart';
import '../../data/services/tutorial_service.dart';

class CameraPage extends StatefulWidget {
  final VoidCallback? onNavigateToGallery;

  const CameraPage({Key? key, this.onNavigateToGallery}) : super(key: key);

  @override
  State<CameraPage> createState() => _CameraPageState();
}

class _CameraPageState extends State<CameraPage> with WidgetsBindingObserver {
  CameraController? _controller;
  bool _isInitialized = false;
  bool _isTakingPicture = false;
  int _selectedCameraIndex = 0;
  Position? _currentPosition;
  bool _isGettingLocation = false;
  String? _currentAddress;
  final GeocodingService _geocodingService = GeocodingService();
  bool _permissionDenied = false;
  double _minAvailableZoom = 1.0;
  double _maxAvailableZoom = 1.0;
  double _currentZoomLevel = 1.0;
  FlashMode _currentFlashMode = FlashMode.auto;
  bool _isRecoveringCamera = false;
  // Timer for camera recovery
  Timer? _cameraRecoveryTimer;

  // Tutorial state
  final TutorialService _tutorialService = TutorialService();
  bool _showCameraTutorial = false;

  // Camera zoom settings
  double _baseZoomLevel = 1.0;
  bool _showZoomIndicator = false;
  Timer? _zoomIndicatorTimer;
  List<double> _deviceZoomLevels = [1.0]; // Dynamic zoom levels based on device

  // Advanced camera features
  bool _showGrid = false;
  GridType _gridType = GridType.rule_of_thirds;
  bool _isTimerEnabled = false; // Timer is enabled/selected
  bool _isTimerCountingDown = false; // Timer is actively counting down
  int _timerSeconds = 0; // Current countdown seconds
  int _selectedTimerDuration = 3; // 3, 5, or 10 seconds
  Timer? _timerCountdown;
  Offset? _focusPoint;
  bool _showFocusIndicator = false;
  String _customText = '';
  bool _showTextOverlay = false;

  // Capture animation
  bool _showCaptureAnimation = false;
  bool _captureButtonPressed = false;

  // Camera options panel
  bool _showCameraOptions = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _checkPermissionsAndInitCamera();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _cameraRecoveryTimer?.cancel();
    _zoomIndicatorTimer?.cancel();
    _timerCountdown?.cancel();
    _disposeCamera();
    super.dispose();
  }

  // Safely dispose camera controller
  Future<void> _disposeCamera() async {
    try {
      final CameraController? controller = _controller;
      if (controller != null) {
        await controller.dispose();
        _controller = null;
      }
    } catch (e) {
      // Silently handle disposal errors
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.inactive) {
      _disposeCamera();
    } else if (state == AppLifecycleState.resumed) {
      // Add a delay before reinitializing camera
      if (!_isRecoveringCamera) {
        Future.delayed(const Duration(milliseconds: 1000), () {
          _initCameraController(_selectedCameraIndex);
        });
      }
    }
  }

  Future<void> _checkPermissionsAndInitCamera() async {
    final cameraStatus = await Permission.camera.request();
    final locationStatus = await Permission.location.request();

    if (cameraStatus.isGranted) {
      _initCameraController(_selectedCameraIndex);
    } else {
      setState(() {
        _permissionDenied = true;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Camera permission is required to use this feature'),
            duration: Duration(seconds: 4),
          ),
        );
      }
    }

    if (locationStatus.isGranted) {
      _getCurrentLocation();
    }
  }

  Future<void> _initCameraController(int cameraIndex) async {
    if (cameras.isEmpty) {
      setState(() {
        _isInitialized = false;
      });
      return;
    }

    // Set flag to prevent multiple initializations
    if (_isRecoveringCamera) return;
    _isRecoveringCamera = true;

    // Reset state
    setState(() {
      _isInitialized = false;
    });

    final cameraId = cameraIndex >= cameras.length ? 0 : cameraIndex;
    
    // Dispose previous controller properly
    await _disposeCamera();
    
    // Add a small delay to ensure previous camera is fully released
    await Future.delayed(const Duration(milliseconds: 500));
    
    try {
      final newController = CameraController(
        cameras[cameraId],
        ResolutionPreset.high,
        imageFormatGroup: ImageFormatGroup.jpeg,
      );

      _controller = newController;

      // Initialize the controller
      await newController.initialize();

      if (!mounted) {
        await newController.dispose();
        _isRecoveringCamera = false;
        return;
      }

      // Configure camera settings for photo capture
      await newController.setFlashMode(FlashMode.auto);
      
      // Get available zoom range
      _minAvailableZoom = await newController.getMinZoomLevel();
      _maxAvailableZoom = await newController.getMaxZoomLevel();
      _currentZoomLevel = 1.0;

      // Generate dynamic zoom levels based on device capabilities
      _generateDeviceZoomLevels();
      
      if (!mounted) {
        await newController.dispose();
        _isRecoveringCamera = false;
        return;
      }
      
      setState(() {
        _isInitialized = true;
        _selectedCameraIndex = cameraId;
        _isRecoveringCamera = false;
      });

      // Check if we should show camera tutorial
      _checkAndShowTutorial();
    } catch (e) {
      // Try with a different camera if available
      if (cameras.length > 1 && mounted) {
        final newIndex = (cameraId + 1) % cameras.length;
        _isRecoveringCamera = false;
        Future.delayed(const Duration(milliseconds: 1000), () {
          _initCameraController(newIndex);
        });
      } else {
        _isRecoveringCamera = false;
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error initializing camera. Please restart the app.')),
        );
      }
    }
  }

  Future<void> _checkAndShowTutorial() async {
    final shouldShow = await _tutorialService.shouldShowCameraTutorial();
    if (shouldShow && mounted) {
      // Show tutorial after a short delay to ensure UI is ready
      Future.delayed(const Duration(milliseconds: 1000), () {
        if (mounted) {
          setState(() {
            _showCameraTutorial = true;
          });
          _tutorialService.showCameraTutorial(context);
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_permissionDenied) {
      return _buildPermissionDeniedUI();
    }
    
    if (!_isInitialized || _controller == null) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }
    
    return Scaffold(
      body: Stack(
        children: [
          // Full screen camera preview
          SizedBox.expand(
            child: FittedBox(
              fit: BoxFit.cover,
              child: SizedBox(
                width: _controller!.value.previewSize?.height ?? 1,
                height: _controller!.value.previewSize?.width ?? 1,
                child: Builder(
                  builder: (context) {
                    try {
                      if (_controller != null && _controller!.value.isInitialized) {
                        return CameraPreview(_controller!);
                      } else {
                        return Container(color: Colors.black);
                      }
                    } catch (e) {
                      return Container(
                        color: Colors.black,
                        child: Center(
                          child: Icon(
                            Icons.error_outline,
                            color: Colors.white,
                            size: 30,
                          ),
                        ),
                      );
                    }
                  },
                ),
              ),
            ),
          ),
          
          // Gesture detector for zoom and focus
          SizedBox.expand(
            child: GestureDetector(
              onScaleStart: (details) {
                setState(() {
                  _baseZoomLevel = _currentZoomLevel;
                });
              },
              onScaleUpdate: (details) {
                if (_controller == null) return;

                // Change zoom level
                double newZoomLevel = _baseZoomLevel * details.scale;
                newZoomLevel = newZoomLevel.clamp(_minAvailableZoom, _maxAvailableZoom);

                if (newZoomLevel != _currentZoomLevel) {
                  setState(() {
                    _currentZoomLevel = newZoomLevel;
                    _showZoomIndicator = true;
                  });
                  _controller!.setZoomLevel(newZoomLevel);

                  // Hide zoom indicator after 2 seconds
                  _zoomIndicatorTimer?.cancel();
                  _zoomIndicatorTimer = Timer(const Duration(seconds: 2), () {
                    if (mounted) {
                      setState(() {
                        _showZoomIndicator = false;
                      });
                    }
                  });
                }
              },
              onTapDown: (details) => _onViewFinderTap(details, context),
            ),
          ),
          
          // Safe area for controls
          SafeArea(
            child: Column(
              children: [
                // Banner Ad at top (always visible)
                const BannerAdWidget(
                  margin: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                  alwaysShow: true,
                ),

                // Top controls row - Camera options toggle
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Camera options toggle button
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            _showCameraOptions = !_showCameraOptions;
                          });
                        },
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: _showCameraOptions ? Colors.white.withOpacity(0.3) : Colors.black38,
                            borderRadius: BorderRadius.circular(25),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.tune,
                                color: _showCameraOptions ? Colors.orange : Colors.white,
                                size: 24,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Options',
                                style: TextStyle(
                                  color: _showCameraOptions ? Colors.orange : Colors.white,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const SizedBox(width: 4),
                              Icon(
                                _showCameraOptions ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                                color: _showCameraOptions ? Colors.orange : Colors.white,
                                size: 20,
                              ),
                            ],
                          ),
                        ),
                      ),
                      
                      // Flash mode button with tutorial
                      TutorialHighlightWidget(
                        showTutorial: _showCameraTutorial,
                        tutorialMessage: "Tap for flash",
                        highlightColor: Colors.orange,
                        position: TutorialPosition.bottom,
                        onTutorialTap: () {
                          setState(() {
                            _showCameraTutorial = false;
                          });
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.black38,
                            borderRadius: BorderRadius.circular(20),
                          ),
                            child: IconButton(
                              icon: Icon(
                                _currentFlashMode == FlashMode.off
                                  ? Icons.flash_off
                                  : _currentFlashMode == FlashMode.auto
                                    ? Icons.flash_auto
                                    : Icons.flash_on,
                                color: Colors.white,
                              ),
                              onPressed: _onFlashModeButtonPressed,
                            ),
                          ),
                      ),
                    ],
                  ),
                ),
                
                // Spacer to push controls to bottom
                const Spacer(),
                
                // Location info overlay with tutorial
                if (_currentPosition != null)
                  TutorialHighlightWidget(
                    showTutorial: _showCameraTutorial,
                    tutorialMessage: "GPS status",
                    highlightColor: Colors.red,
                    position: TutorialPosition.bottom,
                    onTutorialTap: () {
                      setState(() {
                        _showCameraTutorial = false;
                      });
                    },
                    child: Container(
                      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black38,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Lat: ${_currentPosition!.latitude.toStringAsFixed(6)}, '
                            'Lng: ${_currentPosition!.longitude.toStringAsFixed(6)}',
                            style: const TextStyle(color: Colors.white, fontSize: 14),
                          ),
                          if (_currentAddress != null)
                            Text(
                              _currentAddress!,
                              style: const TextStyle(color: Colors.white, fontSize: 14),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                        ],
                      ),
                    ),
                  ),



                // Zoom control at bottom
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: AdvancedZoomControl(
                    currentZoom: _currentZoomLevel,
                    minZoom: _minAvailableZoom,
                    maxZoom: _maxAvailableZoom,
                    onZoomChanged: (zoom) {
                      setState(() {
                        _currentZoomLevel = zoom;
                      });
                      _controller?.setZoomLevel(zoom);
                    },
                    presetZooms: _deviceZoomLevels,
                    isVisible: true,
                  ),
                ),

                // Bottom controls row
                Padding(
                  padding: const EdgeInsets.only(bottom: 30, left: 16, right: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      // Camera switch button with tutorial
                      if (cameras.length > 1)
                        TutorialHighlightWidget(
                          showTutorial: _showCameraTutorial,
                          tutorialMessage: "Tap to flip",
                          highlightColor: Colors.green,
                          position: TutorialPosition.top,
                          onTutorialTap: () {
                            setState(() {
                              _showCameraTutorial = false;
                            });
                          },
                          child: Container(
                            decoration: const BoxDecoration(
                              color: Colors.black38,
                              shape: BoxShape.circle,
                            ),
                            child: IconButton(
                              icon: const Icon(Icons.flip_camera_ios, color: Colors.white, size: 30),
                              onPressed: _switchCamera,
                            ),
                          ),
                        ),

                      // Capture button with tutorial
                      TutorialHighlightWidget(
                        showTutorial: _showCameraTutorial,
                        tutorialMessage: "Tap to capture",
                        highlightColor: Colors.blue,
                        position: TutorialPosition.top,
                        onTutorialTap: () {
                          setState(() {
                            _showCameraTutorial = false;
                          });
                        },
                        child: GestureDetector(
                          onTapDown: (_) {
                            setState(() {
                              _captureButtonPressed = true;
                            });
                          },
                          onTapUp: (_) {
                            setState(() {
                              _captureButtonPressed = false;
                            });
                          },
                          onTapCancel: () {
                            setState(() {
                              _captureButtonPressed = false;
                            });
                          },
                          onTap: _isTimerCountingDown ? null : _startCapture,
                          child: AnimatedScale(
                            scale: _captureButtonPressed ? 0.9 : 1.0,
                            duration: const Duration(milliseconds: 100),
                            child: Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                color: Colors.black38,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: _isTimerEnabled ? Colors.orange : Colors.white,
                                  width: 3,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.3),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Center(
                                child: AnimatedContainer(
                                  duration: const Duration(milliseconds: 150),
                                  width: 65,
                                  height: 65,
                                  decoration: BoxDecoration(
                                    color: _isTimerEnabled ? Colors.orange : Colors.white,
                                    shape: BoxShape.circle,
                                  ),
                                  child: _isTimerEnabled && !_isTimerCountingDown
                                    ? const Center(
                                        child: Icon(
                                          Icons.timer,
                                          color: Colors.white,
                                          size: 30,
                                        ),
                                      )
                                    : null,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),

                      // Gallery button (no tutorial)
                      Container(
                        decoration: const BoxDecoration(
                          color: Colors.black38,
                          shape: BoxShape.circle,
                        ),
                        child: IconButton(
                          icon: const Icon(Icons.photo_library, color: Colors.white, size: 30),
                          onPressed: widget.onNavigateToGallery ?? () {
                            // Fallback: Navigate to gallery page directly
                            Navigator.pushNamed(context, '/gallery');
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Location loading indicator
          if (_isGettingLocation)
            const Positioned(
              top: 60,
              right: 20,
              child: CircularProgressIndicator(
                color: Colors.white,
                strokeWidth: 2,
              ),
            ),
          
          // Zoom indicator (center of screen during pinch-to-zoom)
          if (_showZoomIndicator)
            Center(
              child: ZoomIndicator(
                currentZoom: _currentZoomLevel,
                isVisible: _showZoomIndicator,
              ),
            ),

          // Grid overlay
          if (_showGrid)
            Positioned.fill(
              child: IgnorePointer(
                child: CameraGridOverlay(
                  gridType: _gridType,
                  isVisible: _showGrid,
                  strokeWidth: 1.0,
                ),
              ),
            ),

          // Focus indicator
          if (_showFocusIndicator && _focusPoint != null)
            Positioned(
              left: _focusPoint!.dx - 30,
              top: _focusPoint!.dy - 30,
              child: Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.white, width: 2),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.center_focus_strong,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),

          // Timer countdown overlay (center of screen)
          if (_isTimerCountingDown)
            Positioned.fill(
              child: Container(
                color: Colors.black.withOpacity(0.7),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Large countdown number
                      Container(
                        width: 150,
                        height: 150,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.white,
                            width: 4,
                          ),
                          color: Colors.orange.withOpacity(0.2),
                        ),
                        child: Center(
                          child: Text(
                            '$_timerSeconds',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 80,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),
                      // Timer message
                      Text(
                        'Get Ready!',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 10),
                      // Cancel button
                      ElevatedButton(
                        onPressed: _cancelTimer,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                        ),
                        child: const Text('Cancel'),
                      ),
                    ],
                  ),
                ),
              ),
            ),

          // Custom text overlay
          if (_showTextOverlay && _customText.isNotEmpty)
            Positioned(
              bottom: 100,
              left: 20,
              right: 20,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _customText,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),

          // Camera options panel (positioned at top-right)
          if (_showCameraOptions)
            Positioned(
              top: 100,
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                width: 180,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.8),
                    width: 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 6,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // Grid toggle
                    _buildOptionItem(
                      icon: Icons.grid_on,
                      label: 'Grid',
                      isActive: _showGrid,
                      onTap: _toggleGrid,
                    ),
                    // Timer toggle
                    _buildTimerOptionItem(),
                  ],
                ),
              ),
            ),

          // Capture animation overlay
          if (_showCaptureAnimation)
            Positioned.fill(
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 150),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.8),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(
                  child: Icon(
                    Icons.camera_alt,
                    size: 80,
                    color: Colors.black54,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }







  // Build feature toggle button
  Widget _buildFeatureButton({
    required IconData icon,
    required bool isActive,
    required VoidCallback onPressed,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isActive ? Colors.white.withOpacity(0.3) : Colors.black38,
        borderRadius: BorderRadius.circular(20),
      ),
      child: IconButton(
        icon: Icon(
          icon,
          color: isActive ? Colors.orange : Colors.white,
          size: 24,
        ),
        onPressed: onPressed,
      ),
    );
  }

  // Build timer button with duration display
  Widget _buildTimerButton() {
    return Container(
      decoration: BoxDecoration(
        color: _isTimerEnabled ? Colors.white.withOpacity(0.3) : Colors.black38,
        borderRadius: BorderRadius.circular(20),
      ),
      child: InkWell(
        onTap: _isTimerCountingDown ? null : _toggleTimer, // Disable during countdown
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.timer,
                color: _isTimerEnabled ? Colors.orange : Colors.white,
                size: 20,
              ),
              if (_isTimerEnabled) ...[
                const SizedBox(width: 4),
                Text(
                  '${_selectedTimerDuration}s',
                  style: const TextStyle(
                    color: Colors.orange,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  // Build option item for collapsible panel
  Widget _buildOptionItem({
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isActive ? Colors.orange.withOpacity(0.8) : Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isActive ? Colors.orange : Colors.white.withOpacity(0.8),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Icon(
              icon,
              color: isActive ? Colors.white : Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              color: isActive ? Colors.orange : Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.w600,
              shadows: [
                Shadow(
                  color: Colors.black.withOpacity(0.8),
                  blurRadius: 2,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Build timer option item with duration display
  Widget _buildTimerOptionItem() {
    return GestureDetector(
      onTap: _isTimerCountingDown ? null : _toggleTimer,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _isTimerEnabled ? Colors.orange.withOpacity(0.8) : Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _isTimerEnabled ? Colors.orange : Colors.white.withOpacity(0.8),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.timer,
                  color: Colors.white,
                  size: 16,
                ),
                if (_isTimerEnabled) ...[
                  const SizedBox(width: 2),
                  Text(
                    '${_selectedTimerDuration}s',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 8,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ],
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Timer',
            style: TextStyle(
              color: _isTimerEnabled ? Colors.orange : Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.w600,
              shadows: [
                Shadow(
                  color: Colors.black.withOpacity(0.8),
                  blurRadius: 2,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Toggle grid functionality
  void _toggleGrid() {
    setState(() {
      _showGrid = !_showGrid;
    });
  }

  // Toggle timer functionality - cycles through 3s, 5s, 10s, off
  void _toggleTimer() {
    setState(() {
      if (!_isTimerEnabled) {
        // Turn on timer with current duration
        _isTimerEnabled = true;
      } else {
        // Cycle through timer durations: 3s -> 5s -> 10s -> off
        switch (_selectedTimerDuration) {
          case 3:
            _selectedTimerDuration = 5;
            break;
          case 5:
            _selectedTimerDuration = 10;
            break;
          case 10:
          default:
            _isTimerEnabled = false;
            _selectedTimerDuration = 3; // Reset to 3s for next time
            _cancelTimer(); // Cancel any active countdown
            break;
        }
      }
    });
  }

  // Start capture (with timer if enabled)
  void _startCapture() {
    // If timer is enabled and not currently counting down, start countdown
    if (_isTimerEnabled && !_isTimerCountingDown) {
      _startTimerCountdown();
    }
    // If no timer enabled, take photo immediately
    else if (!_isTimerEnabled) {
      _takePicture();
    }
    // If timer is counting down, ignore additional presses (handled by cancel button)
  }

  // Start timer countdown
  void _startTimerCountdown() {
    // Cancel any existing timer
    _timerCountdown?.cancel();

    // Start countdown
    setState(() {
      _isTimerCountingDown = true;
      _timerSeconds = _selectedTimerDuration;
    });

    _timerCountdown = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      setState(() {
        _timerSeconds--;
      });

      if (_timerSeconds <= 0) {
        timer.cancel();
        setState(() {
          _isTimerCountingDown = false;
          _timerSeconds = 0;
        });

        // Take photo after countdown completes
        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) {
            _takePicture();
          }
        });
      }
    });
  }

  // Cancel timer countdown
  void _cancelTimer() {
    _timerCountdown?.cancel();
    setState(() {
      _isTimerCountingDown = false;
      _timerSeconds = 0;
    });
  }

  void _onViewFinderTap(TapDownDetails details, BuildContext context) {
    if (_controller == null) return;

    final size = MediaQuery.of(context).size;
    final x = details.localPosition.dx / size.width;
    final y = details.localPosition.dy / size.height;

    // Set focus and metering point
    _controller!.setExposurePoint(Offset(x, y));
    _controller!.setFocusPoint(Offset(x, y));

    // Show focus indicator
    setState(() {
      _focusPoint = Offset(details.localPosition.dx, details.localPosition.dy);
      _showFocusIndicator = true;
    });

    // Hide focus indicator after 2 seconds
    Timer(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _showFocusIndicator = false;
        });
      }
    });
  }

  void _onFlashModeButtonPressed() {
    if (_controller == null) return;

    setState(() {
      _currentFlashMode = _currentFlashMode == FlashMode.off
          ? FlashMode.auto
          : _currentFlashMode == FlashMode.auto
              ? FlashMode.always
              : FlashMode.off;
    });

    _controller!.setFlashMode(_currentFlashMode);
  }

  void _generateDeviceZoomLevels() {
    List<double> zoomLevels = [];

    // Add ultra-wide zoom if available (less than 1.0x)
    if (_minAvailableZoom < 1.0) {
      // Add 0.5x if device supports it
      if (_minAvailableZoom <= 0.5) {
        zoomLevels.add(0.5);
      }
      // Add 0.6x if device supports it but not 0.5x
      else if (_minAvailableZoom <= 0.6) {
        zoomLevels.add(0.6);
      }
      // Add the actual minimum zoom level if it's reasonable
      else if (_minAvailableZoom < 1.0) {
        zoomLevels.add(_minAvailableZoom);
      }
    }

    // Always include 1.0x (normal zoom)
    zoomLevels.add(1.0);

    // Only add 2.0x if device supports it - no higher zoom buttons
    if (_maxAvailableZoom >= 2.0) {
      zoomLevels.add(2.0);
    }

    // Sort the zoom levels
    zoomLevels.sort();

    // Ensure we don't exceed device limits
    zoomLevels = zoomLevels.where((zoom) =>
      zoom >= _minAvailableZoom && zoom <= _maxAvailableZoom
    ).toList();

    _deviceZoomLevels = zoomLevels;
  }

  Widget _buildPermissionDeniedUI() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.no_photography,
            size: 80,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          const Text(
            'Camera Permission Required',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              'This app needs camera access to take photos. Please grant camera permission in your device settings.',
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () async {
              final status = await Permission.camera.request();
              if (status.isGranted) {
                setState(() {
                  _permissionDenied = false;
                });
                _initCameraController(_selectedCameraIndex);
              }
            },
            child: const Text('Grant Permission'),
          ),
          TextButton(
            onPressed: () {
              openAppSettings();
            },
            child: const Text('Open Settings'),
          ),
        ],
      ),
    );
  }

  Future<void> _getCurrentLocation() async {
    if (!mounted) return;
    
    setState(() {
      _isGettingLocation = true;
    });
    
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw Exception('Location services are disabled.');
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('Location permissions are denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('Location permissions are permanently denied.');
      }

      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high
      );
      
      if (!mounted) return;
      
      setState(() {
        _currentPosition = position;
        _isGettingLocation = false;
      });
      
      if (mounted) {
        final provider = Provider.of<AppProvider>(context, listen: false);
        provider.setCurrentLocation(
          LocationEntity(
            latitude: position.latitude,
            longitude: position.longitude,
            timestamp: DateTime.now(),
          ),
        );
        
        _getAddressFromPosition(position);
      }
    } catch (e) {
      print('Error getting location: $e');
      if (!mounted) return;
      
      setState(() {
        _isGettingLocation = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error getting location: $e')),
        );
      }
    }
  }
  
  Future<void> _getAddressFromPosition(Position position) async {
    try {
      final address = await _geocodingService.getAddressFromLatLng(
        position.latitude,
        position.longitude
      );
      
      if (!mounted) return;
      
      setState(() {
        _currentAddress = address;
      });
      
      if (mounted) {
        final provider = Provider.of<AppProvider>(context, listen: false);
        provider.setCurrentAddress(address);
      }
    } catch (e) {
      // Silently handle address lookup errors
    }
  }

  void _switchCamera() {
    if (cameras.length <= 1) return;
    
    setState(() {
      _selectedCameraIndex = (_selectedCameraIndex + 1) % cameras.length;
      _isInitialized = false;
    });
    
    _initCameraController(_selectedCameraIndex);
  }

  Future<void> _takePicture() async {
    if (_isTakingPicture || _controller == null || !_controller!.value.isInitialized) {
      return;
    }

    // INSTANT UI feedback with animation
    setState(() {
      _isTakingPicture = true;
      _isTimerCountingDown = false;
      _timerSeconds = 0;
      _showCaptureAnimation = true;
      _captureButtonPressed = false;
    });

    // Hide animation after short duration
    Timer(const Duration(milliseconds: 200), () {
      if (mounted) {
        setState(() {
          _showCaptureAnimation = false;
        });
      }
    });

    // Immediate UI reset in next frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _isTakingPicture = false;
        });
      }
    });

    // Take picture and process completely in background
    _captureAndProcessInBackground();
  }

  // Ultra-fast background capture
  void _captureAndProcessInBackground() {
    Future(() async {
      try {
        // Cancel any active timer
        _timerCountdown?.cancel();
        _timerCountdown = null;

        // Take picture
        final XFile image = await _controller!.takePicture();

        // Process everything without blocking UI
        _processPhotoInBackground(image);

      } catch (e) {
        // Silent error handling - don't block UI with error messages
      }
    });
  }

  // Ultra-lightweight background processing
  void _processPhotoInBackground(XFile image) {
    // Process in completely separate thread
    Future(() async {
      try {
        // Quick file operations
        final directory = await getApplicationDocumentsDirectory();
        final String folderPath = '${directory.path}/Pictures';
        await Directory(folderPath).create(recursive: true);

        final String fileName = '${DateTime.now().millisecondsSinceEpoch}.jpg';
        final String filePath = path.join(folderPath, fileName);
        await File(image.path).copy(filePath);

        // Quick location data
        LocationEntity? locationEntity;
        if (_currentPosition != null) {
          locationEntity = LocationEntity(
            latitude: _currentPosition!.latitude,
            longitude: _currentPosition!.longitude,
            timestamp: DateTime.now(),
            address: _currentAddress,
          );
        }

        if (!mounted) return;

        // Quick photo entity creation
        final photoEntity = PhotoEntity(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          path: filePath,
          location: locationEntity,
          address: _currentAddress,
          timestamp: DateTime.now(),
        );

        // Save to app storage
        final appProvider = Provider.of<AppProvider>(context, listen: false);
        final savedPhoto = await appProvider.addPhoto(photoEntity);

        // Device gallery save in separate future (no waiting)
        Future(() async {
          try {
            if (savedPhoto.processedPath != null) {
              await _saveToDeviceGallery(savedPhoto.processedPath!);
            } else {
              await _saveToDeviceGallery(savedPhoto.path);
            }
          } catch (e) {
            // Silent gallery save errors
          }
        });

      } catch (e) {
        // Silent processing errors for maximum speed
      }
    });
  }
  
  Future<void> _saveToDeviceGallery(String filePath) async {
    try {
      // Use the updated MediaStorageService which handles permissions properly
      final success = await MediaStorageService.saveImageToGallery(filePath);

      // Only show gallery save status if there's an issue
      if (mounted && !success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Note: Could not save to device gallery'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // Silently handle gallery save errors to avoid UI spam
      // Photo is still saved in app, just not in device gallery
    }
  }

  // Add this method to recover from camera errors
  Future<void> _recoverCamera() async {
    if (_isRecoveringCamera) return;
    
    _isRecoveringCamera = true;
    
    // Cancel any existing recovery timer
    _cameraRecoveryTimer?.cancel();
    
    // Reset any ongoing operations
    setState(() {
      // Reset any state if needed
    });
    
    try {
      // Dispose current controller
      await _disposeCamera();
      
      // Wait a moment before reinitializing
      await Future.delayed(const Duration(milliseconds: 1000));
      
      // Reinitialize camera
      if (mounted) {
        await _initCameraController(_selectedCameraIndex);
      }
    } catch (e) {
      // Try again after a delay if still not initialized
      if (mounted && (_controller == null || !_controller!.value.isInitialized)) {
        _cameraRecoveryTimer = Timer(const Duration(seconds: 3), () {
          _isRecoveringCamera = false;
          _recoverCamera();
        });
      }
    } finally {
      _isRecoveringCamera = false;
    }
  }


}
