import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';

import '../providers/app_state_provider.dart';
import '../widgets/photo_card_widget.dart';
import '../widgets/banner_ad_widget.dart';
import 'map_page.dart';
import 'photo_detail_page.dart';
import '../../domain/entities/photo_entity.dart';
import '../../data/services/media_storage_service.dart';
import '../../generated/l10n/app_localizations.dart';

class GalleryPage extends StatefulWidget {
  const GalleryPage({super.key});

  @override
  State<GalleryPage> createState() => _GalleryPageState();
}

class _GalleryPageState extends State<GalleryPage> {
  List<PhotoEntity> _photos = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPhotos();
  }

  Future<void> _loadPhotos() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get photos from the provider
      final appProvider = Provider.of<AppProvider>(context, listen: false);
      final photos = appProvider.photos;
      
      setState(() {
        _photos = photos.where((photo) => !photo.isVideo).toList(); // Only images
        _isLoading = false;
      });

      // If no photos in provider, try loading from file system
      if (photos.isEmpty) {
        await _loadImagesFromFileSystem();
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadImagesFromFileSystem() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final String folderPath = '${directory.path}/Pictures';
      
      final Directory folder = Directory(folderPath);
      
      if (await folder.exists()) {
        final List<FileSystemEntity> entities = await folder.list().toList();
        final List<File> files = entities
            .whereType<File>()
            .where((file) => file.path.endsWith('.jpg') || file.path.endsWith('.jpeg') || file.path.endsWith('.png'))
            .toList();
        
        // Sort by newest first
        files.sort((a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));
        
        // Convert files to PhotoEntity objects
        final List<PhotoEntity> photos = files.map((file) {
          return PhotoEntity(
            id: DateTime.now().millisecondsSinceEpoch.toString() + files.indexOf(file).toString(),
            path: file.path,
            timestamp: file.lastModifiedSync(),
          );
        }).toList();
        
        setState(() {
          _photos = photos;
          _isLoading = false;
        });
      } else {
        setState(() {
          _photos = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }



  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text('${l10n?.gallery ?? 'Gallery'} (${_photos.length} photos)',style: const TextStyle(color:Colors. black),),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Column(
        children: [
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _buildMediaGrid(_photos, AppLocalizations.of(context)?.noData ?? 'No photos found'),
          ),
          // Banner Ad at the bottom (always visible)
          const BannerAdWidget(
            margin: EdgeInsets.all(8.0),
            alwaysShow: true,
          ),
        ],
      ),
    );
  }

  Widget _buildMediaGrid(List<PhotoEntity> mediaList, String emptyMessage) {
    if (mediaList.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.photo,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              emptyMessage,
              style: const TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(8.0),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 8.0,
        mainAxisSpacing: 8.0,
        childAspectRatio: 0.75,
      ),
      itemCount: mediaList.length,
      itemBuilder: (context, index) {
        return PhotoCard(
          photo: mediaList[index],
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (_) => PhotoDetailPage(
                  photo: mediaList[index],
                  photosList: mediaList,
                  initialIndex: index,
                ),
              ),
            );
          },
          onLongPress: () {
            _showPhotoOptions(mediaList[index]);
          },
        );
      },
    );
  }

  void _showPhotoOptions(PhotoEntity photo) {
    final l10n = AppLocalizations.of(context);

    showModalBottomSheet(
      context: context,
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.save_alt),
                title: Text(l10n?.save ?? 'Save to Device Gallery'),
                onTap: () {
                  Navigator.pop(context);
                  _saveToDeviceGallery(photo);
                },
              ),
              ListTile(
                leading: const Icon(Icons.delete),
                title: Text(l10n?.delete ?? 'Delete Photo'),
                onTap: () {
                  Navigator.pop(context);
                  _confirmDeletePhoto(photo);
                },
              ),
              ListTile(
                leading: const Icon(Icons.close),
                title: Text(l10n?.cancel ?? 'Cancel'),
                onTap: () {
                  Navigator.pop(context);
                },
              ),
            ],
          ),
        );
      },
    );
  }
  
  Future<void> _saveToDeviceGallery(PhotoEntity photo) async {
    try {
      // Save to gallery using our custom service
      final success = await MediaStorageService.saveImageToGallery(
        photo.processedPath ?? photo.path
      );

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Photo saved to device gallery'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to save photo to device gallery'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving to gallery: $e')),
        );
      }
    }
  }
  
  void _confirmDeletePhoto(PhotoEntity photo) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Delete Photo'),
          content: const Text('Are you sure you want to delete this photo? This action cannot be undone.'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _deletePhoto(photo);
              },
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }
  
  Future<void> _deletePhoto(PhotoEntity photo) async {
    try {
      final appProvider = Provider.of<AppProvider>(context, listen: false);
      await appProvider.deletePhoto(photo);
      
      // Refresh the photos list
      setState(() {
        _photos.removeWhere((p) => p.id == photo.id);
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Photo deleted')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error deleting photo: $e')),
        );
      }
    }
  }
}


