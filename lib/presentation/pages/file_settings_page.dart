import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_state_provider.dart';
import '../widgets/banner_ad_widget.dart';
import '../../data/services/pro_status_manager.dart';
import '../../data/services/onboarding_service.dart';
import '../../domain/models/file_settings_model.dart';
import 'pro_upgrade_page.dart';
import 'onboarding_screen.dart';
import '../../generated/l10n/app_localizations.dart';

class FileSettingsPage extends StatefulWidget {
  const FileSettingsPage({Key? key}) : super(key: key);

  @override
  State<FileSettingsPage> createState() => _FileSettingsPageState();
}

class _FileSettingsPageState extends State<FileSettingsPage> {
  late TextEditingController _prefixController;
  late FileSettingsModel _settings;
  final ProStatusManager _proManager = ProStatusManager();
  String _selectedTemplate = 'default';

  @override
  void initState() {
    super.initState();
    final appProvider = Provider.of<AppProvider>(context, listen: false);
    _settings = appProvider.fileSettings;
    _prefixController = TextEditingController(text: _settings.customPrefix);
    // Pro status is now managed by ProStatusManager
    _selectedTemplate = _settings.selectedTemplate;
  }

  @override
  void dispose() {
    _prefixController.dispose();
    super.dispose();
  }

  void _saveSettings() {
    final appProvider = Provider.of<AppProvider>(context, listen: false);
    
    // Update settings with current values
    final updatedSettings = _settings.copyWith(
      customPrefix: _prefixController.text,
      isPro: _proManager.isProUser,
      selectedTemplate: _selectedTemplate,
    );
    
    // Save to provider
    appProvider.setFileSettings(updatedSettings);
    
    // Show confirmation
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Settings saved')),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }

  Widget _buildProFeatureTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      title,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.amber,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'PRO',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: _proManager.isProUser && value,
            onChanged: _proManager.isProUser ? onChanged : null,
          ),
        ],
      ),
    );
  }

  Widget _buildTemplateCard(BuildContext context, String templateId, Map<String, dynamic> template) {
    final isSelected = _selectedTemplate == templateId;
    final appProvider = Provider.of<AppProvider>(context, listen: false);
    
    return Card(
      elevation: isSelected ? 4 : 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color: isSelected ? (template['primaryColor'] as Color) : Colors.transparent,
          width: 2,
        ),
      ),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedTemplate = templateId;
            // Update prefix based on template
            _prefixController.text = template['prefix'];
          });
        },
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: template['primaryColor'],
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    template['name'].substring(0, 1),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                template['name'],
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                template['prefix'],
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              if (isSelected)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Icon(
                    Icons.check_circle,
                    color: template['primaryColor'],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final appProvider = Provider.of<AppProvider>(context);
    
    return Scaffold(
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          // File Settings Section
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'File Naming',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _prefixController,
                    decoration: const InputDecoration(
                      labelText: 'Custom Prefix',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildSwitchTile(
                    title: 'Include Date & Time',
                    subtitle: 'Add date and time to filename',
                    value: _settings.includeDateTime,
                    onChanged: (value) {
                      setState(() {
                        _settings = _settings.copyWith(includeDateTime: value);
                      });
                    },
                  ),
                  _buildSwitchTile(
                    title: 'Include Sequence Number',
                    subtitle: 'Add sequential number to filename',
                    value: _settings.includeSequenceNumber,
                    onChanged: (value) {
                      setState(() {
                        _settings = _settings.copyWith(includeSequenceNumber: value);
                      });
                    },
                  ),
                  _buildProFeatureTile(
                    title: 'Include Time Zone',
                    subtitle: 'Add time zone information to filename',
                    value: _settings.includeTimeZone,
                    onChanged: (value) {
                      setState(() {
                        _settings = _settings.copyWith(includeTimeZone: value);
                      });
                    },
                  ),
                  _buildProFeatureTile(
                    title: 'Include Plus Code',
                    subtitle: 'Add location plus code to filename',
                    value: _settings.includePlusCode,
                    onChanged: (value) {
                      setState(() {
                        _settings = _settings.copyWith(includePlusCode: value);
                      });
                    },
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Example Filename Section
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Example Filename',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _generateExampleFilename(),
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // App Settings Section
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'App Settings',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ListTile(
                    leading: const Icon(Icons.help_outline, color: Colors.teal),
                    title: const Text('Replay Tutorial'),
                    subtitle: const Text('View the introduction tutorial again'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () async {
                      // Reset onboarding and navigate to tutorial
                      final onboardingService = OnboardingService();
                      await onboardingService.resetOnboarding();

                      if (mounted) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const OnboardingScreen(),
                          ),
                        );
                      }
                    },
                  ),
                  const Divider(),
                  ListTile(
                    leading: const Icon(Icons.info_outline, color: Colors.blue),
                    title: const Text('App Version'),
                    subtitle: const Text('GPS Map Camera v1.0.0'),
                    trailing: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.green.withOpacity(0.3)),
                      ),
                      child: const Text(
                        'Latest',
                        style: TextStyle(
                          color: Colors.green,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Pro Features Section
          if (!_proManager.isProUser)
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Pro Features',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Unlock additional features with Pro version:',
                      style: TextStyle(fontSize: 14),
                    ),
                    const SizedBox(height: 8),
                    const ListTile(
                      leading: Icon(Icons.access_time),
                      title: Text('Time Zone in Filename'),
                      dense: true,
                    ),
                    const ListTile(
                      leading: Icon(Icons.location_on),
                      title: Text('Plus Code in Filename'),
                      dense: true,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(builder: (context) => const ProUpgradePage()),
                        );
                      },
                      icon: const Icon(Icons.diamond),
                      label: const Text('Upgrade to Pro'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.purple,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                      ),
                    ),
                  ],
                ),
              ),
            ),

          // Banner Ad (always visible)
          const BannerAdWidget(
            margin: EdgeInsets.all(8.0),
            alwaysShow: true,
          ),
        ],
      ),
      bottomNavigationBar: BottomAppBar(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: ElevatedButton(
            onPressed: _saveSettings,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12.0),
              backgroundColor: appProvider.currentTemplateSettings['primaryColor'],
            ),
            child: const Text('Save Settings'),
          ),
        ),
      ),
    );
  }

  String _generateExampleFilename() {
    List<String> parts = [];
    
    // Add custom prefix if set
    if (_prefixController.text.isNotEmpty) {
      parts.add(_prefixController.text);
    }
    
    // Add date and time if enabled
    if (_settings.includeDateTime) {
      final now = DateTime.now();
      parts.add('${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}_'
          '${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}${now.second.toString().padLeft(2, '0')}');
    }
    
    // Add sequence number if enabled
    if (_settings.includeSequenceNumber) {
      parts.add('_0001');
    }
    
    // Add timezone if enabled (Pro feature)
    if (_settings.includeTimeZone && _proManager.isProUser) {
      final now = DateTime.now();
      final timeZoneOffset = now.timeZoneOffset;
      final timeZoneString = '${timeZoneOffset.isNegative ? '-' : '+'}${timeZoneOffset.inHours.abs().toString().padLeft(2, '0')}${(timeZoneOffset.inMinutes % 60).toString().padLeft(2, '0')}';
      parts.add('_TZ$timeZoneString');
    }
    
    // Add plus code if enabled (Pro feature)
    if (_settings.includePlusCode && _proManager.isProUser) {
      parts.add('_PC8FVC9G8V+6X');
    }
    
    return '${parts.join('')}.jpg';
  }
}