import 'dart:io';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import '../../domain/entities/photo_entity.dart';
import '../providers/app_state_provider.dart';
import 'map_page.dart';
import 'video_player_page.dart';
import '../../data/services/media_storage_service.dart';
import '../../data/services/image_processing_service.dart';

class PhotoDetailPage extends StatefulWidget {
  final PhotoEntity photo;
  final List<PhotoEntity>? photosList;
  final int? initialIndex;

  const PhotoDetailPage({
    Key? key, 
    required this.photo, 
    this.photosList,
    this.initialIndex,
  }) : super(key: key);

  @override
  State<PhotoDetailPage> createState() => _PhotoDetailPageState();
}

class _PhotoDetailPageState extends State<PhotoDetailPage> {
  late PageController _pageController;
  late List<PhotoEntity> _photos;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _photos = widget.photosList ?? [widget.photo];
    _currentIndex = widget.initialIndex ?? 0;
    _pageController = PageController(initialPage: _currentIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => _sharePhoto(_photos[_currentIndex]),
          ),
          IconButton(
            icon: const Icon(Icons.save_alt),
            onPressed: () => _saveToDeviceGallery(_photos[_currentIndex]),
          ),
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () => _showOptionsMenu(_photos[_currentIndex]),
          ),
        ],
      ),
      body: PageView.builder(
        controller: _pageController,
        itemCount: _photos.length,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        itemBuilder: (context, index) {
          final photo = _photos[index];
          
          if (photo.isVideo) {
            return _buildVideoPlaceholder(photo);
          } else {
            return _buildImageViewer(photo);
          }
        },
      ),
    );
  }

  Widget _buildVideoPlaceholder(PhotoEntity video) {
    return GestureDetector(
      onTap: () => _openVideoPlayer(video),
      child: Container(
        color: Colors.black,
        child: Stack(
          fit: StackFit.expand,
          children: [
            // Video thumbnail (could be a black background)
            Center(
              child: Icon(
                Icons.videocam,
                color: Colors.white24,
                size: 100,
              ),
            ),
            
            // Play button and info
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.8),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.play_arrow,
                      color: Colors.white,
                      size: 50,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.6),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      'Tap to play video',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  if (video.duration != null) ...[
                    const SizedBox(height: 8),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.6),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.timer, color: Colors.white, size: 16),
                          SizedBox(width: 4),
                          Text(
                            _formatDuration(video.duration!),
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
            
            // Video info at the bottom
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              child: Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    colors: [
                      Colors.black.withOpacity(0.8),
                      Colors.transparent,
                    ],
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (video.address != null)
                      Text(
                        video.address!,
                        style: TextStyle(color: Colors.white, fontSize: 14),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    Text(
                      _formatDate(video.timestamp),
                      style: TextStyle(color: Colors.white70, fontSize: 12),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _openVideoPlayer(PhotoEntity video) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VideoPlayerPage(video: video),
      ),
    );
  }

  Widget _buildImageViewer(PhotoEntity photo) {
    return FutureBuilder<bool>(
      future: ImageProcessingService.isValidImageFile(photo.processedPath ?? photo.path),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(child: CircularProgressIndicator());
        }
        
        if (snapshot.hasError || snapshot.data == false) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.broken_image,
                  color: Colors.white70,
                  size: 64,
                ),
                const SizedBox(height: 16),
                Text(
                  'Invalid or corrupted image file',
                  style: TextStyle(color: Colors.white),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }
        
        return Hero(
          tag: 'photo_${photo.id}',
          child: InteractiveViewer(
            panEnabled: true,
            boundaryMargin: const EdgeInsets.all(20),
            minScale: 0.5,
            maxScale: 4,
            child: Image.file(
              File(photo.processedPath ?? photo.path),
              fit: BoxFit.contain,
              errorBuilder: (context, error, stackTrace) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.broken_image,
                        color: Colors.white70,
                        size: 64,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Error loading image',
                        style: TextStyle(color: Colors.white),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  void _showOptionsMenu(PhotoEntity photo) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.map),
                title: const Text('Show on Map'),
                onTap: () {
                  Navigator.pop(context);
                  if (photo.location != null) {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (_) => MapPage(
                          location: photo.location!,
                          photos: [photo],
                        ),
                      ),
                    );
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('No location data available')),
                    );
                  }
                },
              ),
              ListTile(
                leading: const Icon(Icons.delete),
                title: const Text('Delete'),
                onTap: () {
                  Navigator.pop(context);
                  _confirmDeletePhoto(photo);
                },
              ),
              ListTile(
                leading: const Icon(Icons.info),
                title: const Text('Details'),
                onTap: () {
                  Navigator.pop(context);
                  _showPhotoDetails(photo);
                },
              ),
              ListTile(
                leading: const Icon(Icons.close),
                title: const Text('Cancel'),
                onTap: () {
                  Navigator.pop(context);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _saveToDeviceGallery(PhotoEntity photo) async {
    try {
      final success = await MediaStorageService.saveImageToGallery(
        photo.processedPath ?? photo.path
      );
      
      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('${photo.isVideo ? 'Video' : 'Photo'} saved to device gallery')),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to save ${photo.isVideo ? 'video' : 'photo'} to gallery')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }
  }

  Future<void> _sharePhoto(PhotoEntity photo) async {
    try {
      await  Share.shareXFiles(
        [XFile(photo.processedPath ?? photo.path)],
        text: photo.isVideo ? 'Video taken with GPS Map Camera' : 'Photo taken with GPS Map Camera',
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error sharing: $e')),
        );
      }
    }
  }

  void _confirmDeletePhoto(PhotoEntity photo) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('Delete ${photo.isVideo ? 'Video' : 'Photo'}'),
          content: Text('Are you sure you want to delete this ${photo.isVideo ? 'video' : 'photo'}? This action cannot be undone.'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _deletePhoto(photo);
              },
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  void _deletePhoto(PhotoEntity photo) async {
    try {
      final appProvider = Provider.of<AppProvider>(context, listen: false);
      await appProvider.deletePhoto(photo);
      
      // Delete the file
      final file = File(photo.path);
      if (await file.exists()) {
        await file.delete();
      }
      
      // Delete processed file if it exists
      if (photo.processedPath != null) {
        final processedFile = File(photo.processedPath!);
        if (await processedFile.exists()) {
          await processedFile.delete();
        }
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${photo.isVideo ? 'Video' : 'Photo'} deleted')),
        );
        
        // If there are no more photos, go back
        if (_photos.length <= 1) {
          Navigator.pop(context);
        } else {
          // Remove the photo from the list and update the page
          setState(() {
            _photos.removeAt(_currentIndex);
            if (_currentIndex >= _photos.length) {
              _currentIndex = _photos.length - 1;
            }
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error deleting: $e')),
        );
      }
    }
  }

  void _showPhotoDetails(PhotoEntity photo) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('${photo.isVideo ? 'Video' : 'Photo'} Details'),
          content: SingleChildScrollView(  // Add this to make content scrollable
            child: Column(
              mainAxisSize: MainAxisSize.min,  // Use min to take only needed space
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Date: ${photo.timestamp.toString()}'),
                const SizedBox(height: 8),
                if (photo.location != null) ...[
                  Text('Location: ${photo.location!.latitude}, ${photo.location!.longitude}'),
                  const SizedBox(height: 8),
                ],
                if (photo.address != null) ...[
                  Text('Address: ${photo.address}'),
                  const SizedBox(height: 8),
                ],
                if (photo.isVideo && photo.duration != null) ...[
                  Text('Duration: ${_formatDuration(photo.duration!)}'),
                  const SizedBox(height: 8),
                ],
                Text('File: ${photo.path.split('/').last}'),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return "$minutes:$seconds";
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}