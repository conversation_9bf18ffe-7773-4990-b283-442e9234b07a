import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gpsmapcamera/presentation/pages/camera_page.dart';
import 'package:gpsmapcamera/presentation/pages/gallery_page.dart';
import 'package:gpsmapcamera/presentation/pages/map_view_page.dart';
import 'package:gpsmapcamera/presentation/pages/settings_page.dart';
import 'package:gpsmapcamera/presentation/providers/app_state_provider.dart';
import 'package:provider/provider.dart';

import '../../data/datasources/location_data_source.dart';
import '../../data/services/app_open_ad_manager.dart';
import '../../generated/l10n/app_localizations.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}
class _HomePageState extends State<HomePage> {
  final locationDatasource = LocationDatasource();
  final AppLifecycleObserver _lifecycleObserver = AppLifecycleObserver();
  int _selectedIndex = 2;

  List<Widget> get _pages => [
    const GalleryPage(),
    const MapViewPage(),
    CameraPage(
      onNavigateToGallery: () {
        setState(() {
          _selectedIndex = 0; // Navigate to gallery tab
        });
      },
    ),
    const SettingsPage(),
  ];


  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(_lifecycleObserver);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(_lifecycleObserver);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final appProvider = Provider.of<AppProvider>(context);
    final templateSettings = appProvider.currentTemplateSettings;

    return Scaffold(
      body: _pages[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        type: BottomNavigationBarType.fixed, // Important for more than 3 items
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        items: [
          BottomNavigationBarItem(
            icon: const Icon(Icons.photo_library),
            label: AppLocalizations.of(context)?.gallery ?? 'Gallery',
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.map),
            label: AppLocalizations.of(context)?.map ?? 'Map',
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.camera_alt),
            label: AppLocalizations.of(context)?.camera ?? 'Camera',
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.settings),
            label: AppLocalizations.of(context)?.settings ?? 'Settings',
          ),
        ],
        selectedItemColor: templateSettings['primaryColor'],
      ),
    );
  }
}