import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;
import 'dart:typed_data';
import '../services/measurement_storage_service.dart';
import '../services/measurement_export_service.dart';
import '../widgets/banner_ad_widget.dart';
import '../../generated/l10n/app_localizations.dart';

class MeasurementViewerPage extends StatefulWidget {
  final MeasurementRecord measurement;
  final String? returnPageTitle;

  const MeasurementViewerPage({
    super.key,
    required this.measurement,
    this.returnPageTitle,
  });

  @override
  State<MeasurementViewerPage> createState() => _MeasurementViewerPageState();
}

class _MeasurementViewerPageState extends State<MeasurementViewerPage> {
  GoogleMapController? _mapController;
  final Set<Marker> _markers = {};
  final Set<Polygon> _polygons = {};
  final Set<Polyline> _polylines = {};
  bool _isLoading = true;
  bool _showMeasurementInfo = true;
  LatLngBounds? _bounds;

  // Cache for distance markers to avoid recreating identical markers
  final Map<String, BitmapDescriptor> _distanceMarkerCache = {};

  @override
  void initState() {
    super.initState();
    _initializeMapData();
  }

  Future<void> _initializeMapData() async {
    await _createMapVisualization();
    setState(() => _isLoading = false);
  }

  Future<void> _createMapVisualization() async {
    final measurement = widget.measurement;
    final points = measurement.points;
    
    if (points.isEmpty) return;

    // Convert points to LatLng
    List<LatLng> latLngPoints = points.map((point) => 
      LatLng(point['latitude']!, point['longitude']!)
    ).toList();

    // Calculate bounds for all points
    _calculateBounds(latLngPoints);

    // Create visualization based on measurement type
    switch (measurement.type.toLowerCase()) {
      case 'field':
        await _createFieldVisualization(latLngPoints, measurement);
        break;
      case 'distance':
        await _createDistanceVisualization(latLngPoints, measurement);
        break;
      case 'marker':
        await _createMarkerVisualization(latLngPoints, measurement);
        break;
      default:
        await _createGenericVisualization(latLngPoints, measurement);
        break;
    }
  }

  void _calculateBounds(List<LatLng> points) {
    if (points.isEmpty) return;

    double minLat = points.first.latitude;
    double maxLat = points.first.latitude;
    double minLng = points.first.longitude;
    double maxLng = points.first.longitude;

    for (LatLng point in points) {
      minLat = math.min(minLat, point.latitude);
      maxLat = math.max(maxLat, point.latitude);
      minLng = math.min(minLng, point.longitude);
      maxLng = math.max(maxLng, point.longitude);
    }

    // Add padding
    double latPadding = (maxLat - minLat) * 0.1;
    double lngPadding = (maxLng - minLng) * 0.1;

    _bounds = LatLngBounds(
      southwest: LatLng(minLat - latPadding, minLng - lngPadding),
      northeast: LatLng(maxLat + latPadding, maxLng + lngPadding),
    );
  }

  Future<void> _createFieldVisualization(List<LatLng> points, MeasurementRecord measurement) async {
    // Create numbered markers for each point
    for (int i = 0; i < points.length; i++) {
      final marker = Marker(
        markerId: MarkerId('field_point_$i'),
        position: points[i],
        icon: await _createNumberedMarkerIcon(i + 1, 
          i == 0 ? Colors.green : Colors.red),
        infoWindow: InfoWindow(
          title: i == 0 ? 'Start Point' : 'Point ${i + 1}',
          snippet: '${points[i].latitude.toStringAsFixed(6)}, ${points[i].longitude.toStringAsFixed(6)}',
        ),
      );
      _markers.add(marker);
    }

    // Create polygon if more than 2 points
    if (points.length > 2) {
      final polygon = Polygon(
        polygonId: const PolygonId('field_area'),
        points: points,
        strokeColor: Colors.green,
        strokeWidth: 3,
        fillColor: Colors.green.withOpacity(0.2),
      );
      _polygons.add(polygon);

      // Add area label at center
      LatLng center = _calculatePolygonCenter(points);
      final areaMarker = Marker(
        markerId: const MarkerId('area_label'),
        position: center,
        icon: await _createAreaLabelIcon(measurement.area),
        anchor: const Offset(0.5, 0.5),
      );
      _markers.add(areaMarker);
    }

    // Create perimeter polyline
    List<LatLng> perimeterPoints = List.from(points);
    if (points.length > 2) {
      perimeterPoints.add(points.first); // Close the polygon
    }

    final polyline = Polyline(
      polylineId: const PolylineId('field_perimeter'),
      points: perimeterPoints,
      color: Colors.green,
      width: 3,
      patterns: [PatternItem.dash(20), PatternItem.gap(10)],
    );
    _polylines.add(polyline);

    // Add distance markers for field segments
    await _addFieldDistanceMarkers(points, measurement);
  }

  Future<void> _createDistanceVisualization(List<LatLng> points, MeasurementRecord measurement) async {
    // Create numbered markers for each point
    for (int i = 0; i < points.length; i++) {
      final marker = Marker(
        markerId: MarkerId('distance_point_$i'),
        position: points[i],
        icon: await _createNumberedMarkerIcon(i + 1, 
          i == 0 ? Colors.green : (i == points.length - 1 ? Colors.red : Colors.blue)),
        infoWindow: InfoWindow(
          title: i == 0 ? 'Start Point' : (i == points.length - 1 ? 'End Point' : 'Point ${i + 1}'),
          snippet: '${points[i].latitude.toStringAsFixed(6)}, ${points[i].longitude.toStringAsFixed(6)}',
        ),
      );
      _markers.add(marker);
    }

    // Create polyline connecting all points
    final polyline = Polyline(
      polylineId: const PolylineId('distance_path'),
      points: points,
      color: Colors.blue,
      width: 4,
    );
    _polylines.add(polyline);

    // Add distance labels between consecutive points
    final segmentDistances = measurement.metadata['segment_distances'] as List<dynamic>? ?? [];
    for (int i = 0; i < points.length - 1; i++) {
      LatLng midPoint = LatLng(
        (points[i].latitude + points[i + 1].latitude) / 2,
        (points[i].longitude + points[i + 1].longitude) / 2,
      );

      double distance = segmentDistances.length > i 
        ? segmentDistances[i].toDouble() 
        : _calculateDistance(points[i], points[i + 1]);

      final distanceMarker = Marker(
        markerId: MarkerId('distance_label_$i'),
        position: midPoint,
        icon: await _createDistanceLabelIcon(distance),
        anchor: const Offset(0.5, 0.5),
      );
      _markers.add(distanceMarker);
    }

    // Add total distance label at the end
    if (points.length > 1) {
      final totalDistanceMarker = Marker(
        markerId: const MarkerId('total_distance_label'),
        position: points.last,
        icon: await _createTotalDistanceLabelIcon(measurement.distance),
        anchor: const Offset(0.5, -0.1),
      );
      _markers.add(totalDistanceMarker);
    }
  }

  Future<void> _createMarkerVisualization(List<LatLng> points, MeasurementRecord measurement) async {
    if (points.isEmpty) return;

    final category = measurement.metadata['category'] ?? 'General';
    final description = measurement.metadata['description'] ?? '';
    
    // Get category color and icon
    Color markerColor = _getCategoryColor(category);
    IconData markerIcon = _getCategoryIcon(category);

    final marker = Marker(
      markerId: const MarkerId('location_marker'),
      position: points.first,
      icon: await _createCategoryMarkerIcon(markerIcon, markerColor),
      infoWindow: InfoWindow(
        title: measurement.name,
        snippet: description.isNotEmpty ? description : category,
      ),
    );
    _markers.add(marker);
  }

  Future<void> _createGenericVisualization(List<LatLng> points, MeasurementRecord measurement) async {
    // Create simple markers for generic measurements
    for (int i = 0; i < points.length; i++) {
      final marker = Marker(
        markerId: MarkerId('generic_point_$i'),
        position: points[i],
        infoWindow: InfoWindow(
          title: 'Point ${i + 1}',
          snippet: '${points[i].latitude.toStringAsFixed(6)}, ${points[i].longitude.toStringAsFixed(6)}',
        ),
      );
      _markers.add(marker);
    }

    // Connect points with polyline if more than one point
    if (points.length > 1) {
      final polyline = Polyline(
        polylineId: const PolylineId('generic_path'),
        points: points,
        color: Colors.purple,
        width: 3,
      );
      _polylines.add(polyline);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.measurement.name),
        backgroundColor: _getThemeColor(),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(_showMeasurementInfo ? Icons.info : Icons.info_outline),
            onPressed: () {
              setState(() => _showMeasurementInfo = !_showMeasurementInfo);
            },
          ),
          IconButton(
            icon: const Icon(Icons.zoom_out_map),
            onPressed: _fitMapToBounds,
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'export':
                  _exportMeasurement();
                  break;
                case 'share':
                  _shareMeasurement();
                  break;
                case 'details':
                  _showMeasurementDetails();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'details', child: Text('View Details')),
              const PopupMenuItem(value: 'export', child: Text('Export PDF')),
              const PopupMenuItem(value: 'share', child: Text('Share')),
            ],
          ),
        ],
      ),
      
      body: SafeArea(
        child: Stack(
          children: [
            // Google Map
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : GoogleMap(
                    onMapCreated: _onMapCreated,
                    initialCameraPosition: CameraPosition(
                      target: widget.measurement.points.isNotEmpty
                          ? LatLng(
                              widget.measurement.points.first['latitude']!,
                              widget.measurement.points.first['longitude']!,
                            )
                          : const LatLng(0, 0),
                      zoom: 15,
                    ),
                    markers: _markers,
                    polygons: _polygons,
                    polylines: _polylines,
                    mapType: MapType.hybrid,
                    myLocationEnabled: true,
                    myLocationButtonEnabled: false,
                    zoomControlsEnabled: false,
                    mapToolbarEnabled: false,
                  ),

            // Top Banner Ad
            const Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: BannerAdWidget(
                margin: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                alwaysShow: true,
              ),
            ),

            // Measurement Info Panel
            if (_showMeasurementInfo)
              Positioned(
                top: 60,
                left: 16,
                right: 16,
                child: _buildMeasurementInfoPanel(),
              ),

            // Control buttons
            Positioned(
              bottom: 20,
              right: 16,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  FloatingActionButton(
                    heroTag: "fit_bounds_fab",
                    onPressed: _fitMapToBounds,
                    backgroundColor: _getThemeColor(),
                    child: const Icon(Icons.zoom_out_map, color: Colors.white),
                  ),
                  const SizedBox(height: 12),
                  FloatingActionButton(
                    heroTag: "my_location_fab",
                    onPressed: _goToMyLocation,
                    backgroundColor: Colors.blue,
                    child: const Icon(Icons.my_location, color: Colors.white),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMeasurementInfoPanel() {
    final measurement = widget.measurement;
    
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [_getThemeColor().withOpacity(0.1), Colors.white],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getThemeColor().withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(_getTypeIcon(), color: _getThemeColor(), size: 20),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        measurement.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        _getTypeDisplayName(),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () {
                    setState(() => _showMeasurementInfo = false);
                  },
                  icon: const Icon(Icons.close, size: 20),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Measurement details
            _buildMeasurementDetails(),
          ],
        ),
      ),
    );
  }

  Widget _buildMeasurementDetails() {
    final measurement = widget.measurement;
    
    switch (measurement.type.toLowerCase()) {
      case 'field':
        return Column(
          children: [
            _buildDetailRow('Area', _formatArea(measurement.area)),
            _buildDetailRow('Perimeter', _formatDistance(measurement.perimeter)),
            _buildDetailRow('Points', '${measurement.points.length}'),
          ],
        );
      case 'distance':
        return Column(
          children: [
            _buildDetailRow('Total Distance', _formatDistance(measurement.distance)),
            _buildDetailRow('Points', '${measurement.points.length}'),
            _buildDetailRow('Segments', '${measurement.points.length - 1}'),
          ],
        );
      case 'marker':
        final category = measurement.metadata['category'] ?? 'General';
        final description = measurement.metadata['description'] ?? '';
        return Column(
          children: [
            _buildDetailRow('Category', category),
            if (description.isNotEmpty)
              _buildDetailRow('Description', description),
            _buildDetailRow('Coordinates', 
              '${measurement.points.first['latitude']?.toStringAsFixed(6)}, ${measurement.points.first['longitude']?.toStringAsFixed(6)}'),
          ],
        );
      default:
        return Column(
          children: [
            _buildDetailRow('Points', '${measurement.points.length}'),
            _buildDetailRow('Type', measurement.type),
          ],
        );
    }
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.grey.shade700,
              fontSize: 14,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: _getThemeColor(),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods for map creation
  Future<BitmapDescriptor> _createNumberedMarkerIcon(int number, Color color) async {
    // This would create a custom numbered marker icon
    // For now, using default markers with different colors
    switch (color) {
      case Colors.green:
        return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen);
      case Colors.red:
        return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed);
      case Colors.blue:
        return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue);
      default:
        return BitmapDescriptor.defaultMarker;
    }
  }

  Future<BitmapDescriptor> _createAreaLabelIcon(double area) async {
    // Create custom icon for area label
    return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen);
  }

  // Get cached distance marker to avoid recreating identical markers
  Future<BitmapDescriptor> _getCachedDistanceMarker(String distance, {bool isDistanceMeasurement = false}) async {
    String cacheKey = '${distance}_${isDistanceMeasurement ? 'blue' : 'green'}';

    if (_distanceMarkerCache.containsKey(cacheKey)) {
      return _distanceMarkerCache[cacheKey]!;
    }

    BitmapDescriptor marker = await _createDistanceTextMarker(distance, isDistanceMeasurement: isDistanceMeasurement);
    _distanceMarkerCache[cacheKey] = marker;
    return marker;
  }

  // Create custom distance text marker that's always visible
  Future<BitmapDescriptor> _createDistanceTextMarker(String distance, {bool isDistanceMeasurement = false}) async {
    try {
      final ui.PictureRecorder pictureRecorder = ui.PictureRecorder();
      final Canvas canvas = Canvas(pictureRecorder);

      // Optimized marker dimensions for larger font
      const double width = 200.0;
      const double height = 70.0;
      const double radius = 12.0;

      // Enhanced colors for better visibility - use blue for distance measurements
      Color backgroundColor = isDistanceMeasurement ? Colors.blue.shade600 : Colors.green.shade600;
      Color borderColor = isDistanceMeasurement ? Colors.blue.shade800 : Colors.green.shade800;
      Color textColor = Colors.white;

      // Draw outer border
      final RRect outerRect = RRect.fromRectAndRadius(
        const Rect.fromLTWH(0, 0, width, height),
        const Radius.circular(radius),
      );

      final Paint borderPaint = Paint()
        ..color = borderColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = 3.0;

      canvas.drawRRect(outerRect, borderPaint);

      // Draw background
      final Paint backgroundPaint = Paint()
        ..color = backgroundColor
        ..style = PaintingStyle.fill;

      canvas.drawRRect(outerRect, backgroundPaint);

      // Draw distance text with enhanced visibility
      final textPainter = TextPainter(
        text: TextSpan(
          text: distance,
          style: TextStyle(
            color: textColor,
            fontSize: 32, // Large font for better visibility
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                color: Colors.black.withOpacity(0.8),
                offset: const Offset(1, 1),
                blurRadius: 3,
              ),
              Shadow(
                color: Colors.black.withOpacity(0.4),
                offset: const Offset(0.5, 0.5),
                blurRadius: 1,
              ),
            ],
          ),
        ),
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();
      final double textX = (width - textPainter.width) / 2;
      final double textY = (height - textPainter.height) / 2;
      textPainter.paint(canvas, Offset(textX, textY));

      // Convert to image
      final ui.Picture picture = pictureRecorder.endRecording();
      final ui.Image image = await picture.toImage(width.toInt(), height.toInt());
      final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final Uint8List uint8List = byteData!.buffer.asUint8List();

      return BitmapDescriptor.fromBytes(uint8List);
    } catch (e) {
      // Fallback to default marker
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen);
    }
  }

  Future<BitmapDescriptor> _createDistanceLabelIcon(double distance) async {
    // Use the new text-based distance marker with appropriate color theme
    bool isDistanceMeasurement = widget.measurement.type.toLowerCase() == 'distance';
    return await _getCachedDistanceMarker(_formatDistance(distance), isDistanceMeasurement: isDistanceMeasurement);
  }

  Future<BitmapDescriptor> _createTotalDistanceLabelIcon(double distance) async {
    // Use the new text-based distance marker for total distance with appropriate color theme
    bool isDistanceMeasurement = widget.measurement.type.toLowerCase() == 'distance';
    return await _getCachedDistanceMarker(_formatDistance(distance), isDistanceMeasurement: isDistanceMeasurement);
  }

  Future<BitmapDescriptor> _createCategoryMarkerIcon(IconData icon, Color color) async {
    // Create custom icon for category marker
    if (color == Colors.red) {
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed);
    } else if (color == Colors.blue) {
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue);
    } else if (color == Colors.green) {
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen);
    } else if (color == Colors.orange) {
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueOrange);
    } else {
      return BitmapDescriptor.defaultMarker;
    }
  }

  LatLng _calculatePolygonCenter(List<LatLng> points) {
    double lat = 0;
    double lng = 0;
    
    for (LatLng point in points) {
      lat += point.latitude;
      lng += point.longitude;
    }
    
    return LatLng(lat / points.length, lng / points.length);
  }

  double _calculateDistance(LatLng point1, LatLng point2) {
    const double earthRadius = 6371000; // Earth's radius in meters

    double lat1Rad = point1.latitude * (math.pi / 180);
    double lat2Rad = point2.latitude * (math.pi / 180);
    double deltaLatRad = (point2.latitude - point1.latitude) * (math.pi / 180);
    double deltaLngRad = (point2.longitude - point1.longitude) * (math.pi / 180);

    double a = math.sin(deltaLatRad / 2) * math.sin(deltaLatRad / 2) +
        math.cos(lat1Rad) * math.cos(lat2Rad) *
        math.sin(deltaLngRad / 2) * math.sin(deltaLngRad / 2);
    double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));

    return earthRadius * c;
  }

  // Theme and styling helpers
  Color _getThemeColor() {
    switch (widget.measurement.type.toLowerCase()) {
      case 'field':
        return Colors.green;
      case 'distance':
        return Colors.blue;
      case 'marker':
        return Colors.orange;
      default:
        return Colors.purple;
    }
  }

  IconData _getTypeIcon() {
    switch (widget.measurement.type.toLowerCase()) {
      case 'field':
        return Icons.crop_free_rounded;
      case 'distance':
        return Icons.straighten_rounded;
      case 'marker':
        return Icons.place_rounded;
      default:
        return Icons.location_on;
    }
  }

  String _getTypeDisplayName() {
    switch (widget.measurement.type.toLowerCase()) {
      case 'field':
        return 'Field Measurement';
      case 'distance':
        return 'Distance Measurement';
      case 'marker':
        return 'Location Marker';
      default:
        return 'Measurement';
    }
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'Important':
        return Colors.red;
      case 'Landmark':
        return Colors.purple;
      case 'Building':
        return Colors.brown;
      case 'Parking':
        return Colors.green;
      case 'Restaurant':
        return Colors.orange;
      default:
        return Colors.blue;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'Important':
        return Icons.star;
      case 'Landmark':
        return Icons.location_city;
      case 'Building':
        return Icons.business;
      case 'Parking':
        return Icons.local_parking;
      case 'Restaurant':
        return Icons.restaurant;
      default:
        return Icons.place;
    }
  }

  // Map control methods
  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
    _fitMapToBounds();
  }

  void _fitMapToBounds() {
    if (_mapController != null && _bounds != null) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngBounds(_bounds!, 100),
      );
    }
  }

  void _goToMyLocation() {
    // Implementation for going to user's current location
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('My Location feature - implementation needed'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  // Action methods
  Future<void> _exportMeasurement() async {
    try {
      _showLoadingDialog('Exporting measurement...');
      
      final pdfFile = await MeasurementExportService.exportMeasurementToPDF(
        measurement: widget.measurement,
        additionalNotes: 'Measurement exported from GPS Map Camera viewer',
      );
      
      Navigator.pop(context); // Close loading dialog
      
      await MeasurementExportService.shareMeasurementFile(
        file: pdfFile,
        measurementName: widget.measurement.name,
      );
      
      _showSuccessSnackBar('Measurement exported successfully!');
    } catch (e) {
      Navigator.pop(context); // Close loading dialog
      _showErrorSnackBar('Error exporting measurement: $e');
    }
  }

  void _shareMeasurement() {
    // Implementation for sharing measurement
    _showSuccessSnackBar('Share feature - implementation needed');
  }

  void _showMeasurementDetails() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(widget.measurement.name),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDialogDetailRow('Type', _getTypeDisplayName()),
              _buildDialogDetailRow('Date', _formatDateTime(widget.measurement.timestamp)),
              _buildDialogDetailRow('Points', '${widget.measurement.points.length}'),
              if (widget.measurement.type.toLowerCase() == 'field') ...[
                _buildDialogDetailRow('Area', _formatArea(widget.measurement.area)),
                _buildDialogDetailRow('Perimeter', _formatDistance(widget.measurement.perimeter)),
              ],
              if (widget.measurement.type.toLowerCase() == 'distance')
                _buildDialogDetailRow('Distance', _formatDistance(widget.measurement.distance)),
              if (widget.measurement.metadata['notes']?.isNotEmpty == true)
                _buildDialogDetailRow('Notes', widget.measurement.metadata['notes']),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _exportMeasurement();
            },
            style: ElevatedButton.styleFrom(backgroundColor: _getThemeColor()),
            child: const Text('Export'),
          ),
        ],
      ),
    );
  }

  Widget _buildDialogDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  // Utility methods
  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Text(message),
          ],
        ),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  Future<void> _addFieldDistanceMarkers(List<LatLng> points, MeasurementRecord measurement) async {
    if (points.length < 2) return;

    final segmentDistances = measurement.metadata['segment_distances'] as List<dynamic>? ?? [];

    // Add distance markers for consecutive segments
    for (int i = 0; i < points.length - 1; i++) {
      LatLng midPoint = LatLng(
        (points[i].latitude + points[i + 1].latitude) / 2,
        (points[i].longitude + points[i + 1].longitude) / 2,
      );

      double distance = segmentDistances.length > i
        ? segmentDistances[i].toDouble()
        : _calculateDistance(points[i], points[i + 1]);

      final distanceMarker = Marker(
        markerId: MarkerId('field_segment_distance_$i'),
        position: midPoint,
        icon: await _getCachedDistanceMarker(_formatDistance(distance), isDistanceMeasurement: false),
        anchor: const Offset(0.5, 0.5),
        infoWindow: InfoWindow(
          title: 'Segment ${i + 1}→${i + 2}',
          snippet: _formatDistance(distance),
        ),
      );
      _markers.add(distanceMarker);
    }

    // Add closing distance marker for complete fields (3+ points)
    if (points.length >= 3) {
      LatLng closingMidPoint = LatLng(
        (points.last.latitude + points.first.latitude) / 2,
        (points.last.longitude + points.first.longitude) / 2,
      );

      double closingDistance = _calculateDistance(points.last, points.first);

      final closingDistanceMarker = Marker(
        markerId: const MarkerId('field_closing_distance'),
        position: closingMidPoint,
        icon: await _getCachedDistanceMarker(_formatDistance(closingDistance), isDistanceMeasurement: false),
        anchor: const Offset(0.5, 0.5),
        infoWindow: InfoWindow(
          title: 'Closing Segment ${points.length}→1',
          snippet: _formatDistance(closingDistance),
        ),
      );
      _markers.add(closingDistanceMarker);
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _formatDistance(double meters) {
    if (meters < 1000) {
      return '${meters.toStringAsFixed(1)}m';
    } else {
      return '${(meters / 1000).toStringAsFixed(2)}km';
    }
  }

  String _formatArea(double sqMeters) {
    if (sqMeters < 1) {
      return '${sqMeters.toStringAsFixed(3)} m²';
    } else if (sqMeters < 10000) {
      return '${sqMeters.toStringAsFixed(1)} m²';
    } else {
      double hectares = sqMeters / 10000;
      double acres = sqMeters / 4046.86;
      return '${hectares.toStringAsFixed(2)} ha (${acres.toStringAsFixed(2)} acres)';
    }
  }
}
