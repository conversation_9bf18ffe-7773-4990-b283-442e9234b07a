import 'package:flutter/material.dart';
import '../services/measurement_storage_service.dart';
import '../widgets/banner_ad_widget.dart';
import 'field_measurements_page.dart';
import '../../generated/l10n/app_localizations.dart';
import 'distance_measurements_page.dart';
import 'marker_measurements_page.dart';

class CategoryManagerPage extends StatefulWidget {
  const CategoryManagerPage({Key? key}) : super(key: key);

  @override
  State<CategoryManagerPage> createState() => _CategoryManagerPageState();
}

class _CategoryManagerPageState extends State<CategoryManagerPage> {
  final MeasurementStorageService _storageService = MeasurementStorageService();
  Map<String, int> _categoryCounts = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeStorage();
  }

  Future<void> _initializeStorage() async {
    await _storageService.initialize();
    await _loadCategoryCounts();
  }

  Future<void> _loadCategoryCounts() async {
    setState(() => _isLoading = true);
    try {
      final measurements = await _storageService.getAllMeasurements();
      
      Map<String, int> counts = {
        'field': 0,
        'distance': 0,
        'marker': 0,
        'area': 0,
        'circular': 0,
        'freeform': 0,
      };

      for (var measurement in measurements) {
        String type = measurement.type.toLowerCase();
        counts[type] = (counts[type] ?? 0) + 1;
      }

      setState(() {
        _categoryCounts = counts;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('Error loading categories: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n?.savedMeasurements ?? 'Saved Measurements'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: _showStorageInfo,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadCategoryCounts,
          ),
        ],
      ),
      
      body: SafeArea(
        child: Column(
          children: [
          // Top Banner Ad
          const BannerAdWidget(
            margin: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            alwaysShow: true,
          ),
          // Category list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _buildCategoryList(),
          ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryList() {
    return ListView(
      padding: const EdgeInsets.symmetric(horizontal: 16,vertical: 16),
      children: [
        // Field Measurements
        _buildCategoryCard(
          title: 'Field Measurements',
          subtitle: 'Area calculations and field boundaries',
          icon: Icons.crop_free_rounded,
          color: Colors.green,
          count: _categoryCounts['field'] ?? 0,
          onTap: () => _navigateToCategory('field'),
        ),

        const SizedBox(height: 12),

        // Distance Measurements
        _buildCategoryCard(
          title: 'Distance Measurements',
          subtitle: 'Linear measurements and paths',
          icon: Icons.straighten_rounded,
          color: Colors.blue,
          count: _categoryCounts['distance'] ?? 0,
          onTap: () => _navigateToCategory('distance'),
        ),

        const SizedBox(height: 12),

        // Location Markers
        _buildCategoryCard(
          title: 'Location Markers',
          subtitle: 'Points of interest and landmarks',
          icon: Icons.place_rounded,
          color: Colors.orange,
          count: _categoryCounts['marker'] ?? 0,
          onTap: () => _navigateToCategory('marker'),
        ),

        const SizedBox(height: 12),

        // Area Measurements (Legacy)
        if ((_categoryCounts['area'] ?? 0) > 0)
          _buildCategoryCard(
            title: 'Area Measurements',
            subtitle: 'Legacy area calculations',
            icon: Icons.crop_square_rounded,
            color: Colors.deepPurple,
            count: _categoryCounts['area'] ?? 0,
            onTap: () => _navigateToCategory('area'),
          ),

        if ((_categoryCounts['area'] ?? 0) > 0) const SizedBox(height: 12),

        // Circular Measurements (Legacy)
        if ((_categoryCounts['circular'] ?? 0) > 0)
          _buildCategoryCard(
            title: 'Circular Measurements',
            subtitle: 'Legacy circular calculations',
            icon: Icons.circle_outlined,
            color: Colors.teal,
            count: _categoryCounts['circular'] ?? 0,
            onTap: () => _navigateToCategory('circular'),
          ),

        if ((_categoryCounts['circular'] ?? 0) > 0) const SizedBox(height: 12),

        // Freeform Measurements (Legacy)
        if ((_categoryCounts['freeform'] ?? 0) > 0)
          _buildCategoryCard(
            title: 'Freeform Measurements',
            subtitle: 'Legacy freeform calculations',
            icon: Icons.gesture_rounded,
            color: Colors.indigo,
            count: _categoryCounts['freeform'] ?? 0,
            onTap: () => _navigateToCategory('freeform'),
          ),
        
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildCategoryCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required MaterialColor color,
    required int count,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              colors: [color.withOpacity(0.05), color.withOpacity(0.1)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Row(
            children: [
              // Icon container
              Container(
                width: 64,
                height: 64,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: color.withOpacity(0.3)),
                ),
                child: Icon(
                  icon,
                  color: color.shade700,
                  size: 32,
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: color.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '$count measurements',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: color.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              // Arrow
              Icon(
                Icons.arrow_forward_ios_rounded,
                color: color.shade400,
                size: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToCategory(String category) {
    Widget page;
    
    switch (category) {
      case 'field':
        page = const FieldMeasurementsPage();
        break;
      case 'distance':
        page = const DistanceMeasurementsPage();
        break;
      case 'marker':
        page = const MarkerMeasurementsPage();
        break;
      case 'area':
      case 'circular':
      case 'freeform':
        // For legacy types, use a generic measurements page
        page = LegacyMeasurementsPage(category: category);
        break;
      default:
        _showErrorSnackBar('Unknown category: $category');
        return;
    }
    
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => page),
    );
  }

  void _showStorageInfo() async {
    try {
      final measurements = await _storageService.getAllMeasurements();
      final totalCount = measurements.length;

      // Calculate storage usage by category
      Map<String, int> detailedCounts = {};
      int totalSize = 0;

      for (var measurement in measurements) {
        String type = measurement.type.toLowerCase();
        detailedCounts[type] = (detailedCounts[type] ?? 0) + 1;
        // Estimate size (rough calculation)
        totalSize += measurement.toString().length;
      }

      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Row(
              children: [
                Icon(Icons.storage, color: Colors.blue.shade700),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'Storage Information',
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildStorageInfoRow('Total Measurements', '$totalCount'),
                  const SizedBox(height: 8),
                  _buildStorageInfoRow('Estimated Size', '${(totalSize / 1024).toStringAsFixed(1)} KB'),
                  const SizedBox(height: 16),
                  const Text(
                    'Breakdown by Category:',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  ...detailedCounts.entries.map((entry) =>
                    _buildStorageInfoRow(
                      '${entry.key.toUpperCase()} Measurements',
                      '${entry.value}'
                    )
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.info, color: Colors.blue.shade700, size: 20),
                        const SizedBox(width: 8),
                        const Expanded(
                          child: Text(
                            'Data is stored locally on your device and can be exported or backed up.',
                            style: TextStyle(fontSize: 12),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          );
        },
      );
    } catch (e) {
      _showErrorSnackBar('Error loading storage info: $e');
    }
  }

  Widget _buildStorageInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: const TextStyle(fontSize: 14),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            flex: 1,
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
              textAlign: TextAlign.end,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}

// Legacy measurements page for backward compatibility
class LegacyMeasurementsPage extends StatelessWidget {
  final String category;
  
  const LegacyMeasurementsPage({Key? key, required this.category}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.info_outline, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                'Legacy Measurements',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text(
                'These measurements are from an older version.\nUse the new measurement tools for better features.',
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
