import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../../domain/entities/location_entity.dart';
import '../../domain/entities/photo_entity.dart';

class MapPage extends StatefulWidget {
  final LocationEntity location;
  final List<PhotoEntity>? photos;

  const MapPage({
    Key? key, 
    required this.location,
    this.photos,
  }) : super(key: key);

  @override
  State<MapPage> createState() => _MapPageState();
}

class _MapPageState extends State<MapPage> {
  late GoogleMapController _controller;
  late LatLng _center;
  Set<Marker> _markers = {};

  @override
  void initState() {
    super.initState();
    _center = LatLng(widget.location.latitude, widget.location.longitude);
    
    // Create markers for all photos
    if (widget.photos != null && widget.photos!.isNotEmpty) {
      _createMarkersFromPhotos(widget.photos!);
    } else {
      // Create a single marker for the current location
      _markers = {
        Marker(
          markerId: const MarkerId('photo_location'),
          position: _center,
          infoWindow: InfoWindow(
            title: 'Photo Location',
            snippet: widget.location.address ?? 'No address available',
          ),
        ),
      };
    }
  }

  void _createMarkersFromPhotos(List<PhotoEntity> photos) {
    Set<Marker> markers = {};
    
    for (var photo in photos) {
      if (photo.location != null) {
        final marker = Marker(
          markerId: MarkerId(photo.id),
          position: LatLng(photo.location!.latitude, photo.location!.longitude),
          infoWindow: InfoWindow(
            title: photo.isVideo ? 'Video' : 'Photo',
            snippet: photo.address ?? 'No address available',
          ),
        );
        markers.add(marker);
      }
    }
    
    setState(() {
      _markers = markers;
    });
  }

  void _onMapCreated(GoogleMapController controller) {
    _controller = controller;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Photo Location'),
      ),
      body: GoogleMap(
        onMapCreated: _onMapCreated,
        initialCameraPosition: CameraPosition(
          target: _center,
          zoom: 15.0,
        ),
        markers: _markers,
        myLocationEnabled: true,
        myLocationButtonEnabled: true,
        zoomControlsEnabled: true,
      ),
    );
  }
}
