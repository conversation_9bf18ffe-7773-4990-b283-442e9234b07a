import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import 'dart:ui' as ui;
import 'dart:typed_data';
import '../services/measurement_storage_service.dart';
import '../widgets/banner_ad_widget.dart';
import '../providers/app_state_provider.dart';
import '../../generated/l10n/app_localizations.dart';

// Input methods for distance measurement
enum InputMethod { tap, walk, hybrid }

class DistanceMeasurementPage extends StatefulWidget {
  final LatLng initialLocation;
  final Function(Map<String, dynamic>) onDistanceSaved;

  const DistanceMeasurementPage({
    Key? key,
    required this.initialLocation,
    required this.onDistanceSaved,
  }) : super(key: key);

  @override
  State<DistanceMeasurementPage> createState() => _DistanceMeasurementPageState();
}

class _DistanceMeasurementPageState extends State<DistanceMeasurementPage> {
  GoogleMapController? _mapController;
  Set<Marker> _markers = {};
  Set<Polyline> _polylines = {};
  List<Position> _distancePoints = [];
  double _totalDistance = 0.0;
  List<double> _segmentDistances = [];

  // Map view options
  bool _showSatelliteView = false;

  // Input method selection
  InputMethod _inputMethod = InputMethod.tap;
  bool _isRecording = false;
  StreamSubscription<Position>? _positionStream;
  Position? _currentPosition;

  // Distance marker caching
  final Map<String, BitmapDescriptor> _distanceMarkerCache = {};

  @override
  void dispose() {
    _positionStream?.cancel();
    _distanceMarkerCache.clear(); // Clear distance marker cache
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _initializeLocation();
  }

  Future<void> _initializeLocation() async {
    try {
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.best,
      );
      
      if (_mapController != null) {
        _mapController!.animateCamera(
          CameraUpdate.newLatLngZoom(
            LatLng(position.latitude, position.longitude),
            16.0,
          ),
        );
      }
    } catch (e) {
      if (kDebugMode) print('Error getting location: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n?.distanceMeasurement ?? 'Distance Measurement'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          if (_distancePoints.length >= 2)
            IconButton(
              icon: const Icon(Icons.check_circle),
              onPressed: _completeDistance,
            ),
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: _clearDistance,
          ),
        ],
      ),
      
      body: Column(
        children: [
          // Top Banner Ad
          const BannerAdWidget(
            margin: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            alwaysShow: true,
          ),
          
          // Instructions
          if (_distancePoints.isEmpty)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              color: Colors.blue.shade50,
              child: Column(
                children: [
                  Icon(Icons.straighten, size: 32, color: Colors.blue.shade700),
                  const SizedBox(height: 8),
                  Text(
                    'Tap on the map to mark distance points',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade700,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Add at least 2 points to measure distance',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.blue.shade600,
                    ),
                  ),
                ],
              ),
            ),
          
          // Map
          Expanded(
            child: Stack(
              children: [
                GoogleMap(
                  onMapCreated: _onMapCreated,
                  onTap: _onMapTap,
                  initialCameraPosition: CameraPosition(
                    target: widget.initialLocation,
                    zoom: 16,
                  ),
                  mapType: _showSatelliteView ? MapType.satellite : MapType.normal,
                  markers: _markers,
                  polylines: _polylines,
                  myLocationEnabled: true,
                  myLocationButtonEnabled: false,
                  mapToolbarEnabled: false,
                ),
                
                // Distance info overlay
                if (_distancePoints.isNotEmpty)
                  Positioned(
                    bottom: 1,
                    left: 16,
                    right: 50,
                    child: _buildDistanceInfoCard(),
                  ),

                // Floating method selector
                Positioned(
                  top: 16,
                  left: 16,
                  child: _buildFloatingMethodSelector(),
                ),

                // Satellite view toggle
                Positioned(
                  top: 16,
                  right: 16,
                  child: _buildSatelliteToggle(),
                ),

                // GPS recording status (floating)
                if (_isRecording)
                  Positioned(
                    bottom: 100,
                    left: 16,
                    right: 16,
                    child: _buildGPSRecordingStatus(),
                  ),


              ],
            ),
          ),

          // Bottom controls
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    _distancePoints.isEmpty
                        ? 'Start marking distance points'
                        : '${_distancePoints.length} points marked',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                if (_distancePoints.length >= 2) ...[
                  ElevatedButton.icon(
                    onPressed: _completeDistance,
                    icon: const Icon(Icons.save),
                    label: const Text('Save Distance'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDistanceInfoCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.straighten, color: Colors.blue.shade700),
                const SizedBox(width: 8),
                const Text(
                  'Distance Measurement',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Consumer<AppProvider>(
              builder: (context, appProvider, child) {
                final l10n = AppLocalizations.of(context);
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('${l10n?.points ?? 'Points'}: ${_distancePoints.length}'),
                    if (_totalDistance > 0)
                      Text('${l10n?.totalDistance ?? 'Total Distance'}: ${appProvider.formatDistance(_totalDistance)}'),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }



  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
  }

  void _onMapTap(LatLng latLng) {
    if (kDebugMode) {
      print('Map tapped at: ${latLng.latitude}, ${latLng.longitude}');
      print('Current points: ${_distancePoints.length}');
    }

    if (_inputMethod == InputMethod.tap || _inputMethod == InputMethod.hybrid) {
      _addDistancePoint(latLng);
    }
  }

  void _addDistancePoint(LatLng latLng) {
    // Allow placing points close together - removal only happens when tapping directly on markers
    if (kDebugMode) {
      print('Adding distance point at: ${latLng.latitude}, ${latLng.longitude}');
      print('Points before adding: ${_distancePoints.length}');
    }

    // If not removing a point, add a new one
    final position = Position(
      latitude: latLng.latitude,
      longitude: latLng.longitude,
      timestamp: DateTime.now(),
      accuracy: 0.5, // Set to 0.5 meter accuracy for practical precision
      altitude: 0.0,
      heading: 0.0,
      speed: 0.0,
      speedAccuracy: 0.0,
      altitudeAccuracy: 0.0,
      headingAccuracy: 0.0,
    );

    setState(() {
      _distancePoints.add(position);
    });

    if (kDebugMode) {
      print('Points after adding: ${_distancePoints.length}');
    }

    _calculateDistances();
    _updateDistanceVisualization();
  }

  void _updateDistanceVisualization() {
    // Update markers
    Set<Marker> markers = {};
    for (int i = 0; i < _distancePoints.length; i++) {
      final point = _distancePoints[i];

      // Calculate distance from previous point
      String distanceInfo = '';
      if (i > 0 && _segmentDistances.isNotEmpty && i - 1 < _segmentDistances.length) {
        double segmentDistance = _segmentDistances[i - 1];
        final appProvider = Provider.of<AppProvider>(context, listen: false);
        distanceInfo = '\nDistance from Point $i: ${appProvider.formatDistance(segmentDistance)}';
      }

      markers.add(
        Marker(
          markerId: MarkerId('distance_point_$i'),
          position: LatLng(point.latitude, point.longitude),
          infoWindow: InfoWindow(
            title: 'Point ${i + 1}',
            snippet: 'Tap to remove this point$distanceInfo',
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(
            i == 0 ? BitmapDescriptor.hueGreen : BitmapDescriptor.hueBlue,
          ),
          onTap: () => _removeDistancePoint(i),
        ),
      );
    }

    // Add distance labels directly on the lines between points
    for (int i = 0; i < _distancePoints.length - 1; i++) {
      final point1 = _distancePoints[i];
      final point2 = _distancePoints[i + 1];

      // Calculate midpoint between two consecutive points
      double midLat = (point1.latitude + point2.latitude) / 2;
      double midLng = (point1.longitude + point2.longitude) / 2;

      // Add custom text distance marker on the line
      if (_segmentDistances.isNotEmpty && i < _segmentDistances.length) {
        final appProvider = Provider.of<AppProvider>(context, listen: false);
        String distanceText = appProvider.formatDistance(_segmentDistances[i]);

        _getCachedDistanceMarker(distanceText).then((icon) {
          setState(() {
            _markers.add(
              Marker(
                markerId: MarkerId('distance_line_label_$i'),
                position: LatLng(midLat, midLng),
                infoWindow: InfoWindow.noText,
                icon: icon,
                anchor: const Offset(0.5, 0.5), // Center the marker on the line
                consumeTapEvents: false, // Don't consume tap events
              ),
            );
          });
        });
      }
    }

    // Update polyline if we have at least 2 points
    Set<Polyline> polylines = {};
    if (_distancePoints.length >= 2) {
      List<LatLng> polylinePoints = _distancePoints
          .map((p) => LatLng(p.latitude, p.longitude))
          .toList();

      polylines.add(
        Polyline(
          polylineId: const PolylineId('distance_line'),
          points: polylinePoints,
          color: Colors.blue,
          width: 3,
          geodesic: true,
        ),
      );
    }

    setState(() {
      _markers = markers;
      _polylines = polylines;
    });
  }

  void _calculateDistances() {
    if (_distancePoints.length < 2) {
      setState(() {
        _totalDistance = 0.0;
        _segmentDistances.clear();
      });
      return;
    }

    try {
      double totalDistance = 0.0;
      List<double> segmentDistances = [];

      for (int i = 0; i < _distancePoints.length - 1; i++) {
        double segmentDistance = Geolocator.distanceBetween(
          _distancePoints[i].latitude,
          _distancePoints[i].longitude,
          _distancePoints[i + 1].latitude,
          _distancePoints[i + 1].longitude,
        );

        segmentDistances.add(segmentDistance);
        totalDistance += segmentDistance;
      }

      setState(() {
        _totalDistance = totalDistance;
        _segmentDistances = segmentDistances;
      });
    } catch (e) {
      if (kDebugMode) print('Error calculating distances: $e');
    }
  }

  void _completeDistance() {
    if (_distancePoints.length < 2) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please add at least 2 points to measure distance'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    _showSaveDistanceDialog();
  }

  void _showSaveDistanceDialog() {
    final nameController = TextEditingController();
    final notesController = TextEditingController();

    // Generate default name
    final now = DateTime.now();
    nameController.text = 'Distance ${now.day}/${now.month}/${now.year} ${now.hour}:${now.minute.toString().padLeft(2, '0')}';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Save Distance Measurement'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Distance summary
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Distance Summary',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade700,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Consumer<AppProvider>(
                      builder: (context, appProvider, child) {
                        final l10n = AppLocalizations.of(context);
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('${l10n?.points ?? 'Points'}: ${_distancePoints.length}'),
                            Text('${l10n?.totalDistance ?? 'Total Distance'}: ${appProvider.formatDistance(_totalDistance)}'),
                            Text('${l10n?.segments ?? 'Segments'}: ${_segmentDistances.length}'),
                          ],
                        );
                      },
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'Distance Name',
                  border: OutlineInputBorder(),
                ),
                maxLength: 50,
              ),

              const SizedBox(height: 16),

              TextField(
                controller: notesController,
                decoration: const InputDecoration(
                  labelText: 'Notes (Optional)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
                maxLength: 200,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (nameController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please enter a distance name'),
                    backgroundColor: Colors.orange,
                  ),
                );
                return;
              }

              await _saveDistance(nameController.text.trim(), notesController.text.trim());
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Close page
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
            child: const Text('Save Distance'),
          ),
        ],
      ),
    );
  }

  Future<void> _saveDistance(String name, String notes) async {
    try {
      final storageService = MeasurementStorageService();
      await storageService.initialize();

      final distanceData = MeasurementRecord(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: name,
        type: 'distance',
        points: _distancePoints.map((point) => {
          'latitude': point.latitude,
          'longitude': point.longitude,
        }).toList(),
        area: 0.0,
        distance: _totalDistance,
        perimeter: 0.0,
        units: 'metric',
        timestamp: DateTime.now(),
        metadata: {
          'app_version': '1.0',
          'measurement_type': 'distance',
          'notes': notes,
          'point_count': _distancePoints.length,
          'segment_distances': _segmentDistances,
        },
      );

      await storageService.saveMeasurement(distanceData);

      widget.onDistanceSaved({
        'name': name,
        'distance': _totalDistance,
        'points': _distancePoints.length,
        'segments': _segmentDistances.length,
      });

      _showSaveSuccessDialog();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error saving distance: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _clearDistance() {
    setState(() {
      _distancePoints.clear();
      _markers.clear();
      _polylines.clear();
      _totalDistance = 0.0;
      _segmentDistances.clear();
      _distanceMarkerCache.clear(); // Clear distance marker cache
    });
  }

  // Removed old formatting method - now using AppProvider.formatDistance()

  // Get cached distance marker to avoid recreating identical markers
  Future<BitmapDescriptor> _getCachedDistanceMarker(String distance) async {
    String cacheKey = distance;

    if (_distanceMarkerCache.containsKey(cacheKey)) {
      return _distanceMarkerCache[cacheKey]!;
    }

    BitmapDescriptor marker = await _createDistanceTextMarker(distance);
    _distanceMarkerCache[cacheKey] = marker;
    return marker;
  }

  Widget _buildFloatingMethodSelector() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildCompactMethodButton(InputMethod.tap, Icons.touch_app, 'Tap'),
          const SizedBox(height: 4),
          _buildCompactMethodButton(InputMethod.walk, Icons.directions_walk, 'Walk'),
          const SizedBox(height: 4),
          _buildCompactMethodButton(InputMethod.hybrid, Icons.merge_type, 'Hybrid'),

          // GPS control for walk/hybrid modes
          if (_inputMethod == InputMethod.walk || _inputMethod == InputMethod.hybrid) ...[
            const SizedBox(height: 8),
            Container(
              width: 80,
              height: 1,
              color: Colors.grey.shade300,
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: 80,
              height: 32,
              child: ElevatedButton(
                onPressed: _isRecording ? _stopRecording : _startRecording,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _isRecording ? Colors.red : Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                  minimumSize: const Size(80, 32),
                  maximumSize: const Size(80, 32),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      _isRecording ? Icons.stop : Icons.play_arrow,
                      size: 14,
                    ),
                    const SizedBox(width: 2),
                    Text(
                      _isRecording ? 'Stop' : 'GPS',
                      style: const TextStyle(fontSize: 10),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCompactMethodButton(InputMethod method, IconData icon, String label) {
    bool isSelected = _inputMethod == method;
    Color color = method == InputMethod.tap
        ? Colors.blue
        : method == InputMethod.walk
            ? Colors.green
            : Colors.orange;

    return GestureDetector(
      onTap: () {
        setState(() {
          _inputMethod = method;
          if (_isRecording && method == InputMethod.tap) {
            _stopRecording();
          }
        });
      },
      child: Container(
        width: 80,
        height: 32,
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
        decoration: BoxDecoration(
          color: isSelected ? color : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: isSelected ? color : Colors.grey.shade300,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.white : Colors.grey.shade600,
              size: 16,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.grey.shade600,
                fontSize: 11,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Create custom distance text marker that's always visible
  Future<BitmapDescriptor> _createDistanceTextMarker(String distance) async {
    try {
      final ui.PictureRecorder pictureRecorder = ui.PictureRecorder();
      final Canvas canvas = Canvas(pictureRecorder);

      // Optimized marker dimensions for larger font
      const double width = 200.0;
      const double height = 70.0;
      const double radius = 12.0;

      // Enhanced colors for better visibility - using blue theme for distance measurement
      Color backgroundColor = Colors.blue.shade600;
      Color borderColor = Colors.blue.shade800;
      Color textColor = Colors.white;

      // Draw outer border
      final RRect outerRect = RRect.fromRectAndRadius(
        const Rect.fromLTWH(0, 0, width, height),
        const Radius.circular(radius),
      );

      final Paint borderPaint = Paint()
        ..color = borderColor
        ..style = PaintingStyle.fill;
      canvas.drawRRect(outerRect, borderPaint);

      // Draw inner background with 2px padding
      final RRect innerRect = RRect.fromRectAndRadius(
        const Rect.fromLTWH(2, 2, width - 4, height - 4),
        const Radius.circular(radius - 2),
      );

      final Paint backgroundPaint = Paint()
        ..color = backgroundColor
        ..style = PaintingStyle.fill;
      canvas.drawRRect(innerRect, backgroundPaint);

      // Draw distance text with enhanced visibility
      final textPainter = TextPainter(
        text: TextSpan(
          text: distance,
          style: TextStyle(
            color: textColor,
            fontSize: 32, // Large font for better visibility
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                color: Colors.black.withOpacity(0.8),
                offset: const Offset(1, 1),
                blurRadius: 3,
              ),
              Shadow(
                color: Colors.black.withOpacity(0.4),
                offset: const Offset(0.5, 0.5),
                blurRadius: 1,
              ),
            ],
          ),
        ),
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();
      final double textX = (width - textPainter.width) / 2;
      final double textY = (height - textPainter.height) / 2;
      textPainter.paint(canvas, Offset(textX, textY));

      // Convert to image
      final ui.Picture picture = pictureRecorder.endRecording();
      final ui.Image image = await picture.toImage(width.toInt(), height.toInt());
      final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final Uint8List uint8List = byteData!.buffer.asUint8List();

      return BitmapDescriptor.fromBytes(uint8List);
    } catch (e) {
      if (kDebugMode) print('Error creating distance text marker: $e');
      // Fallback to default marker
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue);
    }
  }

  Widget _buildSatelliteToggle() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () {
            setState(() {
              _showSatelliteView = !_showSatelliteView;
            });
          },
          child: Container(
            padding: const EdgeInsets.all(12),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _showSatelliteView ? Icons.map : Icons.satellite_alt,
                  color: _showSatelliteView ? Colors.green : Colors.blue,
                  size: 24,
                ),
                const SizedBox(height: 4),
                Text(
                  _showSatelliteView ? 'Normal' : 'Satellite',
                  style: TextStyle(
                    color: _showSatelliteView ? Colors.green : Colors.blue,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }





  Widget _buildGPSRecordingStatus() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.green.shade700,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: const BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          const Text(
            'Recording GPS points...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '${_distancePoints.length} points',
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 11,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _startRecording() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Location permission is required for GPS recording'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }
      }

      setState(() {
        _isRecording = true;
      });

      const LocationSettings locationSettings = LocationSettings(
        accuracy: LocationAccuracy.best,
        distanceFilter: 0, // Record every position change for maximum precision
        timeLimit: Duration(seconds: 30), // Optimal time for 0.5m precision
      );

      _positionStream = Geolocator.getPositionStream(locationSettings: locationSettings).listen(
        (Position position) {
          if (_isRecording) {
            _addDistancePointFromGPS(position);
          }
        },
        onError: (error) {
          if (kDebugMode) print('GPS recording error: $error');
          _stopRecording();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('GPS recording error: $error'),
              backgroundColor: Colors.red,
            ),
          );
        },
      );

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('GPS recording started'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error starting GPS recording: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _stopRecording() {
    setState(() {
      _isRecording = false;
    });
    _positionStream?.cancel();
    _positionStream = null;

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('GPS recording stopped'),
        backgroundColor: Colors.orange,
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _addDistancePointFromGPS(Position position) {
    setState(() {
      _distancePoints.add(position);
    });

    _calculateDistances();
    _updateDistanceVisualization();
  }

  void _removeDistancePoint(int index) {
    if (kDebugMode) {
      print('Removing distance point at index: $index');
      print('Points before removal: ${_distancePoints.length}');
    }

    setState(() {
      _distancePoints.removeAt(index);
    });

    if (kDebugMode) {
      print('Points after removal: ${_distancePoints.length}');
    }

    _calculateDistances();
    _updateDistanceVisualization();
  }

  void _showSaveSuccessDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.check_circle,
                color: Colors.blue.shade700,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'Distance Saved Successfully!',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Your distance measurement has been saved to your device.',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.storage, color: Colors.green.shade700, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Saved Location',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '📱 Local Device Storage',
                    style: TextStyle(color: Colors.green.shade600),
                  ),
                  Text(
                    '💾 Accessible from "Saved Data" menu',
                    style: TextStyle(color: Colors.green.shade600),
                  ),
                  Text(
                    '📤 Export as PDF, JSON, or CSV',
                    style: TextStyle(color: Colors.green.shade600),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Close page
            },
            child: const Text('Continue'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Close page
              // Navigate to saved measurements
            },
            icon: const Icon(Icons.folder_rounded),
            label: const Text('View Saved Data'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}
