import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:provider/provider.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:async';
import 'dart:math';
import 'dart:ui' as ui;
import 'dart:typed_data';
import '../providers/app_state_provider.dart';
import '../widgets/banner_ad_widget.dart';
import '../widgets/tutorial_highlight_widget.dart';
import '../../domain/entities/photo_entity.dart';
import '../services/location_sharing_service.dart';
import '../services/measurement_storage_service.dart';
import '../services/measurement_export_service.dart';
import '../../data/services/tutorial_service.dart';
import 'category_manager_page.dart';
import 'field_measurement_page.dart';
import 'distance_measurement_page.dart';
import 'marker_creation_page.dart';
import '../../generated/l10n/app_localizations.dart';

// Measurement modes for interactive map
enum MeasurementMode { area, distance }

// Input methods for measurement
enum InputMethod { gpsWalking, mapTapping, hybrid }

class MapViewPage extends StatefulWidget {
  const MapViewPage({Key? key}) : super(key: key);

  @override
  State<MapViewPage> createState() => _MapViewPageState();
}

class _MapViewPageState extends State<MapViewPage> {
  GoogleMapController? _mapController;
  Set<Marker> _markers = {};
  final Set<Polygon> _polygons = {};
  final Set<Polyline> _polylines = {};
  LatLng _center = const LatLng(13.0827, 80.2707); // Chennai default
  bool _isMapReady = false;

  // Interactive measurement features
  List<Position> _measurementPoints = [];
  MeasurementMode _currentMode = MeasurementMode.area;
  InputMethod _inputMethod = InputMethod.hybrid;
  bool _showInstructions = true;
  bool _showSatelliteView = false;
  bool _isMeasurementMode = false;
  bool _showDistanceLabels = true;
  bool _showFloatingOptions = false;

  // GPS Walking functionality
  bool _isRecording = false;
  bool _isLocationEnabled = false;
  Position? _currentPosition;
  StreamSubscription<Position>? _positionStream;

  // Measurement results
  double _totalArea = 0.0;
  double _perimeter = 0.0;
  double _totalDistance = 0.0;

  // History and interaction
  List<List<Position>> _measurementHistory = [];
  int _currentHistoryIndex = -1;
  Position? _startPoint;
  bool _isPolygonClosed = false;
  String? _selectedMarkerId;

  // Performance optimization
  Timer? _updateTimer;

  // Cache for custom markers to avoid recreating them
  final Map<String, BitmapDescriptor> _markerCache = {};
  final Map<String, BitmapDescriptor> _distanceMarkerCache = {};

  // Tutorial state
  final TutorialService _tutorialService = TutorialService();
  bool _showMapTutorial = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeMap();
      _initializeLocation();
      _checkAndShowTutorial();
    });
  }

  @override
  void dispose() {
    _updateTimer?.cancel();
    _positionStream?.cancel();
    // Clear marker caches
    _markerCache.clear();
    _distanceMarkerCache.clear();
    super.dispose();
  }

  Future<void> _checkAndShowTutorial() async {
    final shouldShow = await _tutorialService.shouldShowMapTutorial();
    if (shouldShow && mounted) {
      // Show tutorial after a short delay to ensure UI is ready
      Future.delayed(const Duration(milliseconds: 1500), () {
        if (mounted) {
          setState(() {
            _showMapTutorial = true;
          });
          _tutorialService.showMapTutorial(context);
        }
      });
    }
  }

  void _initializeMap() {
    final appProvider = Provider.of<AppProvider>(context, listen: false);

    // Set initial location to current location if available
    if (appProvider.currentLocation != null) {
      setState(() {
        _center = LatLng(
          appProvider.currentLocation!.latitude,
          appProvider.currentLocation!.longitude,
        );
      });
    }

    // Add markers for all photos with location
    _createMarkersFromPhotos(appProvider.photos);
  }

  // Initialize location services for GPS walking
  Future<void> _initializeLocation() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        if (kDebugMode) print('Location services are disabled.');
        return;
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          if (kDebugMode) print('Location permissions are denied');
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        if (kDebugMode) print('Location permissions are permanently denied');
        return;
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      setState(() {
        _currentPosition = position;
        _isLocationEnabled = true;
        _center = LatLng(position.latitude, position.longitude);
      });

      // Update map camera to current location
      if (_mapController != null) {
        _mapController!.animateCamera(
          CameraUpdate.newLatLngZoom(
            LatLng(position.latitude, position.longitude),
            16.0,
          ),
        );
      }

      if (kDebugMode) print('Location initialized: ${position.latitude}, ${position.longitude}');
    } catch (e) {
      if (kDebugMode) print('Error initializing location: $e');
    }
  }

  void _createMarkersFromPhotos(List<PhotoEntity> photos) {
    Set<Marker> markers = {};
    
    for (var photo in photos) {
      if (photo.location != null) {
        final marker = Marker(
          markerId: MarkerId(photo.id),
          position: LatLng(photo.location!.latitude, photo.location!.longitude),
          infoWindow: InfoWindow(
            title: photo.address ?? 'Photo',
            snippet: 'Taken on ${_formatDate(photo.timestamp)}',
            onTap: () {
              _showPhotoDetails(photo);
            },
          ),
        );
        markers.add(marker);
      }
    }
    
    setState(() {
      _markers = markers;
    });
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
    setState(() {
      _isMapReady = true;
    });
  }

  void _showPhotoDetails(PhotoEntity photo) {
    Navigator.pushNamed(context, '/photo_detail', arguments: photo);
  }

  String _formatDate(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  // Removed old formatting methods - now using AppProvider.formatArea() and AppProvider.formatDistance()

  @override
  Widget build(BuildContext context) {
    final appProvider = Provider.of<AppProvider>(context);
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      body: SafeArea(
        child: Column(
        children: [
          // Top Banner Ad
          const BannerAdWidget(
            margin: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            alwaysShow: true,
          ),
          
          // Enhanced interactive map with measurement features
          Expanded(
            child: Stack(
              children: [
                TutorialHighlightWidget(
                  showTutorial: _showMapTutorial,
                  tutorialMessage: "Pinch & drag",
                  highlightColor: Colors.blue,
                  position: TutorialPosition.top,
                  onTutorialTap: () {
                    setState(() {
                      _showMapTutorial = false;
                    });
                  },
                  child: GoogleMap(
                    onMapCreated: _onMapCreated,
                    onTap: _onMapTap,
                    initialCameraPosition: CameraPosition(
                      target: _center,
                      zoom: 12,
                    ),
                    mapType: _showSatelliteView ? MapType.satellite : MapType.normal,
                    markers: _markers,
                    polygons: _polygons,
                    polylines: _polylines,
                    myLocationEnabled: true,
                    myLocationButtonEnabled: false,
                    mapToolbarEnabled: false,
                    zoomControlsEnabled: false,
                  ),
                ),
                
                // Measurement mode controls
                if (_isMeasurementMode) _buildMeasurementControls(),
                
                // Instructions overlay
                if (_isMeasurementMode && _showInstructions && _measurementPoints.isEmpty)
                  _buildInstructionsOverlay(),
                
                // Measurement results
                if (_isMeasurementMode && _measurementPoints.isNotEmpty)
                  _buildResultsCard(),
                
                // Bottom controls
                if (_isMeasurementMode) _buildBottomControls(),
              ],
            ),
          ),
        ],
        ),
      ),

      // Animated Floating Action Button with Options (Tutorial disabled)
      floatingActionButton: _buildAnimatedFloatingActionButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }

  // Interactive measurement methods
  void _onMapTap(LatLng latLng) {
    if (_isMeasurementMode && (_inputMethod == InputMethod.mapTapping || _inputMethod == InputMethod.hybrid)) {
      _handleMeasurementTap(latLng);
    }
  }

  void _handleMeasurementTap(LatLng latLng) {
    // Check if tapping near start point to close polygon
    if (_currentMode == MeasurementMode.area && _measurementPoints.length >= 3) {
      if (_isNearStartPoint(latLng)) {
        _closePolygon();
        return;
      }
    }

    // Add new measurement point from map tap
    _addMeasurementPoint(latLng);

    // Hide instructions after first point
    if (_showInstructions && _measurementPoints.length >= 1) {
      setState(() {
        _showInstructions = false;
      });
    }
  }

  bool _isNearStartPoint(LatLng latLng) {
    if (_measurementPoints.isEmpty) return false;

    double distance = _vincentyDistance(
      latLng.latitude,
      latLng.longitude,
      _measurementPoints.first.latitude,
      _measurementPoints.first.longitude,
    );

    return distance <= 10.0; // Within 10 meters
  }

  void _addMeasurementPoint(LatLng latLng) {
    Position mapPosition = Position(
      latitude: latLng.latitude,
      longitude: latLng.longitude,
      timestamp: DateTime.now(),
      accuracy: 0.5, // Set to 0.5 meter accuracy for practical precision
      altitude: 0.0,
      heading: 0.0,
      speed: 0.0,
      speedAccuracy: 0.0,
      altitudeAccuracy: 0.0,
      headingAccuracy: 0.0,
    );

    // Save current state for undo functionality
    _saveToHistory();

    setState(() {
      _measurementPoints.add(mapPosition);

      // Set start point for polygon closure detection
      if (_measurementPoints.length == 1) {
        _startPoint = mapPosition;
      }

      // Reset polygon closed state when adding new points
      _isPolygonClosed = false;
    });

    _calculateMeasurements();
  }

  void _closePolygon() {
    if (_currentMode == MeasurementMode.area && _measurementPoints.length >= 3) {
      setState(() {
        _isPolygonClosed = true;
      });

      _calculateMeasurements();

      final appProvider = Provider.of<AppProvider>(context, listen: false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Polygon closed! Area: ${appProvider.formatArea(_totalArea)}'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  // History management
  void _saveToHistory() {
    if (_currentHistoryIndex < _measurementHistory.length - 1) {
      _measurementHistory = _measurementHistory.sublist(0, _currentHistoryIndex + 1);
    }

    _measurementHistory.add(List.from(_measurementPoints));
    _currentHistoryIndex++;

    if (_measurementHistory.length > 50) {
      _measurementHistory.removeAt(0);
      _currentHistoryIndex--;
    }
  }

  void _undo() {
    if (_currentHistoryIndex > 0) {
      _currentHistoryIndex--;
      setState(() {
        _measurementPoints = List.from(_measurementHistory[_currentHistoryIndex]);
        _isPolygonClosed = false;
        _selectedMarkerId = null;

        if (_measurementPoints.isNotEmpty) {
          _startPoint = _measurementPoints.first;
        } else {
          _startPoint = null;
          _showInstructions = true;
        }
      });

      // Clear all measurement markers before recalculating
      _clearMeasurementMarkers();
      _calculateMeasurements();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Action undone'),
          duration: Duration(seconds: 1),
        ),
      );
    }
  }

  bool get _canUndo => _currentHistoryIndex > 0;

  // Measurement calculation methods
  void _calculateMeasurements() {
    _updateTimer?.cancel();

    _updateTimer = Timer(const Duration(milliseconds: 200), () {
      if (mounted) {
        if (_currentMode == MeasurementMode.area) {
          _calculateArea();
        } else if (_currentMode == MeasurementMode.distance) {
          _calculateDistance();
        }
        _updateMapOverlays();
      }
    });
  }

  void _calculateArea() {
    if (_measurementPoints.length < 3) {
      setState(() {
        _totalArea = 0.0;
        _perimeter = 0.0;
        _totalDistance = 0.0;
      });
      return;
    }

    try {
      double area = _calculateGeodeticArea(_measurementPoints);
      double perimeter = _calculateGeodeticPerimeter(_measurementPoints);

      setState(() {
        _totalArea = area;
        _perimeter = perimeter;
        _totalDistance = perimeter;
      });
    } catch (e) {
      if (kDebugMode) print('Error calculating area: $e');
      setState(() {
        _totalArea = 0.0;
        _perimeter = 0.0;
        _totalDistance = 0.0;
      });
    }
  }

  void _calculateDistance() {
    if (_measurementPoints.length < 2) {
      setState(() {
        _totalDistance = 0.0;
      });
      return;
    }

    try {
      double distance = 0.0;
      for (int i = 0; i < _measurementPoints.length - 1; i++) {
        double segmentDistance = _vincentyDistance(
          _measurementPoints[i].latitude,
          _measurementPoints[i].longitude,
          _measurementPoints[i + 1].latitude,
          _measurementPoints[i + 1].longitude,
        );

        if (!segmentDistance.isNaN && !segmentDistance.isInfinite && segmentDistance >= 0) {
          distance += segmentDistance;
        }
      }

      setState(() {
        _totalDistance = distance;
      });
    } catch (e) {
      if (kDebugMode) print('Error calculating distance: $e');
      setState(() {
        _totalDistance = 0.0;
      });
    }
  }

  // High-precision Vincenty's formula for distance calculation
  double _vincentyDistance(double lat1, double lon1, double lat2, double lon2) {
    const double a = 6378137.0; // WGS84 semi-major axis
    const double b = 6356752.314245; // WGS84 semi-minor axis
    const double f = 1 / 298.257223563; // WGS84 flattening

    double L = (lon2 - lon1) * pi / 180;
    double U1 = atan((1 - f) * tan(lat1 * pi / 180));
    double U2 = atan((1 - f) * tan(lat2 * pi / 180));
    double sinU1 = sin(U1), cosU1 = cos(U1);
    double sinU2 = sin(U2), cosU2 = cos(U2);

    double lambda = L, lambdaP;
    int iterLimit = 100;
    double cosSqAlpha, sinSigma, cos2SigmaM, cosSigma, sigma;

    do {
      double sinLambda = sin(lambda), cosLambda = cos(lambda);
      sinSigma = sqrt((cosU2 * sinLambda) * (cosU2 * sinLambda) +
          (cosU1 * sinU2 - sinU1 * cosU2 * cosLambda) *
              (cosU1 * sinU2 - sinU1 * cosU2 * cosLambda));

      if (sinSigma == 0) return 0; // Co-incident points

      cosSigma = sinU1 * sinU2 + cosU1 * cosU2 * cosLambda;
      sigma = atan2(sinSigma, cosSigma);
      double sinAlpha = cosU1 * cosU2 * sinLambda / sinSigma;
      cosSqAlpha = 1 - sinAlpha * sinAlpha;
      cos2SigmaM = cosSigma - 2 * sinU1 * sinU2 / cosSqAlpha;

      if (cos2SigmaM.isNaN) cos2SigmaM = 0; // Equatorial line

      double C = f / 16 * cosSqAlpha * (4 + f * (4 - 3 * cosSqAlpha));
      lambdaP = lambda;
      lambda = L + (1 - C) * f * sinAlpha *
          (sigma + C * sinSigma * (cos2SigmaM + C * cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM)));
    } while ((lambda - lambdaP).abs() > 1e-12 && --iterLimit > 0);

    if (iterLimit == 0) return 0; // Formula failed to converge

    double uSq = cosSqAlpha * (a * a - b * b) / (b * b);
    double A = 1 + uSq / 16384 * (4096 + uSq * (-768 + uSq * (320 - 175 * uSq)));
    double B = uSq / 1024 * (256 + uSq * (-128 + uSq * (74 - 47 * uSq)));
    double deltaSigma = B * sinSigma * (cos2SigmaM + B / 4 * (cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM) -
        B / 6 * cos2SigmaM * (-3 + 4 * sinSigma * sinSigma) * (-3 + 4 * cos2SigmaM * cos2SigmaM)));

    return b * A * (sigma - deltaSigma);
  }

  // Enhanced geodetic area calculation
  double _calculateGeodeticArea(List<Position> points) {
    if (points.length < 3) return 0.0;

    try {
      const double earthRadius = 6378137.0; // WGS84 Earth radius in meters
      double area = 0.0;

      List<double> lats = [];
      List<double> lngs = [];

      for (var point in points) {
        if (point.latitude.abs() <= 90 && point.longitude.abs() <= 180) {
          lats.add(point.latitude * pi / 180);
          lngs.add(point.longitude * pi / 180);
        } else {
          return 0.0;
        }
      }

      lats.add(lats.first);
      lngs.add(lngs.first);

      for (int i = 0; i < lats.length - 1; i++) {
        area += (lngs[i + 1] - lngs[i]) * (2 + sin(lats[i]) + sin(lats[i + 1]));
      }

      area = area.abs() * earthRadius * earthRadius / 2;

      if (area.isNaN || area.isInfinite) {
        return 0.0;
      }

      return area;
    } catch (e) {
      if (kDebugMode) print('Error in geodetic area calculation: $e');
      return 0.0;
    }
  }

  double _calculateGeodeticPerimeter(List<Position> points) {
    if (points.length < 2) return 0.0;

    try {
      double perimeter = 0.0;
      for (int i = 0; i < points.length; i++) {
        int nextIndex = (i + 1) % points.length;
        double segmentDistance = _vincentyDistance(
          points[i].latitude,
          points[i].longitude,
          points[nextIndex].latitude,
          points[nextIndex].longitude,
        );

        if (!segmentDistance.isNaN && !segmentDistance.isInfinite && segmentDistance >= 0) {
          perimeter += segmentDistance;
        }
      }

      if (perimeter.isNaN || perimeter.isInfinite || perimeter < 0) {
        return 0.0;
      }

      return perimeter;
    } catch (e) {
      if (kDebugMode) print('Error calculating perimeter: $e');
      return 0.0;
    }
  }

  // Toggle measurement mode
  void _toggleMeasurementMode() {
    setState(() {
      _isMeasurementMode = !_isMeasurementMode;
      if (!_isMeasurementMode) {
        _clearMeasurements();
        if (_isRecording) {
          _stopRecording();
        }
      } else {
        _showInstructions = true;
        if (!_isLocationEnabled) {
          _initializeLocation();
        }
      }
    });
    _updateMapOverlays();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(_isMeasurementMode ? 'Measurement mode enabled' : 'Measurement mode disabled'),
        backgroundColor: _isMeasurementMode ? Colors.green : Colors.orange,
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _clearMeasurements() {
    setState(() {
      _measurementPoints.clear();
      _totalArea = 0.0;
      _perimeter = 0.0;
      _totalDistance = 0.0;
      _isPolygonClosed = false;
      _startPoint = null;
      _selectedMarkerId = null;
      _showInstructions = true;

      // Clear measurement history
      _measurementHistory.clear();
      _currentHistoryIndex = -1;
    });

    // Clear all measurement markers and overlays
    _clearMeasurementMarkers();
    _updateMapOverlays();
  }

  // GPS Walking functionality
  void _startRecording() {
    if (!_isLocationEnabled) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Location services not available'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isRecording = true;
    });

    const LocationSettings locationSettings = LocationSettings(
      accuracy: LocationAccuracy.best,
      distanceFilter: 0, // Update every position change for maximum precision
      timeLimit: Duration(seconds: 30), // Optimal time for 0.5m precision
    );

    _positionStream = Geolocator.getPositionStream(locationSettings: locationSettings).listen(
      (Position position) {
        if (_isRecording) {
          _addMeasurementPoint(LatLng(position.latitude, position.longitude));
        }
      },
      onError: (error) {
        if (kDebugMode) print('GPS tracking error: $error');
        _stopRecording();
      },
    );

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('GPS recording started'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _stopRecording() {
    setState(() {
      _isRecording = false;
    });

    _positionStream?.cancel();
    _positionStream = null;

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('GPS recording stopped'),
        backgroundColor: Colors.orange,
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _addPoint() {
    if (_currentPosition != null) {
      _addMeasurementPoint(LatLng(_currentPosition!.latitude, _currentPosition!.longitude));
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Current location not available'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Update map overlays
  void _updateMapOverlays() {
    _updateMeasurementMarkers();
    _updatePolygonsAndPolylines();
  }

  void _updateMeasurementMarkers() {
    // Remove existing measurement markers
    _markers.removeWhere((marker) => marker.markerId.value.startsWith('measure_'));

    // Add numbered markers for each measurement point
    for (int i = 0; i < _measurementPoints.length; i++) {
      String markerId = 'measure_$i';
      bool isStartPoint = i == 0;
      bool isSelected = _selectedMarkerId == markerId;

      // Create cached numbered marker with dynamic sizing
      _getCachedNumberedMarker(i + 1, isStartPoint, isSelected).then((icon) {
        _markers.add(
          Marker(
            markerId: MarkerId(markerId),
            position: LatLng(_measurementPoints[i].latitude, _measurementPoints[i].longitude),
            infoWindow: InfoWindow(
              title: isStartPoint ? '🏁 Start Point' : '📍 Point ${i + 1}',
              snippet: 'Lat: ${_measurementPoints[i].latitude.toStringAsFixed(6)}\n'
                      'Lng: ${_measurementPoints[i].longitude.toStringAsFixed(6)}',
            ),
            icon: icon,
            draggable: true,
            onTap: () => _onMarkerTap(markerId),
            onDragEnd: (LatLng newPosition) => _onMarkerDragEnd(markerId, newPosition),
            // Add long press for sharing options
            consumeTapEvents: true,
          ),
        );

        if (mounted) {
          setState(() {});
        }
      });
    }

    // Add distance labels if enabled
    if (_showDistanceLabels) {
      _addDistanceLabels();
    }
  }

  void _onMarkerTap(String markerId) {
    if (!markerId.startsWith('measure_')) return;

    setState(() {
      _selectedMarkerId = markerId;
    });

    try {
      int index = int.parse(markerId.replaceAll('measure_', ''));
      if (index >= 0 && index < _measurementPoints.length) {
        // Get point location for sharing
        LatLng location = LatLng(
          _measurementPoints[index].latitude,
          _measurementPoints[index].longitude,
        );

        // Show context menu with sharing and removal options
        _showMarkerContextMenu(location, 'Measurement Point ${index + 1}', index);
      }
    } catch (e) {
      if (kDebugMode) print('Error parsing marker ID: $markerId');
    }
  }

  void _onMarkerDragEnd(String markerId, LatLng newPosition) {
    int index = int.parse(markerId.replaceAll('measure_', ''));
    if (index < _measurementPoints.length) {
      _saveToHistory();

      Position updatedPosition = Position(
        latitude: newPosition.latitude,
        longitude: newPosition.longitude,
        timestamp: DateTime.now(),
        accuracy: 0.5, // Set to 0.5 meter accuracy for practical precision
        altitude: 0.0,
        heading: 0.0,
        speed: 0.0,
        speedAccuracy: 0.0,
        altitudeAccuracy: 0.0,
        headingAccuracy: 0.0,
      );

      setState(() {
        _measurementPoints[index] = updatedPosition;
      });

      _calculateMeasurements();
    }
  }

  void _addDistanceLabels() {
    // Add distance labels between consecutive points
    for (int i = 0; i < _measurementPoints.length - 1; i++) {
      Position point1 = _measurementPoints[i];
      Position point2 = _measurementPoints[i + 1];

      double distance = _vincentyDistance(
        point1.latitude,
        point1.longitude,
        point2.latitude,
        point2.longitude,
      );

      LatLng midpoint = LatLng(
        (point1.latitude + point2.latitude) / 2,
        (point1.longitude + point2.longitude) / 2,
      );

      // Create cached distance marker with always visible text
      final appProvider = Provider.of<AppProvider>(context, listen: false);
      _getCachedDistanceMarker(appProvider.formatDistance(distance), false).then((icon) {
        _markers.add(
          Marker(
            markerId: MarkerId('distance_label_${i}_${i + 1}'),
            position: midpoint,
            icon: icon,
            infoWindow: const InfoWindow(), // Empty info window for always visible text
            anchor: const Offset(0.5, 0.5),
            consumeTapEvents: false,
          ),
        );

        if (mounted) {
          setState(() {});
        }
      });
    }
  }

  // Create custom distance text marker that's always visible
  Future<BitmapDescriptor> _createDistanceTextMarker(String distance, bool isClosing) async {
    try {
      final ui.PictureRecorder pictureRecorder = ui.PictureRecorder();
      final Canvas canvas = Canvas(pictureRecorder);

      // Optimized marker dimensions for larger font
      const double width = 240.0;
      const double height = 90.0;
      const double radius = 14.0;

      // Enhanced colors for better visibility
      Color backgroundColor;
      Color borderColor;
      Color textColor = Colors.white;

      if (isClosing) {
        backgroundColor = Colors.orange.shade700;
        borderColor = Colors.orange.shade900;
      } else {
        backgroundColor = Colors.blue.shade600;
        borderColor = Colors.blue.shade800;
      }

      // Draw compact shadow
      final Paint shadowPaint = Paint()
        ..color = Colors.black.withOpacity(0.4)
        ..style = PaintingStyle.fill;
      final RRect shadowRect = RRect.fromRectAndRadius(
        const Rect.fromLTWH(2, 2, width, height),
        const Radius.circular(radius),
      );
      canvas.drawRRect(shadowRect, shadowPaint);

      // Draw background with rounded corners
      final RRect backgroundRect = RRect.fromRectAndRadius(
        const Rect.fromLTWH(0, 0, width, height),
        const Radius.circular(radius),
      );

      // Draw border
      final Paint borderPaint = Paint()
        ..color = borderColor
        ..style = PaintingStyle.fill;
      canvas.drawRRect(backgroundRect, borderPaint);

      // Draw inner background with 2px padding
      final RRect innerRect = RRect.fromRectAndRadius(
        const Rect.fromLTWH(2, 2, width - 4, height - 4),
        const Radius.circular(radius - 2),
      );


      // Draw distance text with enhanced visibility - larger font
      final textPainter = TextPainter(
        text: TextSpan(
          text: distance,
          style: TextStyle(
            color: textColor,
            fontSize: 38, // Larger font for better visibility
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                color: Colors.black.withOpacity(0.8),
                offset: const Offset(2, 2),
                blurRadius: 4,
              ),
              Shadow(
                color: Colors.black.withOpacity(0.4),
                offset: const Offset(1, 1),
                blurRadius: 2,
              ),
            ],
          ),
        ),
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();
      final double textX = (width - textPainter.width) / 2;
      final double textY = (height - textPainter.height) / 2;
      textPainter.paint(canvas, Offset(textX, textY));

      // Convert to image
      final ui.Picture picture = pictureRecorder.endRecording();
      final ui.Image image = await picture.toImage(width.toInt(), height.toInt());
      final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final Uint8List uint8List = byteData!.buffer.asUint8List();

      return BitmapDescriptor.fromBytes(uint8List);
    } catch (e) {
      if (kDebugMode) print('Error creating distance text marker: $e');
      // Fallback to default marker
      return BitmapDescriptor.defaultMarkerWithHue(
        isClosing ? BitmapDescriptor.hueOrange : BitmapDescriptor.hueBlue
      );
    }
  }

  void _updatePolygonsAndPolylines() {
    _polygons.clear();
    _polylines.clear();

    if (_measurementPoints.length < 2) return;

    if (_currentMode == MeasurementMode.area) {
      _addAreaOverlays();
    } else {
      _addDistanceOverlays();
    }
  }

  void _addAreaOverlays() {
    List<LatLng> polygonPoints = _measurementPoints.map((p) => LatLng(p.latitude, p.longitude)).toList();

    // Add distance labels between consecutive points for area mode
    if (_showDistanceLabels) {
      for (int i = 0; i < _measurementPoints.length; i++) {
        int nextIndex = (i + 1) % _measurementPoints.length;

        // Skip if it's the last point and polygon is not closed
        if (nextIndex == 0 && !_isPolygonClosed && _measurementPoints.length < 3) continue;

        Position point1 = _measurementPoints[i];
        Position point2 = _measurementPoints[nextIndex];

        double segmentDistance = _vincentyDistance(
          point1.latitude,
          point1.longitude,
          point2.latitude,
          point2.longitude,
        );

        LatLng midpoint = LatLng(
          (point1.latitude + point2.latitude) / 2,
          (point1.longitude + point2.longitude) / 2,
        );

        // Add cached always-visible distance label marker
        final appProvider = Provider.of<AppProvider>(context, listen: false);
        _getCachedDistanceMarker(
          appProvider.formatDistance(segmentDistance),
          nextIndex == 0, // This is a closing distance
        ).then((icon) {
          _markers.add(
            Marker(
              markerId: MarkerId('area_distance_${i}_$nextIndex'),
              position: midpoint,
              icon: icon,
              infoWindow: const InfoWindow(), // Empty info window for always visible labels
              anchor: const Offset(0.5, 0.5),
              consumeTapEvents: false, // Don't consume tap events
            ),
          );

          if (mounted) {
            setState(() {});
          }
        });
      }
    }

    // Add visual feedback for polygon closure
    if (!_isPolygonClosed && _startPoint != null && _measurementPoints.length >= 3) {
      double closureDistance = _vincentyDistance(
        _measurementPoints.last.latitude,
        _measurementPoints.last.longitude,
        _startPoint!.latitude,
        _startPoint!.longitude,
      );

      _polylines.add(
        Polyline(
          polylineId: const PolylineId('closure_hint'),
          points: [
            LatLng(_measurementPoints.last.latitude, _measurementPoints.last.longitude),
            LatLng(_startPoint!.latitude, _startPoint!.longitude),
          ],
          color: Colors.green.withOpacity(0.7),
          width: 2,
          patterns: [PatternItem.dash(10), PatternItem.gap(5)],
          geodesic: true,
        ),
      );

      // Add closure distance label if enabled
      if (_showDistanceLabels) {
        LatLng closureMidpoint = LatLng(
          (_measurementPoints.last.latitude + _startPoint!.latitude) / 2,
          (_measurementPoints.last.longitude + _startPoint!.longitude) / 2,
        );

        final appProvider = Provider.of<AppProvider>(context, listen: false);
        _getCachedDistanceMarker(
          appProvider.formatDistance(closureDistance),
          true, // This is a closing distance
        ).then((icon) {
          _markers.add(
            Marker(
              markerId: const MarkerId('closure_distance_label'),
              position: closureMidpoint,
              icon: icon,
              infoWindow: const InfoWindow(), // Empty info window for always visible labels
              anchor: const Offset(0.5, 0.5),
              consumeTapEvents: false, // Don't consume tap events
            ),
          );

          if (mounted) {
            setState(() {});
          }
        });
      }
    }

    _polygons.add(
      Polygon(
        polygonId: const PolygonId('measurement_area'),
        points: polygonPoints,
        fillColor: _isPolygonClosed
            ? Colors.blue.withOpacity(0.3)
            : Colors.blue.withOpacity(0.15),
        strokeColor: _isPolygonClosed
            ? Colors.blue.shade700
            : Colors.blue.shade500,
        strokeWidth: _isPolygonClosed ? 4 : 3,
        geodesic: true,
      ),
    );
  }

  void _addDistanceOverlays() {
    List<LatLng> pathPoints = _measurementPoints.map((p) => LatLng(p.latitude, p.longitude)).toList();

    _polylines.add(
      Polyline(
        polylineId: const PolylineId('measurement_distance'),
        points: pathPoints,
        color: Colors.red.shade600,
        width: 4,
        geodesic: true,
      ),
    );
  }

  // UI Components
  Widget _buildMeasurementControls() {
    return Positioned(
      top: 50,
      left: 16,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Mode selection
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildModeButton(MeasurementMode.area, Icons.crop_free, 'Area'),
                const SizedBox(width: 4),
                _buildModeButton(MeasurementMode.distance, Icons.timeline, 'Distance'),
              ],
            ),
            const SizedBox(height: 8),
            // Input method selection
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildInputButton(InputMethod.mapTapping, Icons.touch_app, 'Tap'),
                const SizedBox(width: 4),
                _buildInputButton(InputMethod.gpsWalking, Icons.directions_walk, 'GPS'),
                const SizedBox(width: 4),
                _buildInputButton(InputMethod.hybrid, Icons.compare_arrows, 'Both'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModeButton(MeasurementMode mode, IconData icon, String label) {
    bool isSelected = _currentMode == mode;
    Color color = mode == MeasurementMode.area ? Colors.blue : Colors.red;

    return GestureDetector(
      onTap: () {
        setState(() {
          _currentMode = mode;
          _clearMeasurements();
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? color : Colors.transparent,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: isSelected ? color : Colors.grey.shade400,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 14,
              color: isSelected ? Colors.white : Colors.grey.shade600,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 11,
                color: isSelected ? Colors.white : Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInputButton(InputMethod method, IconData icon, String label) {
    bool isSelected = _inputMethod == method;

    return GestureDetector(
      onTap: () {
        setState(() {
          _inputMethod = method;
          if (_isRecording) {
            _stopRecording();
          }
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
        decoration: BoxDecoration(
          color: isSelected ? Colors.orange : Colors.transparent,
          borderRadius: BorderRadius.circular(4),
          border: Border.all(
            color: isSelected ? Colors.orange : Colors.grey.shade400,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 12,
              color: isSelected ? Colors.white : Colors.grey.shade600,
            ),
            const SizedBox(width: 2),
            Text(
              label,
              style: TextStyle(
                fontSize: 10,
                color: isSelected ? Colors.white : Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionsOverlay() {
    return Positioned(
      top: 200,
      left: 16,
      right: 16,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.7),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              _currentMode == MeasurementMode.area ? Icons.crop_free : Icons.timeline,
              color: Colors.white,
              size: 16,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                _getInstructionText(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                ),
              ),
            ),
            IconButton(
              onPressed: () {
                setState(() {
                  _showInstructions = false;
                });
              },
              icon: const Icon(Icons.close, color: Colors.white, size: 16),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
          ],
        ),
      ),
    );
  }

  String _getInstructionText() {
    if (_currentMode == MeasurementMode.area) {
      return _inputMethod == InputMethod.mapTapping
          ? 'Tap on map to measure field area'
          : _inputMethod == InputMethod.gpsWalking
              ? 'Walk around field boundary'
              : 'Tap map or walk to measure area';
    } else {
      return _inputMethod == InputMethod.mapTapping
          ? 'Tap on map to measure distance'
          : _inputMethod == InputMethod.gpsWalking
              ? 'Walk the path to measure'
              : 'Tap map or walk to measure distance';
    }
  }

  Widget _buildResultsCard() {
    return Positioned(
      bottom: 120,
      left: 16,
      right: 16,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          children: [
            Icon(
              _currentMode == MeasurementMode.area ? Icons.crop_free : Icons.timeline,
              color: _currentMode == MeasurementMode.area ? Colors.blue : Colors.red,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Consumer<AppProvider>(
                builder: (context, appProvider, child) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _currentMode == MeasurementMode.area
                            ? appProvider.formatArea(_totalArea)
                            : appProvider.formatDistance(_totalDistance),
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: _currentMode == MeasurementMode.area ? Colors.blue : Colors.red,
                        ),
                      ),
                      if (_currentMode == MeasurementMode.area && _measurementPoints.length >= 3)
                        Text(
                          'Perimeter: ${appProvider.formatPerimeter(_perimeter)}',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                    ],
                  );
                },
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${_measurementPoints.length} pts',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomControls() {
    return Positioned(
      bottom: 16,
      left: 16,
      right: 16,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // GPS Recording (for GPS modes)
            if (_inputMethod != InputMethod.mapTapping)
              _buildControlButton(
                icon: _isRecording ? Icons.stop : Icons.play_arrow,
                onPressed: _isRecording ? _stopRecording : _startRecording,
                color: _isRecording ? Colors.red : Colors.green,
              ),

            // Add current location (for GPS modes)
            if (_inputMethod != InputMethod.mapTapping)
              _buildControlButton(
                icon: Icons.add_location,
                onPressed: _addPoint,
                color: Colors.blue,
              ),

            // Undo
            _buildControlButton(
              icon: Icons.undo,
              onPressed: _canUndo ? _undo : null,
              color: Colors.orange,
            ),

            // Clear
            _buildControlButton(
              icon: Icons.clear_all,
              onPressed: _measurementPoints.isNotEmpty ? _clearMeasurements : null,
              color: Colors.red,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required Color color,
  }) {
    bool isEnabled = onPressed != null;
    return IconButton(
      onPressed: onPressed,
      icon: Icon(
        icon,
        color: isEnabled ? color : Colors.grey.shade400,
        size: 20,
      ),
      padding: const EdgeInsets.all(8),
    );
  }

  Widget _buildAnimatedFloatingActionButton() {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 400),
      transitionBuilder: (Widget child, Animation<double> animation) {
        return ScaleTransition(
          scale: animation,
          child: child,
        );
      },
      child: Container(
        key: ValueKey(_showFloatingOptions),
        width: _showFloatingOptions ? 320.0 : 56.0,
        height: _showFloatingOptions ? 320.0 : 56.0,
        constraints: const BoxConstraints(
          minWidth: 56.0,
          minHeight: 56.0,
        ),
        child: Stack(
          alignment: Alignment.center,
          clipBehavior: Clip.none,
          children: [
          // Background blur effect when options are open
          if (_showFloatingOptions)
            AnimatedOpacity(
              duration: const Duration(milliseconds: 300),
              opacity: _showFloatingOptions ? 0.1 : 0.0,
              child: Container(
                width: 320,
                height: 320,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.black.withOpacity(0.1),
                ),
              ),
            ),

          // === PRIMARY ACTIONS (Always Visible) ===
          // Create New - Primary action
          _buildProfessionalCircularOption(
            icon: Icons.add_circle_outline,
            label: 'Create New',
            color: const Color(0xFF2196F3), // Material Blue
            angle: -90, // Top
            distance: 100,
            isPrimary: true,
            onPressed: () {
              _showCreateNewOptions();
              _closeFloatingOptions();
            },
          ),

          // My Location (Tutorial disabled)
          _buildProfessionalCircularOption(
            icon: Icons.my_location_rounded,
            label: 'My Location',
            color: const Color(0xFF4CAF50), // Material Green
            angle: -45, // Top-right
            distance: 100,
            isPrimary: true,
            onPressed: () {
              _goToMyLocation();
              _closeFloatingOptions();
            },
          ),

          // Map View Toggle (Tutorial disabled)
          _buildProfessionalCircularOption(
            icon: _showSatelliteView ? Icons.map_rounded : Icons.satellite_alt_rounded,
            label: _showSatelliteView ? 'Normal View' : 'Satellite View',
            color: const Color(0xFF9C27B0), // Material Purple
            angle: 0, // Right
            distance: 100,
            isPrimary: true,
            onPressed: () {
              setState(() {
                _showSatelliteView = !_showSatelliteView;
              });
              _closeFloatingOptions();
            },
          ),

          // === SECONDARY ACTIONS (Context-Aware) ===
          // Share Location - Secondary action
          _buildProfessionalCircularOption(
            icon: Icons.share_location_rounded,
            label: 'Share Location',
            color: const Color(0xFFFF9800), // Material Orange
            angle: 45, // Bottom-right
            distance: 100,
            isPrimary: false,
            onPressed: () {
              _shareCurrentLocation();
              _closeFloatingOptions();
            },
          ),

          // Saved Measurements - Secondary action
          _buildProfessionalCircularOption(
            icon: Icons.folder_rounded,
            label: 'Saved Data',
            color: const Color(0xFF795548), // Material Brown
            angle: 90, // Bottom
            distance: 100,
            isPrimary: false,
            onPressed: () {
              _viewSavedMeasurements();
              _closeFloatingOptions();
            },
          ),

          // === MEASUREMENT MODE ACTIONS (Conditional) ===
          // Save Measurement - Only in measurement mode with points
          _buildProfessionalCircularOption(
            icon: Icons.save_rounded,
            label: 'Save Measurement',
            color: const Color(0xFF4CAF50), // Material Green
            angle: 135, // Bottom-left
            distance: 100,
            isPrimary: false,
            isVisible: _isMeasurementMode && _measurementPoints.isNotEmpty,
            onPressed: () {
              _saveMeasurement();
              _closeFloatingOptions();
            },
          ),

          // Share Points - Only in measurement mode with points
          _buildProfessionalCircularOption(
            icon: Icons.share_rounded,
            label: 'Share Points',
            color: const Color(0xFF3F51B5), // Material Indigo
            angle: 180, // Left
            distance: 100,
            isPrimary: false,
            isVisible: _isMeasurementMode && _measurementPoints.isNotEmpty,
            onPressed: () {
              _shareMeasurementPoints();
              _closeFloatingOptions();
            },
          ),

          // Distance Labels - Only in measurement mode
          _buildProfessionalCircularOption(
            icon: _showDistanceLabels ? Icons.straighten : Icons.straighten_outlined,
            label: _showDistanceLabels ? 'Hide Distances' : 'Show Distances',
            color: _showDistanceLabels ? const Color(0xFF4CAF50) : const Color(0xFF9E9E9E),
            angle: -135, // Top-left
            distance: 100,
            isPrimary: false,
            isVisible: _isMeasurementMode,
            onPressed: () {
              setState(() {
                _showDistanceLabels = !_showDistanceLabels;
              });
              _updateMapOverlays();
              _closeFloatingOptions();
            },
          ),

          // === MAIN FAB (Center) ===
          _buildMainFloatingActionButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildMainFloatingActionButton() {
    // Calculate safe positioning for main FAB
    final double containerSize = _showFloatingOptions ? 320.0 : 56.0;
    final double fabSize = 56.0;
    final double centerOffset = (containerSize - fabSize) / 2.0;

    return Positioned(
      right: _showFloatingOptions ? centerOffset.clamp(0.0, containerSize - fabSize) : 0.0,
      bottom: _showFloatingOptions ? centerOffset.clamp(0.0, containerSize - fabSize) : 0.0,
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: FloatingActionButton(
          heroTag: "main_fab_map_view_${DateTime.now().millisecondsSinceEpoch}", // Unique hero tag to prevent conflicts
          onPressed: () {
            setState(() {
              _showFloatingOptions = !_showFloatingOptions;
            });

            // Haptic feedback for better UX
            if (_showFloatingOptions) {
              // Light impact when opening
              HapticFeedback.lightImpact();
            } else {
              // Selection feedback when closing
              HapticFeedback.selectionClick();
            }
          },
          backgroundColor: const Color(0xFF2196F3), // Material Blue
          elevation: 6,
          child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            transitionBuilder: (Widget child, Animation<double> animation) {
              return RotationTransition(
                turns: animation,
                child: child,
              );
            },
            child: Icon(
              _showFloatingOptions ? Icons.close_rounded : Icons.apps_rounded,
              key: ValueKey(_showFloatingOptions),
              color: Colors.white,
              size: 28,
            ),
          ),
        ),
      ),
    );
  }

  void _closeFloatingOptions() {
    setState(() {
      _showFloatingOptions = false;
    });
  }

  Widget _buildProfessionalCircularOption({
    required IconData icon,
    required String label,
    required Color color,
    required double angle,
    required double distance,
    required VoidCallback onPressed,
    bool isPrimary = false,
    bool isVisible = true,
  }) {
    // Convert angle to radians and calculate position
    final double radians = angle * (3.14159 / 180);
    final double x = distance * cos(radians);
    final double y = distance * sin(radians);

    // Calculate final scale and opacity based on visibility and show state
    double finalScale = (_showFloatingOptions && isVisible) ? 1.0 : 0.0;
    double finalOpacity = (_showFloatingOptions && isVisible) ? 1.0 : 0.0;

    // Ensure scale and opacity are within valid ranges
    finalScale = finalScale.clamp(0.0, 1.0);
    finalOpacity = finalOpacity.clamp(0.0, 1.0);

    // Calculate safe positions to prevent negative values
    final double containerSize = _showFloatingOptions ? 320.0 : 56.0;
    final double center = containerSize / 2.0;
    final double buttonSize = 56.0;
    final double halfButton = buttonSize / 2.0;

    // Only calculate positions if options are showing
    if (!_showFloatingOptions) {
      return const SizedBox.shrink();
    }

    // Calculate positions with bounds checking
    double leftPosition = center + x - halfButton;
    double topPosition = center - y - halfButton;

    // Ensure positions are within safe bounds
    leftPosition = leftPosition.clamp(0.0, containerSize - buttonSize);
    topPosition = topPosition.clamp(0.0, containerSize - buttonSize);

    return Positioned(
      left: leftPosition,
      top: topPosition,
      child: AnimatedScale(
        scale: finalScale,
        duration: Duration(milliseconds: isPrimary ? 300 : 400),
        curve: Curves.elasticOut,
        child: AnimatedOpacity(
          opacity: finalOpacity,
          duration: Duration(milliseconds: isPrimary ? 200 : 250),
          child: Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: color.withOpacity(0.3),
                  blurRadius: isPrimary ? 8 : 6,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(28),
                onTap: () {
                  HapticFeedback.lightImpact();
                  onPressed();
                },
                child: Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                    border: isPrimary
                        ? Border.all(color: Colors.white, width: 2)
                        : null,
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        icon,
                        color: Colors.white,
                        size: isPrimary ? 24 : 20,
                      ),
                      if (isPrimary) ...[
                        const SizedBox(height: 2),
                        Text(
                          label.split(' ').first, // First word only for primary
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 8,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCircularOption({
    required IconData icon,
    required String label,
    required Color color,
    required double angle,
    required double distance,
    required VoidCallback onPressed,
    bool isVisible = true, // Add visibility parameter
  }) {
    // Convert angle to radians
    double radians = angle * (pi / 180);

    // Calculate position from center
    double x = distance * cos(radians);
    double y = distance * sin(radians);

    // Calculate final scale (consider both show state and visibility)
    double finalScale = _showFloatingOptions && isVisible ? 1.0 : 0.0;
    double finalOpacity = _showFloatingOptions && isVisible ? 1.0 : 0.0;

    return Positioned(
      left: 150 + x - 20, // Center (150) + offset - half button width (300/2 = 150)
      top: 150 - y - 20,  // Center (150) - offset - half button width (y inverted for screen coords)
      child: AnimatedScale(
        duration: const Duration(milliseconds: 300),
        curve: Curves.elasticOut,
        scale: finalScale,
        child: AnimatedOpacity(
          duration: const Duration(milliseconds: 200),
          opacity: finalOpacity,
          child: GestureDetector(
            onTap: onPressed,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Label with background
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.8),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    label,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),

                const SizedBox(height: 4),

                // Option button
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    icon,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }



  void _showMarkerContextMenu(LatLng location, String pointName, int pointIndex) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(pointName),
        content: const Text('What would you like to do with this point?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _showLocationSharingOptions(location, pointName: pointName);
            },
            child: const Text('Share'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _removePoint(pointIndex);
            },
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }

  void _showQuickRemovalDialog(int index) {
    Position point = _measurementPoints[index];
    bool isStartPoint = index == 0;

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              isStartPoint ? Icons.flag : Icons.place,
              color: isStartPoint ? Colors.green : Colors.red,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              isStartPoint ? 'Start Point' : 'Point ${index + 1}',
              style: const TextStyle(fontSize: 16),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Coordinates: ${point.latitude.toStringAsFixed(6)}, ${point.longitude.toStringAsFixed(6)}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.pop(context);
                    _removePoint(index);
                  },
                  icon: const Icon(Icons.delete, size: 16),
                  label: const Text('Remove'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _selectedMarkerId = null;
              });
            },
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _removePoint(int index) {
    if (index >= 0 && index < _measurementPoints.length) {
      _saveToHistory();

      setState(() {
        _measurementPoints.removeAt(index);
        _selectedMarkerId = null;

        _isPolygonClosed = false;
        if (_measurementPoints.isNotEmpty) {
          _startPoint = _measurementPoints.first;
        } else {
          _startPoint = null;
          _showInstructions = true;
        }
      });

      // Clear all measurement markers and overlays, then recalculate
      _clearMeasurementMarkers();
      _calculateMeasurements();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Point ${index + 1} removed'),
          backgroundColor: Colors.orange,
          action: SnackBarAction(
            label: 'Undo',
            textColor: Colors.white,
            onPressed: _undo,
          ),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  // Clear all measurement-related markers
  void _clearMeasurementMarkers() {
    setState(() {
      // Remove all measurement markers
      _markers.removeWhere((marker) =>
        marker.markerId.value.startsWith('measure_') ||
        marker.markerId.value.startsWith('distance_label_') ||
        marker.markerId.value.startsWith('area_distance_') ||
        marker.markerId.value.startsWith('distance_text_') ||
        marker.markerId.value.startsWith('closure_distance_label')
      );

      // Clear polygons and polylines
      _polygons.clear();
      _polylines.clear();
    });
  }

  // Enhanced My Location functionality with proper zoom
  Future<void> _goToMyLocation() async {
    try {
      // First try to get current location from app provider
      final appProvider = Provider.of<AppProvider>(context, listen: false);
      Position? targetLocation;

      // Convert LocationEntity to Position if available
      if (appProvider.currentLocation != null) {
        targetLocation = Position(
          latitude: appProvider.currentLocation!.latitude,
          longitude: appProvider.currentLocation!.longitude,
          timestamp: DateTime.now(),
          accuracy: 0.5, // Set to 0.5 meter accuracy for practical precision
          altitude: 0.0,
          heading: 0.0,
          speed: 0.0,
          speedAccuracy: 0.0,
          altitudeAccuracy: 0.0,
          headingAccuracy: 0.0,
        );
      }

      // If no location in provider, try to get fresh location
      if (targetLocation == null) {
        // Check if location services are enabled
        bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
        if (!serviceEnabled) {
          _showLocationError('Location services are disabled. Please enable location services.');
          return;
        }

        // Check location permissions
        LocationPermission permission = await Geolocator.checkPermission();
        if (permission == LocationPermission.denied) {
          permission = await Geolocator.requestPermission();
          if (permission == LocationPermission.denied) {
            _showLocationError('Location permissions are denied. Please grant location permission.');
            return;
          }
        }

        if (permission == LocationPermission.deniedForever) {
          _showLocationError('Location permissions are permanently denied. Please enable in settings.');
          return;
        }

        // Show loading indicator
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                SizedBox(width: 16),
                Text('Getting your location...'),
              ],
            ),
            backgroundColor: Colors.blue,
            duration: Duration(seconds: 2),
          ),
        );

        // Get current position with high accuracy
        try {
          targetLocation = await Geolocator.getCurrentPosition(
            desiredAccuracy: LocationAccuracy.high,
            timeLimit: const Duration(seconds: 10),
          );

          // Update current position state
          setState(() {
            _currentPosition = targetLocation;
          });

          if (kDebugMode) {
            print('Fresh location obtained: ${targetLocation!.latitude}, ${targetLocation.longitude}');
          }
        } catch (e) {
          _showLocationError('Unable to get current location. Please try again.');
          return;
        }
      }

      // Animate to location with appropriate zoom level
      if (targetLocation != null && _mapController != null) {
        // Determine appropriate zoom level based on measurement mode
        double zoomLevel = _isMeasurementMode ? 18.0 : 16.0; // Higher zoom for measurement mode

        await _mapController!.animateCamera(
          CameraUpdate.newLatLngZoom(
            LatLng(targetLocation.latitude, targetLocation.longitude),
            zoomLevel,
          ),
        );

        // Show success feedback
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.location_on, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Location: ${targetLocation.latitude.toStringAsFixed(6)}, ${targetLocation.longitude.toStringAsFixed(6)}',
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );

        if (kDebugMode) {
          print('Map centered on location: ${targetLocation.latitude}, ${targetLocation.longitude} at zoom $zoomLevel');
        }
      } else {
        _showLocationError('Map not ready or location unavailable.');
      }
    } catch (e) {
      if (kDebugMode) print('Error in _goToMyLocation: $e');
      _showLocationError('Error getting location. Please try again.');
    }
  }

  void _showLocationError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: 'Settings',
          textColor: Colors.white,
          onPressed: () {
            Geolocator.openLocationSettings();
          },
        ),
      ),
    );
  }

  // Get cached numbered marker to avoid recreating identical markers
  Future<BitmapDescriptor> _getCachedNumberedMarker(int number, bool isStart, bool isSelected) async {
    String cacheKey = '${number}_${isStart}_$isSelected';

    if (_markerCache.containsKey(cacheKey)) {
      return _markerCache[cacheKey]!;
    }

    BitmapDescriptor marker = await _createNumberedMarker(number, isStart, isSelected);
    _markerCache[cacheKey] = marker;
    return marker;
  }

  // Get cached distance marker to avoid recreating identical markers
  Future<BitmapDescriptor> _getCachedDistanceMarker(String distance, bool isClosing) async {
    String cacheKey = '${distance}_$isClosing';

    if (_distanceMarkerCache.containsKey(cacheKey)) {
      return _distanceMarkerCache[cacheKey]!;
    }

    BitmapDescriptor marker = await _createDistanceTextMarker(distance, isClosing);
    _distanceMarkerCache[cacheKey] = marker;
    return marker;
  }

  // Create custom numbered marker with dynamic sizing based on number
  Future<BitmapDescriptor> _createNumberedMarker(int number, bool isStart, bool isSelected) async {
    try {
      final ui.PictureRecorder pictureRecorder = ui.PictureRecorder();
      final Canvas canvas = Canvas(pictureRecorder);

      // Compact sizing based on number of digits - much smaller base size
      double baseSize = 60.0;  // Reduced from 120px to 60px
      double extraSizePerDigit = 10.0;  // Reduced from 20px to 10px
      int digits = number.toString().length;

      // Calculate compact dynamic size - smaller for all numbers
      double size = baseSize + (extraSizePerDigit * (digits - 1));
      double radius = size / 2;

      // Colors based on marker type
      Color backgroundColor;
      Color borderColor;
      Color textColor = Colors.white;

      if (isStart) {
        backgroundColor = Colors.green.shade600;
        borderColor = Colors.green.shade800;
      } else if (isSelected) {
        backgroundColor = Colors.blue.shade600;
        borderColor = Colors.blue.shade800;
      } else {
        backgroundColor = Colors.red.shade600;
        borderColor = Colors.red.shade800;
      }

      // Draw shadow for better visibility
      final Paint shadowPaint = Paint()
        ..color = Colors.black.withOpacity(0.3)
        ..style = PaintingStyle.fill;
      canvas.drawCircle(Offset(radius + 2, radius + 2), radius, shadowPaint);

      // Draw outer border with minimal padding
      final Paint borderPaint = Paint()
        ..color = borderColor
        ..style = PaintingStyle.fill;
      canvas.drawCircle(Offset(radius, radius), radius, borderPaint);

      // Draw inner circle with 2px padding
      final Paint backgroundPaint = Paint()
        ..color = backgroundColor
        ..style = PaintingStyle.fill;
      canvas.drawCircle(Offset(radius, radius), radius - 2, backgroundPaint);

      // Draw white inner circle for number with 2px padding
      final Paint innerPaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.fill;
      canvas.drawCircle(Offset(radius, radius), radius - 4, innerPaint);

      // Larger font size for better visibility - maximum space utilization
      double fontSize;
      if (digits == 1) {
        fontSize = size * 0.7; // 70% of marker size for single digit (much larger)
      } else if (digits == 2) {
        fontSize = size * 0.65; // 65% of marker size for double digit
      } else if (digits == 3) {
        fontSize = size * 0.6; // 60% of marker size for triple digit
      } else {
        fontSize = size * 0.55; // 55% of marker size for 4+ digits
      }

      // Draw number text with dynamic sizing
      final textPainter = TextPainter(
        text: TextSpan(
          text: number.toString(),
          style: TextStyle(
            color: backgroundColor,
            fontSize: fontSize,
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                color: Colors.black.withOpacity(0.3),
                offset: const Offset(1, 1),
                blurRadius: 1,
              ),
            ],
          ),
        ),
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();
      final double textX = (size - textPainter.width) / 2;
      final double textY = (size - textPainter.height) / 2;
      textPainter.paint(canvas, Offset(textX, textY));

      // Convert to image
      final ui.Picture picture = pictureRecorder.endRecording();
      final ui.Image image = await picture.toImage(size.toInt(), size.toInt());
      final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final Uint8List uint8List = byteData!.buffer.asUint8List();

      return BitmapDescriptor.fromBytes(uint8List);
    } catch (e) {
      if (kDebugMode) print('Error creating numbered marker: $e');
      // Fallback to default marker
      return BitmapDescriptor.defaultMarkerWithHue(
        isStart ? BitmapDescriptor.hueGreen :
        isSelected ? BitmapDescriptor.hueBlue : BitmapDescriptor.hueRed
      );
    }
  }

  // Location sharing methods
  Future<void> _shareCurrentLocation() async {
    try {
      if (_currentPosition != null) {
        await LocationSharingService.shareCurrentLocation(
          position: _currentPosition!,
          customMessage: 'Check out my current location from GPS Map Camera!',
          locationName: 'My Current Location',
        );

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Current location shared successfully!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Current location not available. Please enable GPS.'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error sharing location: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  Future<void> _shareMeasurementPoints() async {
    try {
      if (_measurementPoints.isNotEmpty) {
        await LocationSharingService.shareMeasurementPoints(
          points: _measurementPoints,
          title: 'GPS Measurement from GPS Map Camera',
          totalArea: _totalArea,
          totalDistance: _totalDistance,
          measurementType: _currentMode.name,
        );

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Measurement points shared successfully!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No measurement points to share. Please add some points first.'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error sharing measurement points: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  Future<void> _shareSpecificPoint(LatLng location, {String? pointName}) async {
    try {
      await LocationSharingService.shareLocationPoint(
        location: location,
        pointName: pointName ?? 'GPS Point',
        description: 'Shared from GPS Map Camera App',
        customMessage: 'Check out this location!',
      );

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Location point shared successfully!'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error sharing location point: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  void _showLocationSharingOptions(LatLng location, {String? pointName}) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Title
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'Share Location',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            // Share options
            ListTile(
              leading: const Icon(Icons.share, color: Colors.blue),
              title: const Text('Share Full Details'),
              subtitle: const Text('Share with address, coordinates, and map links'),
              onTap: () {
                Navigator.pop(context);
                _shareSpecificPoint(location, pointName: pointName);
              },
            ),

            ListTile(
              leading: const Icon(Icons.location_on, color: Colors.green),
              title: const Text('Share Coordinates Only'),
              subtitle: const Text('Share latitude and longitude'),
              onTap: () {
                Navigator.pop(context);
                LocationSharingService.shareLocationCustomFormat(
                  location: location,
                  format: 'coordinates',
                  locationName: pointName,
                );
              },
            ),

            ListTile(
              leading: const Icon(Icons.map, color: Colors.purple),
              title: const Text('Share Maps Link'),
              subtitle: const Text('Share Google Maps link'),
              onTap: () {
                Navigator.pop(context);
                LocationSharingService.shareLocationCustomFormat(
                  location: location,
                  format: 'maps_link',
                  locationName: pointName,
                );
              },
            ),

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  // Measurement saving and management methods
  Future<void> _saveMeasurement() async {
    if (_measurementPoints.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No measurement points to save'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    // Show save dialog
    final result = await showDialog<Map<String, String>>(
      context: context,
      builder: (context) => _buildSaveDialog(),
    );

    if (result != null) {
      try {
        final storageService = MeasurementStorageService();
        await storageService.initialize();

        // Create measurement record
        final measurement = MeasurementRecord(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: result['name'] ?? 'Untitled Measurement',
          type: _currentMode.name,
          points: _measurementPoints.map((point) => {
            'latitude': point.latitude,
            'longitude': point.longitude,
          }).toList(),
          area: _totalArea,
          distance: _totalDistance,
          perimeter: _perimeter,
          units: 'metric',
          timestamp: DateTime.now(),
          metadata: {
            'app_version': '1.0',
            'measurement_mode': _currentMode.name,
            'input_method': _inputMethod.name,
            'notes': result['notes'] ?? '',
          },
        );

        await storageService.saveMeasurement(measurement);

        _showSaveSuccessDialog(measurement.name);

        // Optionally clear current measurement
        if (result['clearAfterSave'] == 'true') {
          _clearMeasurement();
        }

      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving measurement: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Widget _buildSaveDialog() {
    final nameController = TextEditingController();
    final notesController = TextEditingController();
    bool clearAfterSave = false;

    // Generate default name
    final now = DateTime.now();
    final defaultName = '${_currentMode.name.toUpperCase()} ${now.day}/${now.month}/${now.year} ${now.hour}:${now.minute.toString().padLeft(2, '0')}';
    nameController.text = defaultName;

    return StatefulBuilder(
      builder: (context, setState) => AlertDialog(
        title: const Text('Save Measurement'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Measurement summary
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Measurement Summary',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade700,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Consumer<AppProvider>(
                      builder: (context, appProvider, child) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Type: ${_currentMode.name.toUpperCase()}'),
                            Text('Points: ${_measurementPoints.length}'),
                            if (_totalArea > 0) Text('Area: ${appProvider.formatArea(_totalArea)}'),
                            if (_totalDistance > 0) Text('Distance: ${appProvider.formatDistance(_totalDistance)}'),
                            if (_perimeter > 0) Text('Perimeter: ${appProvider.formatPerimeter(_perimeter)}'),
                          ],
                        );
                      },
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Name field
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'Measurement Name',
                  border: OutlineInputBorder(),
                  hintText: 'Enter a name for this measurement',
                ),
                maxLength: 50,
              ),

              const SizedBox(height: 16),

              // Notes field
              TextField(
                controller: notesController,
                decoration: const InputDecoration(
                  labelText: 'Notes (Optional)',
                  border: OutlineInputBorder(),
                  hintText: 'Add any additional notes',
                ),
                maxLines: 3,
                maxLength: 200,
              ),

              const SizedBox(height: 16),

              // Clear after save option
              CheckboxListTile(
                title: const Text('Clear measurement after saving'),
                subtitle: const Text('Remove current points and start fresh'),
                value: clearAfterSave,
                onChanged: (value) {
                  setState(() => clearAfterSave = value ?? false);
                },
                controlAffinity: ListTileControlAffinity.leading,
                contentPadding: EdgeInsets.zero,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please enter a measurement name'),
                    backgroundColor: Colors.orange,
                  ),
                );
                return;
              }

              Navigator.pop(context, {
                'name': nameController.text.trim(),
                'notes': notesController.text.trim(),
                'clearAfterSave': clearAfterSave.toString(),
              });
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _viewSavedMeasurements() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CategoryManagerPage(),
      ),
    );
  }

  void _clearMeasurement() {
    setState(() {
      _measurementPoints.clear();
      _totalArea = 0.0;
      _totalDistance = 0.0;
      _perimeter = 0.0;
      _selectedMarkerId = null;
    });
    _updateMapOverlays();
  }

  // Enhanced Create New Options Dialog
  void _showCreateNewOptions() {
    HapticFeedback.mediumImpact();
    final l10n = AppLocalizations.of(context);

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Enhanced handle bar
            Container(
              width: 48,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Enhanced title section
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF2196F3).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.add_circle_outline,
                      color: Color(0xFF2196F3),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Create New',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade800,
                        ),
                      ),
                      Text(
                        'Choose what you want to create',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 8),

            // Enhanced Field option
            _buildProfessionalOption(
              icon: Icons.crop_free_rounded,
              iconColor: const Color(0xFF4CAF50),
              title: l10n?.fieldMeasurement ?? 'Field Measurement',
              subtitle: 'Measure field area by marking boundaries',
              description: 'Perfect for agricultural land, plots, and property areas',
              onTap: () {
                Navigator.pop(context);
                _createNewField();
              },
            ),

            // Enhanced Distance option
            _buildProfessionalOption(
              icon: Icons.straighten_rounded,
              iconColor: const Color(0xFF2196F3),
              title: l10n?.distanceMeasurement ?? 'Distance Measurement',
              subtitle: 'Measure distance between multiple points',
              description: 'Ideal for roads, paths, and linear measurements',
              onTap: () {
                Navigator.pop(context);
                _createNewDistance();
              },
            ),

            // Enhanced Marker option
            _buildProfessionalOption(
              icon: Icons.place_rounded,
              iconColor: const Color(0xFFFF9800),
              title: l10n?.locationMarker ?? 'Location Marker',
              subtitle: 'Mark and save important locations',
              description: 'Save points of interest with custom categories',
              onTap: () {
                Navigator.pop(context);
                _createNewMarker();
              },
            ),

            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildProfessionalOption({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String subtitle,
    required String description,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            HapticFeedback.lightImpact();
            onTap();
          },
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                // Enhanced icon container
                Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    color: iconColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: iconColor.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    icon,
                    color: iconColor,
                    size: 28,
                  ),
                ),

                const SizedBox(width: 16),

                // Enhanced text content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        description,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade500,
                        ),
                      ),
                    ],
                  ),
                ),

                // Arrow indicator
                Icon(
                  Icons.arrow_forward_ios_rounded,
                  color: Colors.grey.shade400,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Create New Field
  void _createNewField() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FieldMeasurementPage(
          initialLocation: _center,
          onFieldSaved: (fieldData) {
            _showSuccessMessage('Field measurement saved successfully!');
          },
        ),
      ),
    );
  }

  // Create New Distance
  void _createNewDistance() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DistanceMeasurementPage(
          initialLocation: _center,
          onDistanceSaved: (distanceData) {
            _showSuccessMessage('Distance measurement saved successfully!');
          },
        ),
      ),
    );
  }

  // Create New Marker
  void _createNewMarker() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MarkerCreationPage(
          initialLocation: _center,
          onMarkerSaved: (markerData) {
            _showSuccessMessage('Location marker saved successfully!');
          },
        ),
      ),
    );
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showSaveSuccessDialog(String measurementName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.green.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.check_circle,
                color: Colors.green.shade700,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'Measurement Saved!',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Your measurement "$measurementName" has been saved successfully.',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.storage, color: Colors.blue.shade700, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Storage Location',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '📱 Local Device Storage',
                    style: TextStyle(color: Colors.blue.shade600),
                  ),
                  Text(
                    '💾 Hive Database (Fast Access)',
                    style: TextStyle(color: Colors.blue.shade600),
                  ),
                  Text(
                    '🗄️ SQLite Database (Backup)',
                    style: TextStyle(color: Colors.blue.shade600),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.orange.shade700, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Access Your Data',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.orange.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '• Tap "Saved Data" button to view all measurements',
                    style: TextStyle(color: Colors.orange.shade600),
                  ),
                  Text(
                    '• Export as PDF, JSON, or CSV formats',
                    style: TextStyle(color: Colors.orange.shade600),
                  ),
                  Text(
                    '• Share measurements with others',
                    style: TextStyle(color: Colors.orange.shade600),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Continue'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context);
              _viewSavedMeasurements();
            },
            icon: const Icon(Icons.folder_rounded),
            label: const Text('View Saved Data'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }


}
