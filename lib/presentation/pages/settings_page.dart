import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_state_provider.dart';
import '../widgets/banner_ad_widget.dart';
import '../../data/services/auth_service.dart';
import 'pro_upgrade_page.dart';
import 'help_tutorial_page.dart';
import 'feedback_page.dart';
import 'auth/login_screen.dart';
import '../../generated/l10n/app_localizations.dart';
import '../../data/services/localization_service.dart';
import '../../data/services/unit_conversion_service.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({Key? key}) : super(key: key);

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  String _selectedLanguage = 'English';
  String _selectedAreaUnit = 'Square Meters';
  String _selectedDistanceUnit = 'Meters';
  String _selectedPerimeterUnit = 'Meters';

  final List<String> _languages = LocalizationService.instance.getSupportedLanguageNames();
  final List<String> _areaUnits = UnitConversionService.instance.getSupportedAreaUnits();
  final List<String> _distanceUnits = UnitConversionService.instance.getSupportedDistanceUnits();
  final List<String> _perimeterUnits = UnitConversionService.instance.getSupportedDistanceUnits();

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  void _loadSettings() {
    final appProvider = Provider.of<AppProvider>(context, listen: false);
    final userSettings = appProvider.userSettings;

    setState(() {
      _selectedLanguage = userSettings.language;
      _selectedAreaUnit = userSettings.areaUnit;
      _selectedDistanceUnit = userSettings.distanceUnit;
      _selectedPerimeterUnit = userSettings.perimeterUnit;
    });
  }

  Future<void> _saveSettings() async {
    final appProvider = Provider.of<AppProvider>(context, listen: false);
    final localizationService = LocalizationService.instance;

    // Get language code from language name
    final locale = localizationService.getLocaleFromLanguageName(_selectedLanguage);
    final languageCode = locale?.languageCode ?? 'en';

    final newSettings = appProvider.userSettings.copyWith(
      language: _selectedLanguage,
      languageCode: languageCode,
      areaUnit: _selectedAreaUnit,
      distanceUnit: _selectedDistanceUnit,
      perimeterUnit: _selectedPerimeterUnit,
    );

    await appProvider.setUserSettings(newSettings);

    if (mounted) {
      final l10n = AppLocalizations.of(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(l10n?.settingsSaved ?? 'Settings saved successfully'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  Future<void> _logout() async {
    final authService = AuthService();

    // Show confirmation dialog
    final shouldLogout = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Logout'),
          ),
        ],
      ),
    );

    if (shouldLogout == true) {
      try {
        await authService.signOut();

        if (mounted) {
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => const LoginScreen()),
            (route) => false,
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Logout failed: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.teal,
        ),
      ),
    );
  }

  Widget _buildDropdownTile({
    required String title,
    required String subtitle,
    required String value,
    required List<String> items,
    required Function(String?) onChanged,
    required IconData icon,
  }) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: Icon(icon, color: Colors.teal),
        title: Text(title),
        subtitle: Text(subtitle),
        trailing: DropdownButton<String>(
          value: value,
          underline: Container(),
          items: items.map((String item) {
            return DropdownMenuItem<String>(
              value: item,
              child: Text(item),
            );
          }).toList(),
          onChanged: onChanged,
        ),
      ),
    );
  }

  Widget _buildActionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: Icon(icon, color: Colors.teal),
        title: Text(title),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n?.settings ?? 'Settings'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveSettings,
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Preferences Section
            _buildSectionHeader(l10n?.preferences ?? 'Preferences'),
            _buildDropdownTile(
              title: l10n?.language ?? 'Language',
              subtitle: l10n?.selectLanguage ?? 'Select your preferred language',
              value: _selectedLanguage,
              items: _languages,
              icon: Icons.language,
              onChanged: (String? newValue) {
                if (newValue != null) {
                  setState(() {
                    _selectedLanguage = newValue;
                  });
                }
              },
            ),

            // Unit Formats Section
            _buildSectionHeader(l10n?.unitFormats ?? 'Unit Formats'),
            _buildDropdownTile(
              title: l10n?.areaUnit ?? 'Area Unit',
              subtitle: l10n?.areaUnitDescription ?? 'Unit for area measurements',
              value: _selectedAreaUnit,
              items: _areaUnits,
              icon: Icons.crop_square,
              onChanged: (String? newValue) {
                if (newValue != null) {
                  setState(() {
                    _selectedAreaUnit = newValue;
                  });
                }
              },
            ),
            _buildDropdownTile(
              title: l10n?.distanceUnit ?? 'Distance Unit',
              subtitle: l10n?.distanceUnitDescription ?? 'Unit for distance measurements',
              value: _selectedDistanceUnit,
              items: _distanceUnits,
              icon: Icons.straighten,
              onChanged: (String? newValue) {
                if (newValue != null) {
                  setState(() {
                    _selectedDistanceUnit = newValue;
                  });
                }
              },
            ),
            _buildDropdownTile(
              title: l10n?.perimeterUnit ?? 'Perimeter Unit',
              subtitle: l10n?.perimeterUnitDescription ?? 'Unit for perimeter measurements',
              value: _selectedPerimeterUnit,
              items: _perimeterUnits,
              icon: Icons.border_outer,
              onChanged: (String? newValue) {
                if (newValue != null) {
                  setState(() {
                    _selectedPerimeterUnit = newValue;
                  });
                }
              },
            ),

            // Others Section
            _buildSectionHeader(l10n?.others ?? 'Others'),
            _buildActionTile(
              title: l10n?.howToUse ?? 'How to Use',
              subtitle: l10n?.howToUseDescription ?? 'Learn how to use the app features',
              icon: Icons.help_outline,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const HelpTutorialPage(),
                  ),
                );
              },
            ),
            _buildActionTile(
              title: l10n?.feedback ?? 'Feedback',
              subtitle: l10n?.feedbackDescription ?? 'Send us your feedback and suggestions',
              icon: Icons.feedback_outlined,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const FeedbackPage(),
                  ),
                );
              },
            ),

            // Account Section
            _buildSectionHeader('Account'),
            Card(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              child: ListTile(
                leading: const Icon(Icons.logout, color: Colors.red),
                title: const Text(
                  'Logout',
                  style: TextStyle(color: Colors.red),
                ),
                subtitle: const Text('Sign out from your account'),
                trailing: const Icon(Icons.arrow_forward_ios, size: 16, color: Colors.red),
                onTap: _logout,
              ),
            ),

            const SizedBox(height: 16),

            // Banner Ad
            const BannerAdWidget(
              margin: EdgeInsets.all(16.0),
              alwaysShow: true,
            ),

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}
