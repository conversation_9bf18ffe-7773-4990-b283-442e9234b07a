import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import '../../data/services/rewarded_interstitial_ad_manager.dart';
import 'reward_dialog.dart';

class RewardedAdButton extends StatefulWidget {
  final String buttonText;
  final IconData? icon;
  final VoidCallback? onRewardEarned;
  final Color? backgroundColor;
  final Color? textColor;
  final EdgeInsets? margin;
  final double? width;

  const RewardedAdButton({
    super.key,
    this.buttonText = 'Watch Ad for Reward',
    this.icon = Icons.play_circle_fill,
    this.onRewardEarned,
    this.backgroundColor,
    this.textColor,
    this.margin,
    this.width,
  });

  @override
  State<RewardedAdButton> createState() => _RewardedAdButtonState();
}

class _RewardedAdButtonState extends State<RewardedAdButton> {
  final RewardedInterstitialAdManager _adManager = RewardedInterstitialAdManager();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final isAdAvailable = _adManager.isAdAvailable;
    
    return Container(
      margin: widget.margin ?? const EdgeInsets.all(8.0),
      width: widget.width,
      child: ElevatedButton.icon(
        onPressed: _isLoading ? null : (isAdAvailable ? _showRewardedAd : null),
        icon: _isLoading 
            ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : Icon(
                widget.icon,
                size: 20,
              ),
        label: Text(
          _isLoading 
              ? 'Loading...' 
              : isAdAvailable 
                  ? widget.buttonText 
                  : 'Ad Not Ready',
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: widget.backgroundColor ?? 
              (isAdAvailable ? Colors.green : Colors.grey),
          foregroundColor: widget.textColor ?? Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
          elevation: isAdAvailable ? 3 : 1,
        ),
      ),
    );
  }

  void _showRewardedAd() {
    setState(() {
      _isLoading = true;
    });

    _adManager.showAdIfAvailable(
      onUserEarnedReward: (AdWithoutView ad, RewardItem reward) {
        print('User earned reward: ${reward.amount} ${reward.type}');
        
        // Show reward dialog
        if (mounted) {
          RewardDialog.show(
            context,
            reward: reward,
            onContinue: () {
              widget.onRewardEarned?.call();
            },
          );
        }
      },
      onAdClosed: () {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      },
    );
  }
}

// Predefined reward button styles
class RewardedAdButtons {
  static Widget premium({
    VoidCallback? onRewardEarned,
    EdgeInsets? margin,
  }) {
    return RewardedAdButton(
      buttonText: 'Watch Ad for Premium Features',
      icon: Icons.diamond,
      backgroundColor: Colors.purple,
      onRewardEarned: onRewardEarned,
      margin: margin,
    );
  }

  static Widget removeAds({
    VoidCallback? onRewardEarned,
    EdgeInsets? margin,
  }) {
    return RewardedAdButton(
      buttonText: 'Remove Ads Temporarily',
      icon: Icons.block,
      backgroundColor: Colors.red,
      onRewardEarned: onRewardEarned,
      margin: margin,
    );
  }

  static Widget extraFeatures({
    VoidCallback? onRewardEarned,
    EdgeInsets? margin,
  }) {
    return RewardedAdButton(
      buttonText: 'Unlock Extra Features',
      icon: Icons.lock_open,
      backgroundColor: Colors.orange,
      onRewardEarned: onRewardEarned,
      margin: margin,
    );
  }

  static Widget coins({
    VoidCallback? onRewardEarned,
    EdgeInsets? margin,
  }) {
    return RewardedAdButton(
      buttonText: 'Earn Coins',
      icon: Icons.monetization_on,
      backgroundColor: Colors.amber,
      onRewardEarned: onRewardEarned,
      margin: margin,
    );
  }

  static Widget bonus({
    VoidCallback? onRewardEarned,
    EdgeInsets? margin,
  }) {
    return RewardedAdButton(
      buttonText: 'Get Bonus Reward',
      icon: Icons.card_giftcard,
      backgroundColor: Colors.teal,
      onRewardEarned: onRewardEarned,
      margin: margin,
    );
  }
}
