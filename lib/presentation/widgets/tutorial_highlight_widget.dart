import 'package:flutter/material.dart';

class TutorialHighlightWidget extends StatefulWidget {
  final Widget child;
  final String tutorialMessage;
  final String actionText;
  final bool showTutorial;
  final VoidCallback? onTutorialTap;
  final Color highlightColor;
  final TutorialPosition position;

  const TutorialHighlightWidget({
    super.key,
    required this.child,
    required this.tutorialMessage,
    this.actionText = "Tap here",
    this.showTutorial = false,
    this.onTutorialTap,
    this.highlightColor = Colors.orange,
    this.position = TutorialPosition.top,
  });

  @override
  State<TutorialHighlightWidget> createState() => _TutorialHighlightWidgetState();
}

class _TutorialHighlightWidgetState extends State<TutorialHighlightWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _bounceController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _bounceAnimation;

  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.15,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _bounceAnimation = Tween<double>(
      begin: 0.0,
      end: 8.0,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: Curves.elasticInOut,
    ));

    if (widget.showTutorial) {
      _startAnimations();
    }
  }

  void _startAnimations() {
    _pulseController.repeat(reverse: true);
    _bounceController.repeat(reverse: true);
  }

  void _stopAnimations() {
    _pulseController.stop();
    _bounceController.stop();
  }

  @override
  void didUpdateWidget(TutorialHighlightWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.showTutorial != oldWidget.showTutorial) {
      if (widget.showTutorial) {
        _startAnimations();
      } else {
        _stopAnimations();
      }
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _bounceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.showTutorial) {
      return widget.child;
    }

    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: widget.highlightColor,
                width: 3,
              ),
              boxShadow: [
                BoxShadow(
                  color: widget.highlightColor.withOpacity(0.4),
                  blurRadius: 15,
                  spreadRadius: 3,
                ),
              ],
            ),
            child: widget.child,
          ),
        );
      },
    );
  }


}

enum TutorialPosition {
  top,
  bottom,
  left,
  right,
}
