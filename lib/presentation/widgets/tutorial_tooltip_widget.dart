import 'package:flutter/material.dart';
import 'dart:math' as math;

class TutorialTooltipWidget extends StatefulWidget {
  final Widget child;
  final String message;
  final String actionText;
  final VoidCallback? onTap;
  final bool showArrow;
  final TooltipPosition position;
  final Color color;

  const TutorialTooltipWidget({
    super.key,
    required this.child,
    required this.message,
    this.actionText = "Click here",
    this.onTap,
    this.showArrow = true,
    this.position = TooltipPosition.top,
    this.color = Colors.orange,
  });

  @override
  State<TutorialTooltipWidget> createState() => _TutorialTooltipWidgetState();
}

class _TutorialTooltipWidgetState extends State<TutorialTooltipWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _bounceController;
  late AnimationController _arrowController;
  
  late Animation<double> _pulseAnimation;
  late Animation<double> _bounceAnimation;
  late Animation<double> _arrowAnimation;

  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _arrowController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _bounceAnimation = Tween<double>(
      begin: 0.0,
      end: 10.0,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: Curves.elasticInOut,
    ));

    _arrowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _arrowController,
      curve: Curves.elasticOut,
    ));

    // Start animations
    _pulseController.repeat(reverse: true);
    _bounceController.repeat(reverse: true);
    _arrowController.forward();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _bounceController.dispose();
    _arrowController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        // Just return the child widget without any tooltip overlays
        widget.child,
        // All tooltip elements disabled
      ],
    );
  }

  double _getArrowTop() {
    switch (widget.position) {
      case TooltipPosition.top:
        return -40;
      case TooltipPosition.bottom:
        return 60;
      case TooltipPosition.left:
        return 15;
      case TooltipPosition.right:
        return 15;
    }
  }

  double _getArrowLeft() {
    switch (widget.position) {
      case TooltipPosition.top:
        return 20;
      case TooltipPosition.bottom:
        return 20;
      case TooltipPosition.left:
        return -40;
      case TooltipPosition.right:
        return 60;
    }
  }

  Offset _getArrowOffset() {
    switch (widget.position) {
      case TooltipPosition.top:
        return Offset(0, _bounceAnimation.value);
      case TooltipPosition.bottom:
        return Offset(0, -_bounceAnimation.value);
      case TooltipPosition.left:
        return Offset(_bounceAnimation.value, 0);
      case TooltipPosition.right:
        return Offset(-_bounceAnimation.value, 0);
    }
  }

  double _getArrowRotation() {
    switch (widget.position) {
      case TooltipPosition.top:
        return math.pi;
      case TooltipPosition.bottom:
        return 0;
      case TooltipPosition.left:
        return math.pi / 2;
      case TooltipPosition.right:
        return -math.pi / 2;
    }
  }

  double _getTooltipTop() {
    switch (widget.position) {
      case TooltipPosition.top:
        return -120;
      case TooltipPosition.bottom:
        return 80;
      case TooltipPosition.left:
        return -30;
      case TooltipPosition.right:
        return -30;
    }
  }

  double _getTooltipLeft() {
    switch (widget.position) {
      case TooltipPosition.top:
        return -50;
      case TooltipPosition.bottom:
        return -50;
      case TooltipPosition.left:
        return -220;
      case TooltipPosition.right:
        return 80;
    }
  }
}

class ArrowPainter extends CustomPainter {
  final Color color;

  ArrowPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    
    // Draw arrow pointing down
    path.moveTo(size.width * 0.5, size.height);
    path.lineTo(size.width * 0.2, size.height * 0.3);
    path.lineTo(size.width * 0.4, size.height * 0.3);
    path.lineTo(size.width * 0.4, 0);
    path.lineTo(size.width * 0.6, 0);
    path.lineTo(size.width * 0.6, size.height * 0.3);
    path.lineTo(size.width * 0.8, size.height * 0.3);
    path.close();

    canvas.drawPath(path, paint);
    
    // Add glow effect
    final glowPaint = Paint()
      ..color = color.withOpacity(0.3)
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3);
    
    canvas.drawPath(path, glowPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

enum TooltipPosition {
  top,
  bottom,
  left,
  right,
}
