import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import '../../data/services/admob_service.dart';
import '../../data/services/pro_status_manager.dart';

class BannerAdWidget extends StatefulWidget {
  final AdSize adSize;
  final EdgeInsets? margin;
  final bool alwaysShow;

  const BannerAdWidget({
    super.key,
    this.adSize = AdSize.banner,
    this.margin,
    this.alwaysShow = true, // Always show ads by default
  });

  @override
  State<BannerAdWidget> createState() => _BannerAdWidgetState();
}

class _BannerAdWidgetState extends State<BannerAdWidget> {
  BannerAd? _bannerAd;
  bool _isAdLoaded = false;
  bool _isAdFailed = false;
  int _retryAttempt = 0;
  static const int _maxRetryAttempts = 3;
  final ProStatusManager _proManager = ProStatusManager();

  @override
  void initState() {
    super.initState();
    _loadAd();
  }

  void _loadAd() {
    // Don't load ads for Pro users
    if (_proManager.isProUser) {
      return;
    }

    _bannerAd?.dispose(); // Dispose previous ad if exists

    _bannerAd = AdMobService.createBannerAd(
      adSize: widget.adSize,
      onAdLoaded: (Ad ad) {
        print('Banner ad loaded successfully');
        if (mounted) {
          setState(() {
            _isAdLoaded = true;
            _isAdFailed = false;
            _retryAttempt = 0; // Reset retry counter on success
          });
        }
      },
      onAdFailedToLoad: (Ad ad, LoadAdError error) {
        print('Banner ad failed to load: $error');
        ad.dispose();
        if (mounted) {
          setState(() {
            _isAdLoaded = false;
            _isAdFailed = true;
          });

          // Retry loading ad with exponential backoff
          if (_retryAttempt < _maxRetryAttempts) {
            _retryAttempt++;
            final retryDelay = Duration(seconds: _retryAttempt * 2);
            print('Retrying ad load in ${retryDelay.inSeconds} seconds (attempt $_retryAttempt)');

            Future.delayed(retryDelay, () {
              if (mounted) {
                _loadAd();
              }
            });
          }
        }
      },
    );

    _bannerAd?.load();
  }

  @override
  void dispose() {
    _bannerAd?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Don't show ads for Pro users
    if (_proManager.isProUser) {
      return const SizedBox.shrink();
    }

    // Always show ad space to maintain consistent layout
    if (widget.alwaysShow) {
      if (_isAdLoaded && _bannerAd != null) {
        // Show the actual ad
        return Container(
          margin: widget.margin,
          height: widget.adSize.height.toDouble(),
          width: widget.adSize.width.toDouble(),
          child: AdWidget(ad: _bannerAd!),
        );
      } else {
        // Show loading placeholder or retry message
        return Container(
          margin: widget.margin,
          height: widget.adSize.height.toDouble(),
          width: widget.adSize.width.toDouble(),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!, width: 1),
          ),
          child: Center(
            child: _isAdFailed && _retryAttempt >= _maxRetryAttempts
                ? Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.ads_click, color: Colors.grey[400], size: 20),
                      const SizedBox(height: 4),
                      Text(
                        'Ad Space',
                        style: TextStyle(
                          color: Colors.grey[500],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  )
                : Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.grey[400],
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Loading Ad...',
                        style: TextStyle(
                          color: Colors.grey[500],
                          fontSize: 10,
                        ),
                      ),
                    ],
                  ),
          ),
        );
      }
    }

    // If alwaysShow is false, hide when failed
    if (_isAdFailed) {
      return const SizedBox.shrink();
    }

    if (!_isAdLoaded || _bannerAd == null) {
      return Container(
        margin: widget.margin,
        height: widget.adSize.height.toDouble(),
        width: widget.adSize.width.toDouble(),
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Container(
      margin: widget.margin,
      height: widget.adSize.height.toDouble(),
      width: widget.adSize.width.toDouble(),
      child: AdWidget(ad: _bannerAd!),
    );
  }
}
