import 'package:flutter/material.dart';
import '../../domain/models/camera_settings_model.dart';

class CameraSettingsPanel extends StatefulWidget {
  final CameraSettingsModel settings;
  final Function(CameraSettingsModel) onSettingsChanged;

  const CameraSettingsPanel({
    super.key,
    required this.settings,
    required this.onSettingsChanged,
  });

  @override
  State<CameraSettingsPanel> createState() => _CameraSettingsPanelState();
}

class _CameraSettingsPanelState extends State<CameraSettingsPanel>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: const BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.symmetric(vertical: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[400],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Title
          const Padding(
            padding: EdgeInsets.all(16),
            child: Text(
              'Camera Settings',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          
          // Tab Bar
          TabBar(
            controller: _tabController,
            indicatorColor: Colors.white,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.grey,
            tabs: const [
              Tab(text: 'General'),
              Tab(text: 'Quality'),
              Tab(text: 'Advanced'),
              Tab(text: 'Video'),
            ],
          ),
          
          // Tab Views
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildGeneralSettings(),
                _buildQualitySettings(),
                _buildAdvancedSettings(),
                _buildVideoSettings(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGeneralSettings() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSwitchTile(
          title: 'Grid Lines',
          subtitle: 'Show composition grid',
          value: widget.settings.isGridEnabled,
          onChanged: (value) => _updateSettings(
            widget.settings.copyWith(isGridEnabled: value),
          ),
        ),
        
        if (widget.settings.isGridEnabled)
          _buildDropdownTile<GridType>(
            title: 'Grid Type',
            value: widget.settings.gridType,
            items: GridType.values,
            itemBuilder: (type) => _getGridTypeText(type),
            onChanged: (value) => _updateSettings(
              widget.settings.copyWith(gridType: value),
            ),
          ),
        
        _buildDropdownTile<TimerMode>(
          title: 'Timer',
          value: widget.settings.timerMode,
          items: TimerMode.values,
          itemBuilder: (mode) => _getTimerModeText(mode),
          onChanged: (value) => _updateSettings(
            widget.settings.copyWith(timerMode: value),
          ),
        ),
        
        _buildSwitchTile(
          title: 'Level Indicator',
          subtitle: 'Show horizon level',
          value: widget.settings.isLevelEnabled,
          onChanged: (value) => _updateSettings(
            widget.settings.copyWith(isLevelEnabled: value),
          ),
        ),
        
        _buildSwitchTile(
          title: 'Histogram',
          subtitle: 'Show exposure histogram',
          value: widget.settings.isHistogramEnabled,
          onChanged: (value) => _updateSettings(
            widget.settings.copyWith(isHistogramEnabled: value),
          ),
        ),
        
        _buildSwitchTile(
          title: 'Volume Buttons as Shutter',
          subtitle: 'Use volume buttons to capture',
          value: widget.settings.isVolumeButtonsAsShutter,
          onChanged: (value) => _updateSettings(
            widget.settings.copyWith(isVolumeButtonsAsShutter: value),
          ),
        ),
        
        _buildSwitchTile(
          title: 'Camera Sounds',
          subtitle: 'Play shutter and focus sounds',
          value: widget.settings.isSoundEnabled,
          onChanged: (value) => _updateSettings(
            widget.settings.copyWith(isSoundEnabled: value),
          ),
        ),
      ],
    );
  }

  Widget _buildQualitySettings() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildDropdownTile<ImageQuality>(
          title: 'Photo Quality',
          value: widget.settings.imageQuality,
          items: ImageQuality.values,
          itemBuilder: (quality) => _getImageQualityText(quality),
          onChanged: (value) => _updateSettings(
            widget.settings.copyWith(imageQuality: value),
          ),
        ),
        
        _buildDropdownTile<VideoQuality>(
          title: 'Video Quality',
          value: widget.settings.videoQuality,
          items: VideoQuality.values,
          itemBuilder: (quality) => _getVideoQualityText(quality),
          onChanged: (value) => _updateSettings(
            widget.settings.copyWith(videoQuality: value),
          ),
        ),
        
        _buildSwitchTile(
          title: 'RAW Photos',
          subtitle: 'Save photos in RAW format (Pro)',
          value: widget.settings.isRawEnabled,
          onChanged: (value) => _updateSettings(
            widget.settings.copyWith(isRawEnabled: value),
          ),
        ),
        
        _buildSwitchTile(
          title: 'HDR',
          subtitle: 'High Dynamic Range',
          value: widget.settings.isHdrEnabled,
          onChanged: (value) => _updateSettings(
            widget.settings.copyWith(isHdrEnabled: value),
          ),
        ),
        
        _buildSwitchTile(
          title: 'Image Stabilization',
          subtitle: 'Reduce camera shake',
          value: widget.settings.isImageStabilizationEnabled,
          onChanged: (value) => _updateSettings(
            widget.settings.copyWith(isImageStabilizationEnabled: value),
          ),
        ),
        
        _buildSwitchTile(
          title: 'Noise Reduction',
          subtitle: 'Reduce image noise in low light',
          value: widget.settings.isNoiseReductionEnabled,
          onChanged: (value) => _updateSettings(
            widget.settings.copyWith(isNoiseReductionEnabled: value),
          ),
        ),
      ],
    );
  }

  Widget _buildAdvancedSettings() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildDropdownTile<FocusMode>(
          title: 'Focus Mode',
          value: widget.settings.focusMode,
          items: FocusMode.values,
          itemBuilder: (mode) => _getFocusModeText(mode),
          onChanged: (value) => _updateSettings(
            widget.settings.copyWith(focusMode: value),
          ),
        ),
        
        _buildDropdownTile<ExposureMode>(
          title: 'Exposure Mode',
          value: widget.settings.exposureMode,
          items: ExposureMode.values,
          itemBuilder: (mode) => _getExposureModeText(mode),
          onChanged: (value) => _updateSettings(
            widget.settings.copyWith(exposureMode: value),
          ),
        ),
        
        _buildDropdownTile<WhiteBalanceMode>(
          title: 'White Balance',
          value: widget.settings.whiteBalanceMode,
          items: WhiteBalanceMode.values,
          itemBuilder: (mode) => _getWhiteBalanceModeText(mode),
          onChanged: (value) => _updateSettings(
            widget.settings.copyWith(whiteBalanceMode: value),
          ),
        ),
        
        _buildSwitchTile(
          title: 'Face Detection',
          subtitle: 'Detect and focus on faces',
          value: widget.settings.isFaceDetectionEnabled,
          onChanged: (value) => _updateSettings(
            widget.settings.copyWith(isFaceDetectionEnabled: value),
          ),
        ),
        
        _buildSwitchTile(
          title: 'Smile Detection',
          subtitle: 'Auto capture when smile detected',
          value: widget.settings.isSmileDetectionEnabled,
          onChanged: (value) => _updateSettings(
            widget.settings.copyWith(isSmileDetectionEnabled: value),
          ),
        ),
        
        _buildSwitchTile(
          title: 'Location Tagging',
          subtitle: 'Add GPS location to photos',
          value: widget.settings.isLocationTaggingEnabled,
          onChanged: (value) => _updateSettings(
            widget.settings.copyWith(isLocationTaggingEnabled: value),
          ),
        ),
      ],
    );
  }

  Widget _buildVideoSettings() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildDropdownTile<int>(
          title: 'Frame Rate',
          value: widget.settings.videoFrameRate,
          items: const [24, 30, 60, 120],
          itemBuilder: (fps) => '${fps} FPS',
          onChanged: (value) => _updateSettings(
            widget.settings.copyWith(videoFrameRate: value),
          ),
        ),
        
        _buildSwitchTile(
          title: 'Video Stabilization',
          subtitle: 'Reduce camera shake in videos',
          value: widget.settings.isVideoStabilizationEnabled,
          onChanged: (value) => _updateSettings(
            widget.settings.copyWith(isVideoStabilizationEnabled: value),
          ),
        ),
        
        _buildSwitchTile(
          title: 'Slow Motion',
          subtitle: 'Enable slow motion recording',
          value: widget.settings.isSlowMotionEnabled,
          onChanged: (value) => _updateSettings(
            widget.settings.copyWith(isSlowMotionEnabled: value),
          ),
        ),
        
        _buildSwitchTile(
          title: 'Time Lapse',
          subtitle: 'Enable time lapse recording',
          value: widget.settings.isTimelapseEnabled,
          onChanged: (value) => _updateSettings(
            widget.settings.copyWith(isTimelapseEnabled: value),
          ),
        ),
        
        if (widget.settings.isTimelapseEnabled)
          _buildSliderTile(
            title: 'Time Lapse Interval',
            subtitle: 'Seconds between frames',
            value: widget.settings.timelapseInterval,
            min: 0.5,
            max: 10.0,
            divisions: 19,
            onChanged: (value) => _updateSettings(
              widget.settings.copyWith(timelapseInterval: value),
            ),
            displayValue: '${widget.settings.timelapseInterval.toStringAsFixed(1)}s',
          ),
      ],
    );
  }

  Widget _buildSwitchTile({
    required String title,
    String? subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return ListTile(
      title: Text(title, style: const TextStyle(color: Colors.white)),
      subtitle: subtitle != null 
          ? Text(subtitle, style: TextStyle(color: Colors.grey[400]))
          : null,
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: Colors.white,
      ),
    );
  }

  Widget _buildDropdownTile<T>({
    required String title,
    required T value,
    required List<T> items,
    required String Function(T) itemBuilder,
    required Function(T) onChanged,
  }) {
    return ListTile(
      title: Text(title, style: const TextStyle(color: Colors.white)),
      trailing: DropdownButton<T>(
        value: value,
        dropdownColor: Colors.grey[800],
        style: const TextStyle(color: Colors.white),
        items: items.map((item) {
          return DropdownMenuItem<T>(
            value: item,
            child: Text(itemBuilder(item)),
          );
        }).toList(),
        onChanged: (newValue) {
          if (newValue != null) onChanged(newValue);
        },
      ),
    );
  }

  Widget _buildSliderTile({
    required String title,
    String? subtitle,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required Function(double) onChanged,
    required String displayValue,
  }) {
    return ListTile(
      title: Text(title, style: const TextStyle(color: Colors.white)),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (subtitle != null)
            Text(subtitle, style: TextStyle(color: Colors.grey[400])),
          Slider(
            value: value,
            min: min,
            max: max,
            divisions: divisions,
            onChanged: onChanged,
            activeColor: Colors.white,
            inactiveColor: Colors.grey,
          ),
          Text(
            displayValue,
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
        ],
      ),
    );
  }

  void _updateSettings(CameraSettingsModel newSettings) {
    widget.onSettingsChanged(newSettings);
  }

  // Helper methods for text conversion
  String _getGridTypeText(GridType type) {
    switch (type) {
      case GridType.none: return 'None';
      case GridType.rule_of_thirds: return 'Rule of Thirds';
      case GridType.square: return 'Square';
      case GridType.golden_ratio: return 'Golden Ratio';
    }
  }

  String _getTimerModeText(TimerMode mode) {
    switch (mode) {
      case TimerMode.off: return 'Off';
      case TimerMode.three_seconds: return '3 seconds';
      case TimerMode.five_seconds: return '5 seconds';
      case TimerMode.ten_seconds: return '10 seconds';
    }
  }

  String _getImageQualityText(ImageQuality quality) {
    switch (quality) {
      case ImageQuality.low: return 'Low';
      case ImageQuality.medium: return 'Medium';
      case ImageQuality.high: return 'High';
      case ImageQuality.ultra: return 'Ultra';
    }
  }

  String _getVideoQualityText(VideoQuality quality) {
    switch (quality) {
      case VideoQuality.sd_480p: return '480p SD';
      case VideoQuality.hd_720p: return '720p HD';
      case VideoQuality.fhd_1080p: return '1080p FHD';
      case VideoQuality.uhd_4k: return '4K UHD';
    }
  }

  String _getFocusModeText(FocusMode mode) {
    switch (mode) {
      case FocusMode.auto: return 'Auto';
      case FocusMode.manual: return 'Manual';
      case FocusMode.continuous: return 'Continuous';
      case FocusMode.single: return 'Single';
    }
  }

  String _getExposureModeText(ExposureMode mode) {
    switch (mode) {
      case ExposureMode.auto: return 'Auto';
      case ExposureMode.manual: return 'Manual';
      case ExposureMode.spot: return 'Spot';
      case ExposureMode.center: return 'Center';
      case ExposureMode.matrix: return 'Matrix';
    }
  }

  String _getWhiteBalanceModeText(WhiteBalanceMode mode) {
    switch (mode) {
      case WhiteBalanceMode.auto: return 'Auto';
      case WhiteBalanceMode.daylight: return 'Daylight';
      case WhiteBalanceMode.cloudy: return 'Cloudy';
      case WhiteBalanceMode.tungsten: return 'Tungsten';
      case WhiteBalanceMode.fluorescent: return 'Fluorescent';
      case WhiteBalanceMode.manual: return 'Manual';
    }
  }
}
