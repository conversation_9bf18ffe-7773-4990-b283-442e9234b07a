import 'package:flutter/material.dart';
import 'dart:math' as math;

class AnimatedPointerWidget extends StatefulWidget {
  final Offset targetPosition;
  final String message;
  final VoidCallback? onTap;
  final PointerDirection direction;
  final Color color;

  const AnimatedPointerWidget({
    super.key,
    required this.targetPosition,
    required this.message,
    this.onTap,
    this.direction = PointerDirection.bottomLeft,
    this.color = Colors.orange,
  });

  @override
  State<AnimatedPointerWidget> createState() => _AnimatedPointerWidgetState();
}

class _AnimatedPointerWidgetState extends State<AnimatedPointerWidget>
    with TickerProviderStateMixin {
  late AnimationController _bounceController;
  late AnimationController _pulseController;
  late AnimationController _arrowController;
  
  late Animation<double> _bounceAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _arrowAnimation;

  @override
  void initState() {
    super.initState();
    
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _arrowController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _bounceAnimation = Tween<double>(
      begin: 0.0,
      end: 15.0,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: Curves.elasticInOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _arrowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _arrowController,
      curve: Curves.elasticOut,
    ));

    // Start animations
    _bounceController.repeat(reverse: true);
    _pulseController.repeat(reverse: true);
    _arrowController.forward();
  }

  @override
  void dispose() {
    _bounceController.dispose();
    _pulseController.dispose();
    _arrowController.dispose();
    super.dispose();
  }

  Offset _getArrowOffset() {
    switch (widget.direction) {
      case PointerDirection.topLeft:
        return const Offset(-80, -80);
      case PointerDirection.topRight:
        return const Offset(80, -80);
      case PointerDirection.bottomLeft:
        return const Offset(-80, 80);
      case PointerDirection.bottomRight:
        return const Offset(80, 80);
      case PointerDirection.left:
        return const Offset(-100, 0);
      case PointerDirection.right:
        return const Offset(100, 0);
      case PointerDirection.top:
        return const Offset(0, -100);
      case PointerDirection.bottom:
        return const Offset(0, 100);
    }
  }

  double _getArrowRotation() {
    switch (widget.direction) {
      case PointerDirection.topLeft:
        return -math.pi / 4;
      case PointerDirection.topRight:
        return math.pi / 4;
      case PointerDirection.bottomLeft:
        return -3 * math.pi / 4;
      case PointerDirection.bottomRight:
        return 3 * math.pi / 4;
      case PointerDirection.left:
        return -math.pi / 2;
      case PointerDirection.right:
        return math.pi / 2;
      case PointerDirection.top:
        return 0;
      case PointerDirection.bottom:
        return math.pi;
    }
  }

  @override
  Widget build(BuildContext context) {
    final arrowOffset = _getArrowOffset();
    final messagePosition = widget.targetPosition + arrowOffset;

    return Stack(
      children: [
        // Target highlight circle
        Positioned(
          left: widget.targetPosition.dx - 30,
          top: widget.targetPosition.dy - 30,
          child: AnimatedBuilder(
            animation: _pulseController,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: widget.color.withOpacity(0.8),
                      width: 3,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: widget.color.withOpacity(0.3),
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),

        // Animated arrow
        Positioned(
          left: messagePosition.dx,
          top: messagePosition.dy,
          child: AnimatedBuilder(
            animation: Listenable.merge([_bounceController, _arrowController]),
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(0, _bounceAnimation.value),
                child: Transform.scale(
                  scale: _arrowAnimation.value,
                  child: Transform.rotate(
                    angle: _getArrowRotation(),
                    child: CustomPaint(
                      size: const Size(40, 40),
                      painter: ArrowPainter(color: widget.color),
                    ),
                  ),
                ),
              );
            },
          ),
        ),

        // Message bubble
        Positioned(
          left: messagePosition.dx - 60,
          top: messagePosition.dy + 50,
          child: AnimatedBuilder(
            animation: _arrowController,
            builder: (context, child) {
              return Transform.scale(
                scale: _arrowAnimation.value,
                child: GestureDetector(
                  onTap: widget.onTap,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    constraints: const BoxConstraints(maxWidth: 200),
                    decoration: BoxDecoration(
                      color: widget.color,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          widget.message,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          'Tap to continue',
                          style: TextStyle(
                            color: Colors.white70,
                            fontSize: 11,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

class ArrowPainter extends CustomPainter {
  final Color color;

  ArrowPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill
      ..strokeWidth = 3;

    final path = Path();
    
    // Draw arrow shape
    path.moveTo(size.width * 0.5, 0);
    path.lineTo(size.width * 0.8, size.height * 0.3);
    path.lineTo(size.width * 0.65, size.height * 0.3);
    path.lineTo(size.width * 0.65, size.height);
    path.lineTo(size.width * 0.35, size.height);
    path.lineTo(size.width * 0.35, size.height * 0.3);
    path.lineTo(size.width * 0.2, size.height * 0.3);
    path.close();

    canvas.drawPath(path, paint);
    
    // Add shadow
    canvas.drawShadow(path, Colors.black.withOpacity(0.3), 3, false);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

enum PointerDirection {
  topLeft,
  topRight,
  bottomLeft,
  bottomRight,
  left,
  right,
  top,
  bottom,
}
