import 'dart:async';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

class CustomVideoControls extends StatefulWidget {
  final VideoPlayerController controller;
  final VoidCallback? onToggleFullScreen;
  final bool showFullscreenButton;

  const CustomVideoControls({
    Key? key,
    required this.controller,
    this.onToggleFullScreen,
    this.showFullscreenButton = true,
  }) : super(key: key);

  @override
  State<CustomVideoControls> createState() => _CustomVideoControlsState();
}

class _CustomVideoControlsState extends State<CustomVideoControls> {
  bool _hideControls = false;
  Timer? _hideTimer;
  double _currentSliderValue = 0.0;
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_videoListener);
    _startHideTimer();
  }

  @override
  void dispose() {
    _hideTimer?.cancel();
    widget.controller.removeListener(_videoListener);
    super.dispose();
  }

  void _videoListener() {
    if (!_isDragging && mounted) {
      setState(() {
        _currentSliderValue = widget.controller.value.position.inMilliseconds.toDouble();
      });
    }
  }

  void _startHideTimer() {
    _hideTimer?.cancel();
    _hideTimer = Timer(const Duration(seconds: 3), () {
      if (mounted && !_isDragging) {
        setState(() {
          _hideControls = true;
        });
      }
    });
  }

  void _cancelHideTimer() {
    _hideTimer?.cancel();
    if (mounted) {
      setState(() {
        _hideControls = false;
      });
    }
    _startHideTimer();
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    
    return duration.inHours > 0 
        ? "$hours:$minutes:$seconds" 
        : "$minutes:$seconds";
  }

  @override
  Widget build(BuildContext context) {
    final duration = widget.controller.value.duration;
    final position = widget.controller.value.position;
    final isPlaying = widget.controller.value.isPlaying;
    final isBuffering = widget.controller.value.isBuffering;
    
    return GestureDetector(
      onTap: _cancelHideTimer,
      behavior: HitTestBehavior.opaque,
      child: Stack(
        children: [
          // Tap area for showing/hiding controls
          Positioned.fill(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _hideControls = !_hideControls;
                });
                if (!_hideControls) {
                  _startHideTimer();
                }
              },
              child: Container(color: Colors.transparent),
            ),
          ),
          
          // Play/Pause button in center
          if (!_hideControls)
            Center(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black54,
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  iconSize: 50,
                  icon: Icon(
                    isPlaying ? Icons.pause : Icons.play_arrow,
                    color: Colors.white,
                  ),
                  onPressed: () {
                    if (isPlaying) {
                      widget.controller.pause();
                    } else {
                      widget.controller.play();
                    }
                    _cancelHideTimer();
                  },
                ),
              ),
            ),
          
          // Bottom controls bar
          if (!_hideControls)
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    colors: [
                      Colors.black87,
                      Colors.transparent,
                    ],
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Progress slider
                    Slider(
                      value: _currentSliderValue.clamp(0.0, duration.inMilliseconds.toDouble()),
                      min: 0.0,
                      max: duration.inMilliseconds.toDouble(),
                      activeColor: Colors.red,
                      inactiveColor: Colors.white30,
                      onChanged: (value) {
                        setState(() {
                          _currentSliderValue = value;
                        });
                      },
                      onChangeStart: (value) {
                        _isDragging = true;
                        _hideTimer?.cancel();
                      },
                      onChangeEnd: (value) {
                        _isDragging = false;
                        widget.controller.seekTo(Duration(milliseconds: value.toInt()));
                        _startHideTimer();
                      },
                    ),
                    
                    // Time and controls row
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        children: [
                          // Current position
                          Text(
                            _formatDuration(position),
                            style: TextStyle(color: Colors.white),
                          ),
                          
                          // Spacer
                          Spacer(),
                          
                          // Rewind button
                          IconButton(
                            icon: Icon(Icons.replay_10, color: Colors.white),
                            onPressed: () {
                              final newPosition = position - Duration(seconds: 10);
                              widget.controller.seekTo(newPosition < Duration.zero ? Duration.zero : newPosition);
                              _cancelHideTimer();
                            },
                          ),
                          
                          // Play/Pause button
                          IconButton(
                            icon: Icon(
                              isPlaying ? Icons.pause : Icons.play_arrow,
                              color: Colors.white,
                            ),
                            onPressed: () {
                              if (isPlaying) {
                                widget.controller.pause();
                              } else {
                                widget.controller.play();
                              }
                              _cancelHideTimer();
                            },
                          ),
                          
                          // Forward button
                          IconButton(
                            icon: Icon(Icons.forward_10, color: Colors.white),
                            onPressed: () {
                              final newPosition = position + Duration(seconds: 10);
                              widget.controller.seekTo(newPosition > duration ? duration : newPosition);
                              _cancelHideTimer();
                            },
                          ),
                          
                          // Spacer
                          Spacer(),
                          
                          // Total duration
                          Text(
                            _formatDuration(duration),
                            style: TextStyle(color: Colors.white),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          
          // Top bar with back and fullscreen buttons
          if (!_hideControls)
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black87,
                      Colors.transparent,
                    ],
                  ),
                ),
                child: Row(
                  children: [
                    // Back button
                    IconButton(
                      icon: Icon(Icons.arrow_back, color: Colors.white),
                      onPressed: () {
                        Navigator.pop(context);
                      },
                    ),
                    
                    Spacer(),
                    
                    // Fullscreen button
                    if (widget.showFullscreenButton)
                      IconButton(
                        icon: Icon(Icons.fullscreen, color: Colors.white),
                        onPressed: widget.onToggleFullScreen,
                      ),
                  ],
                ),
              ),
            ),
          
          // Buffering indicator
          if (isBuffering)
            Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
        ],
      ),
    );
  }
}