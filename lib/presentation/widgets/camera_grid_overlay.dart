import 'package:flutter/material.dart';
import '../../domain/models/camera_settings_model.dart';

class CameraGridOverlay extends StatelessWidget {
  final GridType gridType;
  final bool isVisible;
  final Color gridColor;
  final double strokeWidth;

  const CameraGridOverlay({
    super.key,
    required this.gridType,
    this.isVisible = true,
    this.gridColor = Colors.white,
    this.strokeWidth = 0.5,
  });

  @override
  Widget build(BuildContext context) {
    if (!isVisible || gridType == GridType.none) {
      return const SizedBox.shrink();
    }

    return RepaintBoundary(
      child: CustomPaint(
        painter: GridPainter(
          gridType: gridType,
          color: gridColor.withValues(alpha: 0.5),
          strokeWidth: strokeWidth,
        ),
        child: Container(),
      ),
    );
  }
}

class GridPainter extends CustomPainter {
  final GridType gridType;
  final Color color;
  final double strokeWidth;

  GridPainter({
    required this.gridType,
    required this.color,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    switch (gridType) {
      case GridType.rule_of_thirds:
        _drawRuleOfThirds(canvas, size, paint);
        break;
      case GridType.square:
        _drawSquareGrid(canvas, size, paint);
        break;
      case GridType.golden_ratio:
        _drawGoldenRatio(canvas, size, paint);
        break;
      case GridType.none:
        break;
    }
  }

  void _drawRuleOfThirds(Canvas canvas, Size size, Paint paint) {
    // Vertical lines
    final verticalLine1 = size.width / 3;
    final verticalLine2 = size.width * 2 / 3;
    
    canvas.drawLine(
      Offset(verticalLine1, 0),
      Offset(verticalLine1, size.height),
      paint,
    );
    
    canvas.drawLine(
      Offset(verticalLine2, 0),
      Offset(verticalLine2, size.height),
      paint,
    );

    // Horizontal lines
    final horizontalLine1 = size.height / 3;
    final horizontalLine2 = size.height * 2 / 3;
    
    canvas.drawLine(
      Offset(0, horizontalLine1),
      Offset(size.width, horizontalLine1),
      paint,
    );
    
    canvas.drawLine(
      Offset(0, horizontalLine2),
      Offset(size.width, horizontalLine2),
      paint,
    );

    // Draw intersection points
    final pointPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    const pointRadius = 2.0;
    
    // Four intersection points
    canvas.drawCircle(Offset(verticalLine1, horizontalLine1), pointRadius, pointPaint);
    canvas.drawCircle(Offset(verticalLine2, horizontalLine1), pointRadius, pointPaint);
    canvas.drawCircle(Offset(verticalLine1, horizontalLine2), pointRadius, pointPaint);
    canvas.drawCircle(Offset(verticalLine2, horizontalLine2), pointRadius, pointPaint);
  }

  void _drawSquareGrid(Canvas canvas, Size size, Paint paint) {
    final smallerDimension = size.width < size.height ? size.width : size.height;
    final gridSize = smallerDimension / 4;

    // Vertical lines
    for (int i = 1; i < 4; i++) {
      final x = gridSize * i;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // Horizontal lines
    for (int i = 1; i < 4; i++) {
      final y = gridSize * i;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  void _drawGoldenRatio(Canvas canvas, Size size, Paint paint) {
    const goldenRatio = 1.618;

    // Vertical lines based on golden ratio
    final verticalLine1 = size.width / goldenRatio;
    final verticalLine2 = size.width - verticalLine1;

    canvas.drawLine(
      Offset(verticalLine1, 0),
      Offset(verticalLine1, size.height),
      paint,
    );

    canvas.drawLine(
      Offset(verticalLine2, 0),
      Offset(verticalLine2, size.height),
      paint,
    );

    // Horizontal lines based on golden ratio
    final horizontalLine1 = size.height / goldenRatio;
    final horizontalLine2 = size.height - horizontalLine1;

    canvas.drawLine(
      Offset(0, horizontalLine1),
      Offset(size.width, horizontalLine1),
      paint,
    );

    canvas.drawLine(
      Offset(0, horizontalLine2),
      Offset(size.width, horizontalLine2),
      paint,
    );

    // Simplified golden ratio points instead of complex spiral
    final pointPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    const pointRadius = 2.0;

    // Golden ratio intersection points
    canvas.drawCircle(Offset(verticalLine1, horizontalLine1), pointRadius, pointPaint);
    canvas.drawCircle(Offset(verticalLine2, horizontalLine1), pointRadius, pointPaint);
    canvas.drawCircle(Offset(verticalLine1, horizontalLine2), pointRadius, pointPaint);
    canvas.drawCircle(Offset(verticalLine2, horizontalLine2), pointRadius, pointPaint);
  }



  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is GridPainter &&
        (oldDelegate.gridType != gridType ||
         oldDelegate.color != color ||
         oldDelegate.strokeWidth != strokeWidth);
  }
}

class FocusIndicator extends StatefulWidget {
  final Offset? focusPoint;
  final bool isVisible;
  final Color color;

  const FocusIndicator({
    super.key,
    this.focusPoint,
    this.isVisible = false,
    this.color = Colors.white,
  });

  @override
  State<FocusIndicator> createState() => _FocusIndicatorState();
}

class _FocusIndicatorState extends State<FocusIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _opacityAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.5, 1.0, curve: Curves.easeOut),
    ));
  }

  @override
  void didUpdateWidget(FocusIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isVisible && widget.focusPoint != null) {
      _animationController.reset();
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible || widget.focusPoint == null) {
      return const SizedBox.shrink();
    }

    return Positioned(
      left: widget.focusPoint!.dx - 30,
      top: widget.focusPoint!.dy - 30,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Opacity(
              opacity: _opacityAnimation.value,
              child: Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: widget.color,
                    width: 2,
                  ),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.center_focus_strong,
                  color: widget.color,
                  size: 20,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
