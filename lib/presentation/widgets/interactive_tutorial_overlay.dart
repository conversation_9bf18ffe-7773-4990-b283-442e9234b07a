import 'package:flutter/material.dart';
import 'tutorial_tooltip_widget.dart';

class InteractiveTutorialOverlay extends StatefulWidget {
  final List<TutorialStep> steps;
  final VoidCallback onComplete;
  final VoidCallback? onSkip;

  const InteractiveTutorialOverlay({
    super.key,
    required this.steps,
    required this.onComplete,
    this.onSkip,
  });

  @override
  State<InteractiveTutorialOverlay> createState() => _InteractiveTutorialOverlayState();
}

class _InteractiveTutorialOverlayState extends State<InteractiveTutorialOverlay>
    with TickerProviderStateMixin {
  int _currentStep = 0;
  late AnimationController _overlayController;
  late Animation<double> _overlayAnimation;

  @override
  void initState() {
    super.initState();
    _overlayController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _overlayAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _overlayController,
      curve: Curves.easeInOut,
    ));

    _overlayController.forward();
  }

  @override
  void dispose() {
    _overlayController.dispose();
    super.dispose();
  }

  void _nextStep() {
    if (_currentStep < widget.steps.length - 1) {
      setState(() {
        _currentStep++;
      });
    } else {
      widget.onComplete();
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Tutorial overlay disabled - return empty container
    return Container();
  }
}

class TutorialArrowWidget extends StatefulWidget {
  final ArrowDirection direction;
  final Color color;
  final String message;

  const TutorialArrowWidget({
    super.key,
    required this.direction,
    required this.color,
    required this.message,
  });

  @override
  State<TutorialArrowWidget> createState() => _TutorialArrowWidgetState();
}

class _TutorialArrowWidgetState extends State<TutorialArrowWidget>
    with TickerProviderStateMixin {
  late AnimationController _bounceController;
  late Animation<double> _bounceAnimation;

  @override
  void initState() {
    super.initState();
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _bounceAnimation = Tween<double>(
      begin: 0.0,
      end: 15.0,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: Curves.elasticInOut,
    ));

    _bounceController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _bounceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _bounceController,
      builder: (context, child) {
        return Transform.translate(
          offset: _getBounceOffset(),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Smaller arrow
              CustomPaint(
                size: const Size(24, 24),
                painter: ArrowPainter(
                  color: widget.color,
                  direction: widget.direction,
                ),
              ),
              const SizedBox(height: 4),
              // Compact message
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: widget.color,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  widget.message,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Offset _getBounceOffset() {
    switch (widget.direction) {
      case ArrowDirection.down:
        return Offset(0, -_bounceAnimation.value);
      case ArrowDirection.up:
        return Offset(0, _bounceAnimation.value);
      case ArrowDirection.left:
        return Offset(_bounceAnimation.value, 0);
      case ArrowDirection.right:
        return Offset(-_bounceAnimation.value, 0);
    }
  }
}

class ArrowPainter extends CustomPainter {
  final Color color;
  final ArrowDirection direction;

  ArrowPainter({required this.color, required this.direction});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    
    switch (direction) {
      case ArrowDirection.down:
        path.moveTo(size.width * 0.5, size.height);
        path.lineTo(size.width * 0.2, size.height * 0.6);
        path.lineTo(size.width * 0.4, size.height * 0.6);
        path.lineTo(size.width * 0.4, 0);
        path.lineTo(size.width * 0.6, 0);
        path.lineTo(size.width * 0.6, size.height * 0.6);
        path.lineTo(size.width * 0.8, size.height * 0.6);
        break;
      case ArrowDirection.up:
        path.moveTo(size.width * 0.5, 0);
        path.lineTo(size.width * 0.2, size.height * 0.4);
        path.lineTo(size.width * 0.4, size.height * 0.4);
        path.lineTo(size.width * 0.4, size.height);
        path.lineTo(size.width * 0.6, size.height);
        path.lineTo(size.width * 0.6, size.height * 0.4);
        path.lineTo(size.width * 0.8, size.height * 0.4);
        break;
      case ArrowDirection.left:
        path.moveTo(0, size.height * 0.5);
        path.lineTo(size.width * 0.4, size.height * 0.2);
        path.lineTo(size.width * 0.4, size.height * 0.4);
        path.lineTo(size.width, size.height * 0.4);
        path.lineTo(size.width, size.height * 0.6);
        path.lineTo(size.width * 0.4, size.height * 0.6);
        path.lineTo(size.width * 0.4, size.height * 0.8);
        break;
      case ArrowDirection.right:
        path.moveTo(size.width, size.height * 0.5);
        path.lineTo(size.width * 0.6, size.height * 0.2);
        path.lineTo(size.width * 0.6, size.height * 0.4);
        path.lineTo(0, size.height * 0.4);
        path.lineTo(0, size.height * 0.6);
        path.lineTo(size.width * 0.6, size.height * 0.6);
        path.lineTo(size.width * 0.6, size.height * 0.8);
        break;
    }
    
    path.close();
    canvas.drawPath(path, paint);
    
    // Add glow effect
    canvas.drawShadow(path, color.withOpacity(0.5), 5, false);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class TutorialStep {
  final String title;
  final String description;
  final String actionText;
  final IconData icon;
  final Color highlightColor;
  final Rect? targetRect;
  final Offset arrowPosition;
  final ArrowDirection arrowDirection;

  TutorialStep({
    required this.title,
    required this.description,
    required this.actionText,
    required this.icon,
    required this.highlightColor,
    this.targetRect,
    required this.arrowPosition,
    required this.arrowDirection,
  });
}

enum ArrowDirection {
  up,
  down,
  left,
  right,
}
