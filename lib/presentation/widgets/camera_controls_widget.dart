import 'package:flutter/material.dart';
import '../../domain/models/camera_settings_model.dart';

class CameraControlsWidget extends StatelessWidget {
  final CameraSettingsModel settings;
  final Function(CameraSettingsModel) onSettingsChanged;
  final VoidCallback? onCapturePressed;
  final VoidCallback? onSwitchCamera;
  final VoidCallback? onFlashToggle;
  final bool isRecording;
  final Duration recordingDuration;
  final double currentZoom;
  final double minZoom;
  final double maxZoom;
  final Function(double) onZoomChanged;

  const CameraControlsWidget({
    super.key,
    required this.settings,
    required this.onSettingsChanged,
    this.onCapturePressed,
    this.onSwitchCamera,
    this.onFlashToggle,
    this.isRecording = false,
    this.recordingDuration = Duration.zero,
    this.currentZoom = 1.0,
    this.minZoom = 1.0,
    this.maxZoom = 8.0,
    required this.onZoomChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Top Controls Bar
        _buildTopControls(),
        
        const Spacer(),
        
        // Camera Mode Selector
        _buildCameraModeSelector(),
        
        const SizedBox(height: 16),
        
        // Manual Controls (when in Pro mode)
        if (settings.cameraMode == CameraMode.pro)
          _buildManualControls(),
        
        // Zoom Control
        _buildZoomControl(),
        
        const SizedBox(height: 16),
        
        // Bottom Controls
        _buildBottomControls(),
      ],
    );
  }

  Widget _buildTopControls() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Settings Button
          _buildControlButton(
            icon: Icons.settings,
            onPressed: () => _showSettingsPanel(),
          ),
          
          // Timer Display
          if (isRecording)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.fiber_manual_record, color: Colors.white, size: 12),
                  const SizedBox(width: 4),
                  Text(
                    _formatDuration(recordingDuration),
                    style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ),
          
          // Flash Button
          _buildControlButton(
            icon: _getFlashIcon(),
            onPressed: onFlashToggle,
          ),
        ],
      ),
    );
  }

  Widget _buildCameraModeSelector() {
    return Container(
      height: 40,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: CameraMode.values.length,
        itemBuilder: (context, index) {
          final mode = CameraMode.values[index];
          final isSelected = settings.cameraMode == mode;
          
          return GestureDetector(
            onTap: () => _updateSettings(settings.copyWith(cameraMode: mode)),
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 8),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: isSelected ? Colors.white.withOpacity(0.3) : Colors.transparent,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: isSelected ? Colors.white : Colors.white.withOpacity(0.5),
                  width: 1,
                ),
              ),
              child: Text(
                _getCameraModeText(mode),
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildManualControls() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // ISO Control
          _buildSliderControl(
            label: 'ISO',
            value: settings.iso.toDouble(),
            min: 100,
            max: 3200,
            divisions: 10,
            onChanged: (value) => _updateSettings(
              settings.copyWith(iso: value.round()),
            ),
            displayValue: settings.iso.toString(),
          ),
          
          // Exposure Compensation
          _buildSliderControl(
            label: 'EV',
            value: settings.exposureCompensation,
            min: -2.0,
            max: 2.0,
            divisions: 40,
            onChanged: (value) => _updateSettings(
              settings.copyWith(exposureCompensation: value),
            ),
            displayValue: settings.exposureCompensation.toStringAsFixed(1),
          ),
          
          // White Balance Temperature
          _buildSliderControl(
            label: 'WB',
            value: settings.whiteBalanceTemperature.toDouble(),
            min: 2000,
            max: 8000,
            divisions: 30,
            onChanged: (value) => _updateSettings(
              settings.copyWith(whiteBalanceTemperature: value.round()),
            ),
            displayValue: '${settings.whiteBalanceTemperature}K',
          ),
        ],
      ),
    );
  }

  Widget _buildZoomControl() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 32),
      child: Row(
        children: [
          Text(
            '${minZoom.toStringAsFixed(1)}x',
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
          Expanded(
            child: Slider(
              value: currentZoom,
              min: minZoom,
              max: maxZoom,
              onChanged: onZoomChanged,
              activeColor: Colors.white,
              inactiveColor: Colors.white.withOpacity(0.3),
            ),
          ),
          Text(
            '${maxZoom.toStringAsFixed(1)}x',
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomControls() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Camera Switch
          _buildControlButton(
            icon: Icons.flip_camera_ios,
            onPressed: onSwitchCamera,
            size: 30,
          ),
          
          // Capture Button
          GestureDetector(
            onTap: onCapturePressed,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.black38,
                shape: BoxShape.circle,
                border: Border.all(
                  color: isRecording ? Colors.red : Colors.white,
                  width: 3,
                ),
              ),
              child: Center(
                child: Container(
                  width: 65,
                  height: 65,
                  decoration: BoxDecoration(
                    color: isRecording ? Colors.red : Colors.white,
                    shape: settings.cameraMode == CameraMode.video ? 
                      BoxShape.rectangle : BoxShape.circle,
                    borderRadius: settings.cameraMode == CameraMode.video ? 
                      BorderRadius.circular(isRecording ? 15 : 65) : null,
                  ),
                ),
              ),
            ),
          ),
          
          // Gallery Button
          _buildControlButton(
            icon: Icons.photo_library,
            onPressed: () => _openGallery(),
            size: 30,
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    VoidCallback? onPressed,
    double size = 24,
  }) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.black38,
        shape: BoxShape.circle,
      ),
      child: IconButton(
        icon: Icon(icon, color: Colors.white, size: size),
        onPressed: onPressed,
      ),
    );
  }

  Widget _buildSliderControl({
    required String label,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required Function(double) onChanged,
    required String displayValue,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 40,
            child: Text(
              label,
              style: const TextStyle(color: Colors.white, fontSize: 12),
            ),
          ),
          Expanded(
            child: Slider(
              value: value,
              min: min,
              max: max,
              divisions: divisions,
              onChanged: onChanged,
              activeColor: Colors.white,
              inactiveColor: Colors.white.withOpacity(0.3),
            ),
          ),
          SizedBox(
            width: 50,
            child: Text(
              displayValue,
              style: const TextStyle(color: Colors.white, fontSize: 12),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getFlashIcon() {
    // This would be determined by current flash mode
    return Icons.flash_auto;
  }

  String _getCameraModeText(CameraMode mode) {
    switch (mode) {
      case CameraMode.photo:
        return 'Photo';
      case CameraMode.video:
        return 'Video';
      case CameraMode.portrait:
        return 'Portrait';
      case CameraMode.night:
        return 'Night';
      case CameraMode.pro:
        return 'Pro';
    }
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  void _updateSettings(CameraSettingsModel newSettings) {
    onSettingsChanged(newSettings);
  }

  void _showSettingsPanel() {
    // Implementation for showing advanced settings panel
  }

  void _openGallery() {
    // Implementation for opening gallery
  }
}
