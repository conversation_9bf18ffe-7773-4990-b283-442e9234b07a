import 'package:flutter/material.dart';
import 'dart:math' as math;

class InteractiveTooltipWidget extends StatefulWidget {
  final Widget child;
  final List<TooltipStep> steps;
  final VoidCallback? onComplete;
  final bool autoStart;

  const InteractiveTooltipWidget({
    super.key,
    required this.child,
    required this.steps,
    this.onComplete,
    this.autoStart = true,
  });

  @override
  State<InteractiveTooltipWidget> createState() => _InteractiveTooltipWidgetState();
}

class _InteractiveTooltipWidgetState extends State<InteractiveTooltipWidget>
    with TickerProviderStateMixin {
  int _currentStep = 0;
  bool _showTooltips = false;
  late AnimationController _arrowController;
  late AnimationController _pulseController;
  late Animation<double> _arrowAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    
    _arrowController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _arrowAnimation = Tween<double>(
      begin: 0.0,
      end: 15.0,
    ).animate(CurvedAnimation(
      parent: _arrowController,
      curve: Curves.easeInOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    if (widget.autoStart) {
      Future.delayed(const Duration(milliseconds: 500), () {
        _startTutorial();
      });
    }
  }

  @override
  void dispose() {
    _arrowController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  void _startTutorial() {
    setState(() {
      _showTooltips = true;
      _currentStep = 0;
    });
    _arrowController.repeat(reverse: true);
    _pulseController.repeat(reverse: true);
  }

  void _nextStep() {
    if (_currentStep < widget.steps.length - 1) {
      setState(() {
        _currentStep++;
      });
    } else {
      _completeTutorial();
    }
  }

  void _completeTutorial() {
    setState(() {
      _showTooltips = false;
    });
    _arrowController.stop();
    _pulseController.stop();
    widget.onComplete?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        // Tooltips disabled - no overlay shown
      ],
    );
  }

  Widget _buildTooltipOverlay() {
    final currentStep = widget.steps[_currentStep];
    
    return Material(
      color: Colors.black.withOpacity(0.7),
      child: Stack(
        children: [
          // Highlight target area
          if (currentStep.targetKey != null)
            _buildHighlight(currentStep),
          
          // Animated arrow and tooltip
          _buildAnimatedTooltip(currentStep),
        ],
      ),
    );
  }

  Widget _buildHighlight(TooltipStep step) {
    return Positioned.fill(
      child: CustomPaint(
        painter: HighlightPainter(
          targetKey: step.targetKey!,
          pulseAnimation: _pulseAnimation,
        ),
      ),
    );
  }

  Widget _buildAnimatedTooltip(TooltipStep step) {
    return AnimatedBuilder(
      animation: Listenable.merge([_arrowController, _pulseController]),
      builder: (context, child) {
        return Positioned(
          left: step.position.dx,
          top: step.position.dy,
          child: Transform.translate(
            offset: Offset(
              step.direction == TooltipDirection.left ? -_arrowAnimation.value : 
              step.direction == TooltipDirection.right ? _arrowAnimation.value : 0,
              step.direction == TooltipDirection.top ? -_arrowAnimation.value :
              step.direction == TooltipDirection.bottom ? _arrowAnimation.value : 0,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Arrow pointing to target
                if (step.direction == TooltipDirection.bottom)
                  _buildArrow(step.direction),
                
                // Tooltip content
                Container(
                  constraints: const BoxConstraints(maxWidth: 250),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 15,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Step indicator
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: step.color.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'Step ${_currentStep + 1}',
                              style: TextStyle(
                                color: step.color,
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          const Spacer(),
                          Icon(
                            step.icon,
                            color: step.color,
                            size: 20,
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 12),
                      
                      // Action text
                      Text(
                        step.action,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: step.color,
                        ),
                      ),
                      
                      const SizedBox(height: 8),
                      
                      // Description
                      Text(
                        step.description,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.black87,
                          height: 1.4,
                        ),
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Action button
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          if (_currentStep > 0)
                            TextButton(
                              onPressed: () {
                                setState(() {
                                  _currentStep--;
                                });
                              },
                              child: const Text('Back'),
                            ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            onPressed: _nextStep,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: step.color,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20),
                              ),
                            ),
                            child: Text(
                              _currentStep == widget.steps.length - 1 ? 'Got it!' : 'Next',
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                // Arrow pointing to target
                if (step.direction == TooltipDirection.top)
                  _buildArrow(step.direction),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildArrow(TooltipDirection direction) {
    return Transform.scale(
      scale: _pulseAnimation.value,
      child: CustomPaint(
        size: const Size(20, 10),
        painter: ArrowPainter(
          direction: direction,
          color: Colors.white,
        ),
      ),
    );
  }
}

class TooltipStep {
  final String action;
  final String description;
  final Offset position;
  final TooltipDirection direction;
  final GlobalKey? targetKey;
  final IconData icon;
  final Color color;

  TooltipStep({
    required this.action,
    required this.description,
    required this.position,
    required this.direction,
    this.targetKey,
    required this.icon,
    this.color = Colors.blue,
  });
}

enum TooltipDirection { top, bottom, left, right }

class HighlightPainter extends CustomPainter {
  final GlobalKey targetKey;
  final Animation<double> pulseAnimation;

  HighlightPainter({
    required this.targetKey,
    required this.pulseAnimation,
  }) : super(repaint: pulseAnimation);

  @override
  void paint(Canvas canvas, Size size) {
    final renderBox = targetKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final targetSize = renderBox.size;
    final targetPosition = renderBox.localToGlobal(Offset.zero);

    // Draw highlight circle
    final paint = Paint()
      ..color = Colors.orange.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3 * pulseAnimation.value;

    final center = Offset(
      targetPosition.dx + targetSize.width / 2,
      targetPosition.dy + targetSize.height / 2,
    );

    final radius = math.max(targetSize.width, targetSize.height) / 2 + 10;

    canvas.drawCircle(center, radius * pulseAnimation.value, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class ArrowPainter extends CustomPainter {
  final TooltipDirection direction;
  final Color color;

  ArrowPainter({required this.direction, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();

    switch (direction) {
      case TooltipDirection.top:
        path.moveTo(size.width / 2, 0);
        path.lineTo(0, size.height);
        path.lineTo(size.width, size.height);
        break;
      case TooltipDirection.bottom:
        path.moveTo(0, 0);
        path.lineTo(size.width, 0);
        path.lineTo(size.width / 2, size.height);
        break;
      case TooltipDirection.left:
        path.moveTo(0, size.height / 2);
        path.lineTo(size.width, 0);
        path.lineTo(size.width, size.height);
        break;
      case TooltipDirection.right:
        path.moveTo(0, 0);
        path.lineTo(0, size.height);
        path.lineTo(size.width, size.height / 2);
        break;
    }

    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
