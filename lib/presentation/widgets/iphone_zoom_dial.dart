import 'package:flutter/material.dart';
import 'dart:math' as math;

class iPhoneZoomDial extends StatefulWidget {
  final double currentZoom;
  final double minZoom;
  final double maxZoom;
  final Function(double) onZoomChanged;
  final List<double> presetZooms;
  final bool isVisible;

  const iPhoneZoomDial({
    super.key,
    required this.currentZoom,
    required this.minZoom,
    required this.maxZoom,
    required this.onZoomChanged,
    this.presetZooms = const [0.5, 1.0, 2.0, 5.0],
    this.isVisible = true,
  });

  @override
  State<iPhoneZoomDial> createState() => _iPhoneZoomDialState();
}

class _iPhoneZoomDialState extends State<iPhoneZoomDial>
    with TickerProviderStateMixin {
  late AnimationController _dialController;
  late Animation<double> _dialAnimation;
  
  bool _isDragging = false;
  double _dragStartZoom = 1.0;
  
  @override
  void initState() {
    super.initState();
    
    _dialController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _dialAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _dialController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _dialController.dispose();
    super.dispose();
  }

  void _onPanStart(DragStartDetails details) {
    setState(() {
      _isDragging = true;
      _dragStartZoom = widget.currentZoom;
    });
    _dialController.forward();
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (!_isDragging) return;
    
    // Calculate zoom based on horizontal drag
    final screenWidth = MediaQuery.of(context).size.width;
    final sensitivity = 0.01; // Adjust sensitivity
    final deltaX = details.delta.dx;
    final zoomDelta = deltaX * sensitivity * (widget.maxZoom - widget.minZoom);
    
    double newZoom = widget.currentZoom + zoomDelta;
    newZoom = newZoom.clamp(widget.minZoom, widget.maxZoom);
    
    widget.onZoomChanged(newZoom);
  }

  void _onPanEnd(DragEndDetails details) {
    setState(() {
      _isDragging = false;
    });
    
    // Snap to nearest preset if close enough
    _snapToNearestPreset();
    
    _dialController.reverse();
  }

  void _snapToNearestPreset() {
    double closestPreset = widget.presetZooms.first;
    double minDistance = (widget.currentZoom - closestPreset).abs();
    
    for (double preset in widget.presetZooms) {
      double distance = (widget.currentZoom - preset).abs();
      if (distance < minDistance && distance < 0.2) {
        minDistance = distance;
        closestPreset = preset;
      }
    }
    
    if (minDistance < 0.2) {
      widget.onZoomChanged(closestPreset);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: _dialAnimation,
      builder: (context, child) {
        return Container(
          height: 120,
          width: double.infinity,
          child: Stack(
            children: [
              // Curved zoom dial background
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: CustomPaint(
                  size: Size(MediaQuery.of(context).size.width, 120),
                  painter: ZoomDialPainter(
                    currentZoom: widget.currentZoom,
                    minZoom: widget.minZoom,
                    maxZoom: widget.maxZoom,
                    presetZooms: widget.presetZooms,
                    isDragging: _isDragging,
                    animationValue: _dialAnimation.value,
                  ),
                ),
              ),
              
              // Gesture detector for dragging
              Positioned(
                bottom: 20,
                left: 0,
                right: 0,
                height: 80,
                child: GestureDetector(
                  onPanStart: _onPanStart,
                  onPanUpdate: _onPanUpdate,
                  onPanEnd: _onPanEnd,
                  child: Container(
                    color: Colors.transparent,
                  ),
                ),
              ),
              
              // Current zoom value display
              Positioned(
                bottom: 30,
                left: 0,
                right: 0,
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.7),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${widget.currentZoom.toStringAsFixed(1)}×',
                      style: const TextStyle(
                        color: Colors.yellow,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class ZoomDialPainter extends CustomPainter {
  final double currentZoom;
  final double minZoom;
  final double maxZoom;
  final List<double> presetZooms;
  final bool isDragging;
  final double animationValue;

  ZoomDialPainter({
    required this.currentZoom,
    required this.minZoom,
    required this.maxZoom,
    required this.presetZooms,
    required this.isDragging,
    required this.animationValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height);
    final radius = size.width * 0.4;
    
    // Draw the curved dial background
    _drawDialBackground(canvas, center, radius, size);
    
    // Draw zoom scale marks
    _drawZoomMarks(canvas, center, radius, size);
    
    // Draw preset zoom indicators
    _drawPresetIndicators(canvas, center, radius, size);
    
    // Draw current zoom indicator
    _drawCurrentZoomIndicator(canvas, center, radius, size);
  }

  void _drawDialBackground(Canvas canvas, Offset center, double radius, Size size) {
    final paint = Paint()
      ..color = Colors.black.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    // Draw curved background arc
    final rect = Rect.fromCircle(center: center, radius: radius);
    canvas.drawArc(
      rect,
      -math.pi * 0.7, // Start angle
      math.pi * 1.4,  // Sweep angle
      false,
      paint,
    );
  }

  void _drawZoomMarks(Canvas canvas, Offset center, double radius, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.5)
      ..strokeWidth = 1;

    const int totalMarks = 20;
    const double startAngle = -math.pi * 0.7;
    const double endAngle = math.pi * 0.7;
    const double angleStep = (endAngle - startAngle) / totalMarks;

    for (int i = 0; i <= totalMarks; i++) {
      final angle = startAngle + (i * angleStep);
      final isMainMark = i % 5 == 0;
      final markLength = isMainMark ? 15.0 : 8.0;
      
      final startPoint = Offset(
        center.dx + (radius - markLength) * math.cos(angle),
        center.dy + (radius - markLength) * math.sin(angle),
      );
      
      final endPoint = Offset(
        center.dx + radius * math.cos(angle),
        center.dy + radius * math.sin(angle),
      );
      
      paint.strokeWidth = isMainMark ? 2 : 1;
      canvas.drawLine(startPoint, endPoint, paint);
    }
  }

  void _drawPresetIndicators(Canvas canvas, Offset center, double radius, Size size) {
    for (double preset in presetZooms) {
      final normalizedZoom = (preset - minZoom) / (maxZoom - minZoom);
      final angle = -math.pi * 0.7 + (normalizedZoom * math.pi * 1.4);
      
      // Draw preset indicator
      final indicatorPaint = Paint()
        ..color = Colors.yellow
        ..style = PaintingStyle.fill;
      
      final indicatorCenter = Offset(
        center.dx + (radius - 25) * math.cos(angle),
        center.dy + (radius - 25) * math.sin(angle),
      );
      
      canvas.drawCircle(indicatorCenter, 3, indicatorPaint);
      
      // Draw preset value text
      final textPainter = TextPainter(
        text: TextSpan(
          text: preset == preset.roundToDouble() 
              ? '${preset.round()}' 
              : preset.toStringAsFixed(1),
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      
      textPainter.layout();
      
      final textCenter = Offset(
        center.dx + (radius - 45) * math.cos(angle) - textPainter.width / 2,
        center.dy + (radius - 45) * math.sin(angle) - textPainter.height / 2,
      );
      
      textPainter.paint(canvas, textCenter);
    }
  }

  void _drawCurrentZoomIndicator(Canvas canvas, Offset center, double radius, Size size) {
    final normalizedZoom = (currentZoom - minZoom) / (maxZoom - minZoom);
    final angle = -math.pi * 0.7 + (normalizedZoom * math.pi * 1.4);
    
    // Draw current zoom line indicator
    final indicatorPaint = Paint()
      ..color = Colors.yellow
      ..strokeWidth = 3
      ..strokeCap = StrokeCap.round;
    
    final startPoint = Offset(
      center.dx + (radius - 30) * math.cos(angle),
      center.dy + (radius - 30) * math.sin(angle),
    );
    
    final endPoint = Offset(
      center.dx + (radius - 5) * math.cos(angle),
      center.dy + (radius - 5) * math.sin(angle),
    );
    
    canvas.drawLine(startPoint, endPoint, indicatorPaint);
    
    // Draw current zoom dot
    final dotPaint = Paint()
      ..color = Colors.yellow
      ..style = PaintingStyle.fill;
    
    final dotCenter = Offset(
      center.dx + (radius - 15) * math.cos(angle),
      center.dy + (radius - 15) * math.sin(angle),
    );
    
    canvas.drawCircle(dotCenter, 4, dotPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is ZoomDialPainter &&
        (oldDelegate.currentZoom != currentZoom ||
         oldDelegate.isDragging != isDragging ||
         oldDelegate.animationValue != animationValue);
  }
}
