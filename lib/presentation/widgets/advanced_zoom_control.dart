import 'package:flutter/material.dart';
import 'dart:math' as math;

class AdvancedZoomControl extends StatefulWidget {
  final double currentZoom;
  final double minZoom;
  final double maxZoom;
  final Function(double) onZoomChanged;
  final List<double> presetZooms;
  final bool isVisible;

  const AdvancedZoomControl({
    super.key,
    required this.currentZoom,
    required this.minZoom,
    required this.maxZoom,
    required this.onZoomChanged,
    this.presetZooms = const [1.0, 2.0, 5.0],
    this.isVisible = true,
  });

  @override
  State<AdvancedZoomControl> createState() => _AdvancedZoomControlState();
}

class _AdvancedZoomControlState extends State<AdvancedZoomControl>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _scaleController;
  late AnimationController _sliderController;
  
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _sliderOpacityAnimation;
  
  bool _showSlider = false;
  int _selectedPresetIndex = 0;
  double _sliderValue = 1.0;

  @override
  void initState() {
    super.initState();
    
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _sliderController = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: this,
    );
    
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.easeInOut,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));
    
    _sliderOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _sliderController,
      curve: Curves.easeInOut,
    ));
    
    _updateSelectedPreset();
    _sliderValue = widget.currentZoom;
  }

  @override
  void didUpdateWidget(AdvancedZoomControl oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentZoom != widget.currentZoom) {
      _updateSelectedPreset();
      _sliderValue = widget.currentZoom;
    }
  }

  void _updateSelectedPreset() {
    // Find the closest preset zoom level
    double minDifference = double.infinity;
    int closestIndex = 0;
    
    for (int i = 0; i < widget.presetZooms.length; i++) {
      double difference = (widget.presetZooms[i] - widget.currentZoom).abs();
      if (difference < minDifference) {
        minDifference = difference;
        closestIndex = i;
      }
    }
    
    if (minDifference < 0.1) { // Close enough to a preset
      _selectedPresetIndex = closestIndex;
    } else {
      _selectedPresetIndex = -1; // Custom zoom level
    }
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _scaleController.dispose();
    _sliderController.dispose();
    super.dispose();
  }

  void _onPresetTap(int index) {
    if (_selectedPresetIndex == index) {
      // Toggle slider visibility
      setState(() {
        _showSlider = !_showSlider;
      });
      
      if (_showSlider) {
        _sliderController.forward();
      } else {
        _sliderController.reverse();
      }
    } else {
      // Change to new zoom level
      setState(() {
        _selectedPresetIndex = index;
        _showSlider = false;
      });
      
      _sliderController.reverse();
      _rotationController.forward().then((_) {
        _rotationController.reverse();
      });
      
      _scaleController.forward().then((_) {
        _scaleController.reverse();
      });
      
      widget.onZoomChanged(widget.presetZooms[index]);
    }
  }

  void _onSliderChanged(double value) {
    setState(() {
      _sliderValue = value;
    });
    widget.onZoomChanged(value);
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible) {
      return const SizedBox.shrink();
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Zoom slider (appears when preset is tapped)
        AnimatedBuilder(
          animation: _sliderOpacityAnimation,
          builder: (context, child) {
            return Opacity(
              opacity: _sliderOpacityAnimation.value,
              child: Transform.scale(
                scale: _sliderOpacityAnimation.value,
                child: _showSlider ? _buildZoomSlider() : const SizedBox.shrink(),
              ),
            );
          },
        ),
        
        if (_showSlider) const SizedBox(height: 16),
        
        // Preset zoom buttons
        _buildPresetButtons(),
      ],
    );
  }

  Widget _buildZoomSlider() {
    return Container(
      width: 200,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '${_sliderValue.toStringAsFixed(1)}x',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: Colors.white,
              inactiveTrackColor: Colors.white.withOpacity(0.3),
              thumbColor: Colors.white,
              thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
              overlayShape: const RoundSliderOverlayShape(overlayRadius: 16),
              trackHeight: 2,
            ),
            child: Slider(
              value: _sliderValue.clamp(widget.minZoom, widget.maxZoom),
              min: widget.minZoom,
              max: widget.maxZoom,
              onChanged: _onSliderChanged,
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${widget.minZoom.toStringAsFixed(1)}x',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 12,
                ),
              ),
              Text(
                '${widget.maxZoom.toStringAsFixed(1)}x',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPresetButtons() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.6),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: widget.presetZooms.asMap().entries.map((entry) {
          int index = entry.key;
          double zoom = entry.value;
          bool isSelected = _selectedPresetIndex == index;
          bool isCustomZoom = _selectedPresetIndex == -1 && 
                             (widget.currentZoom - zoom).abs() < 0.1;
          
          return AnimatedBuilder(
            animation: Listenable.merge([_rotationAnimation, _scaleAnimation]),
            builder: (context, child) {
              double rotation = isSelected ? _rotationAnimation.value * 2 * math.pi : 0;
              double scale = isSelected ? _scaleAnimation.value : 1.0;
              
              return Transform.rotate(
                angle: rotation,
                child: Transform.scale(
                  scale: scale,
                  child: GestureDetector(
                    onTap: () => _onPresetTap(index),
                    child: Container(
                      margin: const EdgeInsets.symmetric(horizontal: 2),
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: (isSelected || isCustomZoom) 
                            ? Colors.white.withOpacity(0.9)
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: (isSelected || isCustomZoom)
                              ? Colors.white
                              : Colors.white.withOpacity(0.5),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            '${zoom.toStringAsFixed(zoom == zoom.roundToDouble() ? 0 : 1)}x',
                            style: TextStyle(
                              color: (isSelected || isCustomZoom) 
                                  ? Colors.black 
                                  : Colors.white,
                              fontSize: 14,
                              fontWeight: (isSelected || isCustomZoom) 
                                  ? FontWeight.bold 
                                  : FontWeight.normal,
                            ),
                          ),
                          if (isSelected && _showSlider) ...[
                            const SizedBox(width: 4),
                            Icon(
                              Icons.tune,
                              size: 14,
                              color: Colors.black,
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          );
        }).toList(),
      ),
    );
  }
}

class ZoomIndicator extends StatefulWidget {
  final double currentZoom;
  final bool isVisible;

  const ZoomIndicator({
    super.key,
    required this.currentZoom,
    this.isVisible = true,
  });

  @override
  State<ZoomIndicator> createState() => _ZoomIndicatorState();
}

class _ZoomIndicatorState extends State<ZoomIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: const Interval(0.5, 1.0, curve: Curves.easeOut),
    ));
  }

  @override
  void didUpdateWidget(ZoomIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentZoom != widget.currentZoom) {
      _fadeController.reset();
      _fadeController.forward();
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.7),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.white.withOpacity(0.3)),
            ),
            child: Text(
              '${widget.currentZoom.toStringAsFixed(1)}x',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );
      },
    );
  }
}
