
import 'package:get_it/get_it.dart';
import '../data/datasources/camera_data_source.dart';
import '../data/datasources/location_data_source.dart';
import '../data/repositories/camera_repository_impl.dart';
import '../domain/repositories/camera_repository.dart';
import '../domain/usecases/get_avalable_cameras.dart';

final sl = GetIt.instance;

Future<void> init() async {
  // Datasources
  sl.registerLazySingleton(() => CameraDatasource());
  sl.registerLazySingleton(() => LocationDatasource());

  // Repositories
  sl.registerLazySingleton<CameraRepository>(() => CameraRepositoryImpl(
        sl<CameraDatasource>(),
        sl<LocationDatasource>(),
        sl<CameraDatasource>(),
      ));

  // Usecases
  sl.registerLazySingleton(() => GetAvailableCameras(sl()));
}
