// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyA3Ccwfn94Zzo8T6XPAlpnup9l4r-RXBG8',
    appId: '1:248396723437:web:056eaab24bddf203fe88e4',
    messagingSenderId: '248396723437',
    projectId: 'gpsmap-e933b',
    authDomain: 'gpsmap-e933b.firebaseapp.com',
    storageBucket: 'gpsmap-e933b.firebasestorage.app',
    measurementId: 'G-RFV6V3P5Y1',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBcQOBPWag9Qpg-RP0oq0mKF0XoYh3GUeo',
    appId: '1:248396723437:android:ab99520fe72dc75dfe88e4',
    messagingSenderId: '248396723437',
    projectId: 'gpsmap-e933b',
    storageBucket: 'gpsmap-e933b.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDsirr2RIWYKv2THVOvDK4olSHvkGuA7Q4',
    appId: '1:248396723437:ios:98c05cfb99bb94a5fe88e4',
    messagingSenderId: '248396723437',
    projectId: 'gpsmap-e933b',
    storageBucket: 'gpsmap-e933b.firebasestorage.app',
    iosBundleId: 'com.example.gpsmapcamera.RunnerTests',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDsirr2RIWYKv2THVOvDK4olSHvkGuA7Q4',
    appId: '1:248396723437:ios:cc5b8942e5dcb38ffe88e4',
    messagingSenderId: '248396723437',
    projectId: 'gpsmap-e933b',
    storageBucket: 'gpsmap-e933b.firebasestorage.app',
    iosBundleId: 'com.example.gpsmapcamera',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyA3Ccwfn94Zzo8T6XPAlpnup9l4r-RXBG8',
    appId: '1:248396723437:web:54974ddd3408badcfe88e4',
    messagingSenderId: '248396723437',
    projectId: 'gpsmap-e933b',
    authDomain: 'gpsmap-e933b.firebaseapp.com',
    storageBucket: 'gpsmap-e933b.firebasestorage.app',
    measurementId: 'G-C6ZF0VVVKQ',
  );
}
