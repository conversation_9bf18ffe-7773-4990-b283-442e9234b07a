## Flutter wrapper
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }

## Razorpay ProGuard Rules
-keep class com.razorpay.** { *; }
-keep class com.razorpay.AnalyticsEvent { *; }
-keep class proguard.annotation.Keep
-keep class proguard.annotation.KeepClassMembers
-dontwarn com.razorpay.**
-dontwarn proguard.annotation.**

## Keep all classes with @Keep annotation
-keep @proguard.annotation.Keep class * { *; }
-keepclassmembers class * {
    @proguard.annotation.Keep *;
}

## Additional Razorpay rules
-keepattributes Signature
-keepattributes *Annotation*
-keep class com.google.gson.** { *; }
-keep class org.json.** { *; }

## Google Mobile Ads
-keep class com.google.android.gms.ads.** { *; }
-dontwarn com.google.android.gms.ads.**

## Camera and Location
-keep class androidx.camera.** { *; }
-keep class com.google.android.gms.location.** { *; }
-dontwarn androidx.camera.**
-dontwarn com.google.android.gms.location.**

## Google Play Core
-keep class com.google.android.play.core.** { *; }
-dontwarn com.google.android.play.core.**

## Flutter specific
-keep class io.flutter.embedding.** { *; }
-dontwarn io.flutter.embedding.**

## Additional ProGuard rules for release builds
-keepattributes SourceFile,LineNumberTable
-renamesourcefileattribute SourceFile
