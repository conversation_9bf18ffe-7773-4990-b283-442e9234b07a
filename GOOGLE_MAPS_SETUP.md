# Google Maps API Setup Guide

## 🗺️ Setting up Google Maps API for GPS Map Camera App

### Step 1: Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Note down your project ID

### Step 2: Enable Required APIs

Enable the following APIs in your Google Cloud project:
- **Maps SDK for Android**
- **Maps SDK for iOS** 
- **Geocoding API** (for address lookup)
- **Places API** (optional, for location search)

### Step 3: Create API Keys

1. Go to **Credentials** in Google Cloud Console
2. Click **Create Credentials** → **API Key**
3. Create separate keys for Android and iOS (recommended)

### Step 4: Restrict API Keys (Important for Security)

#### For Android API Key:
1. Click on your Android API key
2. Under **Application restrictions**, select **Android apps**
3. Add your package name: `com.example.gpsmapcamera`
4. Add your SHA-1 certificate fingerprint

#### For iOS API Key:
1. Click on your iOS API key  
2. Under **Application restrictions**, select **iOS apps**
3. Add your bundle identifier: `com.example.gpsmapcamera`

### Step 5: Update Configuration Files

#### Android Configuration:
Update `android/app/src/main/AndroidManifest.xml`:
```xml
<meta-data
    android:name="com.google.android.geo.API_KEY"
    android:value="YOUR_ANDROID_API_KEY_HERE"/>
```

#### iOS Configuration:
Update `ios/Runner/AppDelegate.swift`:
```swift
GMSServices.provideAPIKey("YOUR_IOS_API_KEY_HERE")
```

#### App Configuration:
Update `lib/config/api_keys.dart`:
```dart
static const String googleMapsAndroid = 'YOUR_ANDROID_API_KEY';
static const String googleMapsIOS = 'YOUR_IOS_API_KEY';
```

### Step 6: Get SHA-1 Certificate Fingerprint (Android)

#### For Debug Build:
```bash
keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
```

#### For Release Build:
```bash
keytool -list -v -keystore your-release-key.keystore -alias your-key-alias
```

### Step 7: Test the Integration

1. Replace placeholder API keys with your actual keys
2. Run the app: `flutter run`
3. Navigate to Land Measuring section
4. Switch to Map view
5. Verify that Google Maps loads correctly

### 🔒 Security Best Practices

1. **Never commit real API keys to public repositories**
2. **Use environment variables in production**
3. **Restrict API keys to specific apps and APIs**
4. **Monitor API usage in Google Cloud Console**
5. **Set up billing alerts to avoid unexpected charges**

### 📱 Testing Checklist

- [ ] Google Maps loads in Land Measuring section
- [ ] Satellite and normal map views work
- [ ] Tap-to-add points functionality works
- [ ] GPS location marker appears
- [ ] Zoom and pan controls work
- [ ] Map overlays (polygons/polylines) display correctly

### 🚨 Troubleshooting

#### Map not loading:
- Check API key is correct
- Verify APIs are enabled
- Check app restrictions match your package/bundle ID
- Check SHA-1 fingerprint is added (Android)

#### "This page can't load Google Maps correctly":
- API key restrictions are too strict
- Billing not enabled on Google Cloud project
- API quotas exceeded

#### Permission errors:
- Location permissions not granted
- API key doesn't have required permissions

### 💰 Pricing Information

Google Maps API has a free tier with generous limits:
- **Maps SDK**: $7 per 1,000 requests (after free tier)
- **Geocoding API**: $5 per 1,000 requests (after free tier)
- **Free tier**: $200 credit per month

Monitor usage in Google Cloud Console to avoid unexpected charges.

### 📞 Support

For Google Maps API issues:
- [Google Maps Platform Documentation](https://developers.google.com/maps/documentation)
- [Google Cloud Support](https://cloud.google.com/support)
- [Stack Overflow - google-maps](https://stackoverflow.com/questions/tagged/google-maps)
